//
//  HistoryAccessibilityUITests.swift
//  RockerSTTUITests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest

class HistoryAccessibilityUITests: XCTestCase {
    
    // MARK: - Properties
    
    var app: XCUIApplication!
    
    // MARK: - Setup & Teardown
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // Configure for accessibility testing on iPhone 16
        app.launchArguments = ["UI_TESTING", "ACCESSIBILITY_TESTING", "IPHONE_16_TESTING"]
        app.launchEnvironment = [
            "ENABLE_TEST_DATA": "true",
            "DISABLE_ANIMATIONS": "true",
            "MOCK_SPEECH_RECOGNITION": "true"
        ]
        
        app.launch()
        
        // Navigate to History tab
        navigateToHistoryTab()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - Helper Methods
    
    private func navigateToHistoryTab() {
        let historyTab = app.tabBars.buttons["History"]
        XCTAssertTrue(historyTab.waitForExistence(timeout: 5))
        historyTab.tap()
        
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.waitForExistence(timeout: 5))
    }
    
    private func createTestSession() {
        app.tabBars.buttons["Record"].tap()
        let recordButton = app.buttons["Start Recording"]
        recordButton.tap()
        Thread.sleep(forTimeInterval: 2)
        app.buttons["Stop Recording"].tap()
        Thread.sleep(forTimeInterval: 1)
        navigateToHistoryTab()
    }
    
    // MARK: - VoiceOver Navigation Tests
    
    func testVoiceOverNavigationThroughTabs() throws {
        // Given - VoiceOver is enabled (simulated through accessibility properties)
        
        // When - Navigate through tab bar with VoiceOver
        let historyTab = app.tabBars.buttons["History"]
        XCTAssertTrue(historyTab.isAccessibilityElement)
        XCTAssertEqual(historyTab.accessibilityLabel, "History")
        XCTAssertEqual(historyTab.accessibilityHint, "View transcription history")
        XCTAssertTrue(historyTab.accessibilityTraits.contains(.button))
        
        // Test other tabs
        let recordTab = app.tabBars.buttons["Record"]
        XCTAssertTrue(recordTab.isAccessibilityElement)
        XCTAssertEqual(recordTab.accessibilityLabel, "Record")
        
        let settingsTab = app.tabBars.buttons["Settings"]
        XCTAssertTrue(settingsTab.isAccessibilityElement)
        XCTAssertEqual(settingsTab.accessibilityLabel, "Settings")
    }
    
    func testVoiceOverHistoryTabNavigation() throws {
        // Given - History view is displayed
        
        // When - Check tab buttons accessibility
        let recentsTab = app.buttons["Recents"]
        XCTAssertTrue(recentsTab.isAccessibilityElement)
        XCTAssertEqual(recentsTab.accessibilityLabel, "Recents")
        XCTAssertEqual(recentsTab.accessibilityHint, "View recent transcription sessions")
        XCTAssertTrue(recentsTab.accessibilityTraits.contains(.button))
        
        let favoritesTab = app.buttons["Favorites"]
        XCTAssertTrue(favoritesTab.isAccessibilityElement)
        XCTAssertEqual(favoritesTab.accessibilityLabel, "Favorites")
        XCTAssertEqual(favoritesTab.accessibilityHint, "View favorite transcription sessions")
        
        let savedTab = app.buttons["Saved"]
        XCTAssertTrue(savedTab.isAccessibilityElement)
        XCTAssertEqual(savedTab.accessibilityLabel, "Saved")
        XCTAssertEqual(savedTab.accessibilityHint, "View saved transcription sessions")
    }
    
    func testVoiceOverSearchBarAccessibility() throws {
        // Given - History view is displayed
        
        // When - Check search bar accessibility
        let searchBar = app.searchFields["Search history"]
        XCTAssertTrue(searchBar.isAccessibilityElement)
        XCTAssertEqual(searchBar.accessibilityLabel, "Search history")
        XCTAssertEqual(searchBar.accessibilityHint, "Search through your transcription history")
        XCTAssertTrue(searchBar.accessibilityTraits.contains(.searchField))
        
        // Test search interaction
        searchBar.tap()
        XCTAssertTrue(searchBar.hasKeyboardFocus)
        
        // Type and verify accessibility feedback
        searchBar.typeText("test")
        XCTAssertEqual(searchBar.value as? String, "test")
    }
    
    // MARK: - Session Card Accessibility Tests
    
    func testSessionCardAccessibilityLabels() throws {
        // Given - Create a test session
        createTestSession()
        
        // When - Check session card accessibility
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        XCTAssertTrue(sessionCard.isAccessibilityElement)
        
        // Verify accessibility label includes key information
        let accessibilityLabel = sessionCard.accessibilityLabel!
        XCTAssertTrue(accessibilityLabel.contains("Session"))
        XCTAssertTrue(accessibilityLabel.contains("Duration"))
        XCTAssertTrue(accessibilityLabel.contains("Language"))
        
        // Verify accessibility traits
        XCTAssertTrue(sessionCard.accessibilityTraits.contains(.button))
        
        // Verify accessibility hint
        XCTAssertNotNil(sessionCard.accessibilityHint)
        XCTAssertTrue(sessionCard.accessibilityHint!.contains("Double tap to view details"))
    }
    
    func testSessionCardActionButtonsAccessibility() throws {
        // Given - Create a test session
        createTestSession()
        
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        // When - Check favorite button accessibility
        let favoriteButton = sessionCard.buttons["Favorite"]
        XCTAssertTrue(favoriteButton.isAccessibilityElement)
        XCTAssertEqual(favoriteButton.accessibilityLabel, "Favorite")
        XCTAssertEqual(favoriteButton.accessibilityHint, "Add to favorites")
        XCTAssertTrue(favoriteButton.accessibilityTraits.contains(.button))
        
        // Test favorite action
        favoriteButton.tap()
        
        // Verify state change is announced
        XCTAssertEqual(favoriteButton.accessibilityLabel, "Unfavorite")
        XCTAssertEqual(favoriteButton.accessibilityHint, "Remove from favorites")
        
        // When - Check save button accessibility
        let saveButton = sessionCard.buttons["Save"]
        XCTAssertTrue(saveButton.isAccessibilityElement)
        XCTAssertEqual(saveButton.accessibilityLabel, "Save")
        XCTAssertEqual(saveButton.accessibilityHint, "Save session")
        
        // When - Check share button accessibility
        let shareButton = sessionCard.buttons["Share"]
        XCTAssertTrue(shareButton.isAccessibilityElement)
        XCTAssertEqual(shareButton.accessibilityLabel, "Share")
        XCTAssertEqual(shareButton.accessibilityHint, "Share session content")
    }
    
    func testSessionCardSwipeActionsAccessibility() throws {
        // Given - Create a test session
        createTestSession()
        
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        // When - Perform swipe to reveal actions
        sessionCard.swipeLeft()
        
        // Then - Check delete button accessibility
        let deleteButton = app.buttons["Delete"]
        XCTAssertTrue(deleteButton.waitForExistence(timeout: 3))
        XCTAssertTrue(deleteButton.isAccessibilityElement)
        XCTAssertEqual(deleteButton.accessibilityLabel, "Delete")
        XCTAssertEqual(deleteButton.accessibilityHint, "Delete this session permanently")
        XCTAssertTrue(deleteButton.accessibilityTraits.contains(.button))
        
        // Verify destructive trait for delete action
        XCTAssertTrue(deleteButton.accessibilityTraits.contains(.destructive))
    }
    
    // MARK: - Dynamic Type Support Tests
    
    func testDynamicTypeSupport() throws {
        // Given - Create a test session
        createTestSession()
        
        // When - Check that text scales with Dynamic Type
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.waitForExistence(timeout: 5))
        
        // Verify text elements support Dynamic Type
        let titleLabel = sessionCard.staticTexts.firstMatch
        XCTAssertTrue(titleLabel.exists)
        
        // Check that font is scalable (this would require specific font testing)
        // In a real test, you would change system font size and verify scaling
        XCTAssertTrue(titleLabel.isAccessibilityElement)
    }
    
    func testLargeTextAccessibility() throws {
        // Given - Simulate large text setting
        // Note: In real testing, you would set accessibility text size
        
        // When - Check that UI accommodates large text
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.exists)
        
        // Verify navigation elements are still accessible
        let searchBar = app.searchFields["Search history"]
        XCTAssertTrue(searchBar.exists)
        XCTAssertTrue(searchBar.isAccessibilityElement)
        
        // Verify tab buttons are still accessible
        let recentsTab = app.buttons["Recents"]
        XCTAssertTrue(recentsTab.exists)
        XCTAssertTrue(recentsTab.isAccessibilityElement)
    }
    
    // MARK: - Reduced Motion Support Tests
    
    func testReducedMotionSupport() throws {
        // Given - Reduced motion is enabled (simulated)
        
        // When - Perform navigation that typically has animations
        let favoritesTab = app.buttons["Favorites"]
        favoritesTab.tap()
        
        // Then - Content should change without relying on motion
        let favoritesContent = app.staticTexts["No favorite sessions yet"]
        XCTAssertTrue(favoritesContent.waitForExistence(timeout: 2))
        
        // Switch back to recents
        let recentsTab = app.buttons["Recents"]
        recentsTab.tap()
        
        // Verify immediate content change
        XCTAssertTrue(recentsTab.isSelected)
    }
    
    // MARK: - Voice Control Support Tests
    
    func testVoiceControlSupport() throws {
        // Given - Voice Control is enabled (simulated)
        
        // When - Check that elements have voice control names
        let historyTab = app.tabBars.buttons["History"]
        XCTAssertNotNil(historyTab.accessibilityLabel)
        XCTAssertFalse(historyTab.accessibilityLabel!.isEmpty)
        
        // Check search bar
        let searchBar = app.searchFields["Search history"]
        XCTAssertNotNil(searchBar.accessibilityLabel)
        XCTAssertEqual(searchBar.accessibilityLabel, "Search history")
        
        // Check tab buttons
        let recentsTab = app.buttons["Recents"]
        XCTAssertNotNil(recentsTab.accessibilityLabel)
        XCTAssertEqual(recentsTab.accessibilityLabel, "Recents")
    }
    
    // MARK: - Color and Contrast Accessibility Tests
    
    func testHighContrastSupport() throws {
        // Given - High contrast mode is enabled (simulated)
        
        // When - Check that UI elements are still visible
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.exists)
        
        // Verify tab bar elements are distinguishable
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.exists)
        
        let historyTab = app.tabBars.buttons["History"]
        XCTAssertTrue(historyTab.exists)
        XCTAssertTrue(historyTab.isSelected)
        
        // Check that selected state is clearly indicated
        XCTAssertTrue(historyTab.accessibilityTraits.contains(.selected))
    }
    
    func testColorBlindnessSupport() throws {
        // Given - Color should not be the only way to convey information
        
        // When - Check favorite button states
        createTestSession()
        
        let sessionCard = app.cells.firstMatch
        let favoriteButton = sessionCard.buttons["Favorite"]
        
        // Then - State should be indicated by more than just color
        XCTAssertEqual(favoriteButton.accessibilityLabel, "Favorite")
        
        favoriteButton.tap()
        
        // After favoriting, label should change
        XCTAssertEqual(favoriteButton.accessibilityLabel, "Unfavorite")
        
        // Icon should also change (not just color)
        let favoriteIcon = sessionCard.images["heart.fill"]
        XCTAssertTrue(favoriteIcon.waitForExistence(timeout: 2))
    }
    
    // MARK: - Comprehensive Accessibility Journey Tests
    
    func testCompleteAccessibilityUserJourney() throws {
        // Given - User relies on accessibility features
        
        // Step 1: Navigate to History tab
        let historyTab = app.tabBars.buttons["History"]
        XCTAssertTrue(historyTab.isAccessibilityElement)
        historyTab.tap()
        
        // Step 2: Create a session for testing
        createTestSession()
        
        // Step 3: Navigate through history tabs
        let favoritesTab = app.buttons["Favorites"]
        XCTAssertTrue(favoritesTab.isAccessibilityElement)
        favoritesTab.tap()
        
        let savedTab = app.buttons["Saved"]
        XCTAssertTrue(savedTab.isAccessibilityElement)
        savedTab.tap()
        
        let recentsTab = app.buttons["Recents"]
        XCTAssertTrue(recentsTab.isAccessibilityElement)
        recentsTab.tap()
        
        // Step 4: Interact with session card
        let sessionCard = app.cells.firstMatch
        XCTAssertTrue(sessionCard.isAccessibilityElement)
        
        let favoriteButton = sessionCard.buttons["Favorite"]
        XCTAssertTrue(favoriteButton.isAccessibilityElement)
        favoriteButton.tap()
        
        // Step 5: Use search functionality
        let searchBar = app.searchFields["Search history"]
        XCTAssertTrue(searchBar.isAccessibilityElement)
        searchBar.tap()
        searchBar.typeText("session")
        
        // Step 6: Clear search
        let clearButton = searchBar.buttons["Clear text"]
        if clearButton.exists {
            XCTAssertTrue(clearButton.isAccessibilityElement)
            clearButton.tap()
        }
        
        // Journey completed with full accessibility support
        XCTAssertTrue(app.navigationBars["History"].exists)
    }
    
    func testAccessibilityAnnouncementsForStateChanges() throws {
        // Given - Create a test session
        createTestSession()
        
        let sessionCard = app.cells.firstMatch
        let favoriteButton = sessionCard.buttons["Favorite"]
        
        // When - Toggle favorite state
        XCTAssertEqual(favoriteButton.accessibilityLabel, "Favorite")
        favoriteButton.tap()
        
        // Then - Accessibility label should update
        XCTAssertEqual(favoriteButton.accessibilityLabel, "Unfavorite")
        
        // When - Toggle save state
        let saveButton = sessionCard.buttons["Save"]
        XCTAssertEqual(saveButton.accessibilityLabel, "Save")
        saveButton.tap()
        
        // Then - Accessibility label should update
        XCTAssertEqual(saveButton.accessibilityLabel, "Unsave")
    }
    
    // MARK: - iPhone 16 Accessibility Tests
    
    func testiPhone16AccessibilityOptimization() throws {
        // Given - App is running on iPhone 16 with accessibility features
        
        // When - Check that UI elements are properly sized for accessibility
        let historyTitle = app.navigationBars["History"]
        XCTAssertTrue(historyTitle.exists)
        
        // Verify minimum touch target sizes (44x44 points)
        let tabButtons = app.tabBars.buttons
        for button in tabButtons.allElementsBoundByIndex {
            XCTAssertGreaterThanOrEqual(button.frame.width, 44)
            XCTAssertGreaterThanOrEqual(button.frame.height, 44)
        }
        
        // Verify search bar is accessible
        let searchBar = app.searchFields["Search history"]
        XCTAssertGreaterThanOrEqual(searchBar.frame.height, 44)
        
        // Verify session cards have adequate spacing
        if app.cells.count > 1 {
            let firstCard = app.cells.element(boundBy: 0)
            let secondCard = app.cells.element(boundBy: 1)
            
            let spacing = secondCard.frame.minY - firstCard.frame.maxY
            XCTAssertGreaterThanOrEqual(spacing, 8) // Minimum spacing for accessibility
        }
    }
}
