//
//  LanguageDetectorTests.swift
//  RockerSTTTests
//
//  Created by <PERSON><PERSON> on 2025/7/18.
//

import XCTest
@testable import RockerSTT

final class LanguageDetectorTests: XCTestCase {
    
    var languageDetector: LanguageDetector!
    
    override func setUp() {
        super.setUp()
        languageDetector = LanguageDetector()
    }
    
    override func tearDown() {
        languageDetector = nil
        super.tearDown()
    }
    
    // MARK: - shouldApplyMerging Tests
    
    func testShouldApplyMerging_English_ReturnsTrue() {
        // Given
        let language = RecognizedLanguage.english
        
        // When
        let result = languageDetector.shouldApplyMerging(for: language)
        
        // Then
        XCTAssertTrue(result, "English should apply word merging due to space-based nature")
    }
    
    func testShouldApplyMerging_Japanese_ReturnsTrue() {
        // Given
        let language = RecognizedLanguage.japanese
        
        // When
        let result = languageDetector.shouldApplyMerging(for: language)
        
        // Then
        XCTAssertTrue(result, "Japanese (romanized) should apply word merging")
    }
    
    func testShouldApplyMerging_Chinese_ReturnsFalse() {
        // Given
        let language = RecognizedLanguage.chinese
        
        // When
        let result = languageDetector.shouldApplyMerging(for: language)
        
        // Then
        XCTAssertFalse(result, "Chinese should not apply word merging as it doesn't use spaces")
    }
    
    func testShouldApplyMerging_Cantonese_ReturnsFalse() {
        // Given
        let language = RecognizedLanguage.cantonese
        
        // When
        let result = languageDetector.shouldApplyMerging(for: language)
        
        // Then
        XCTAssertFalse(result, "Cantonese should not apply word merging as it doesn't use spaces")
    }
    
    func testShouldApplyMerging_Unknown_ReturnsTrue() {
        // Given
        let language = RecognizedLanguage.unknown
        
        // When
        let result = languageDetector.shouldApplyMerging(for: language)
        
        // Then
        XCTAssertTrue(result, "Unknown language should default to applying merging for safety")
    }
    
    // MARK: - detectLanguageFromText Tests
    
    func testDetectLanguageFromText_EnglishText_ReturnsEnglish() {
        // Given
        let englishText = "Hello world, this is a test sentence in English."
        
        // When
        let result = languageDetector.detectLanguageFromText(englishText)
        
        // Then
        XCTAssertEqual(result, .english, "Should detect English text correctly")
    }
    
    func testDetectLanguageFromText_ChineseText_ReturnsChinese() {
        // Given
        let chineseText = "你好世界，这是一个中文测试句子。"
        
        // When
        let result = languageDetector.detectLanguageFromText(chineseText)
        
        // Then
        XCTAssertEqual(result, .chinese, "Should detect Chinese text correctly")
    }
    
    func testDetectLanguageFromText_JapaneseText_ReturnsJapanese() {
        // Given
        let japaneseText = "こんにちは世界、これは日本語のテスト文です。"
        
        // When
        let result = languageDetector.detectLanguageFromText(japaneseText)
        
        // Then
        XCTAssertEqual(result, .japanese, "Should detect Japanese text correctly")
    }
    
    func testDetectLanguageFromText_EmptyString_ReturnsUnknown() {
        // Given
        let emptyText = ""
        
        // When
        let result = languageDetector.detectLanguageFromText(emptyText)
        
        // Then
        XCTAssertEqual(result, .unknown, "Empty string should return unknown language")
    }
    
    func testDetectLanguageFromText_WhitespaceOnly_ReturnsUnknown() {
        // Given
        let whitespaceText = "   \n\t  "
        
        // When
        let result = languageDetector.detectLanguageFromText(whitespaceText)
        
        // Then
        XCTAssertEqual(result, .unknown, "Whitespace-only text should return unknown language")
    }
    
    func testDetectLanguageFromText_VeryShortText_ReturnsUnknown() {
        // Given
        let shortText = "Hi"
        
        // When
        let result = languageDetector.detectLanguageFromText(shortText)
        
        // Then
        XCTAssertEqual(result, .unknown, "Very short text should return unknown language")
    }
    
    func testDetectLanguageFromText_MixedLanguageText_ReturnsDetectedDominant() {
        // Given - Text with mixed languages but English dominant
        let mixedText = "Hello world 你好 this is mostly English text with some Chinese words."
        
        // When
        let result = languageDetector.detectLanguageFromText(mixedText)
        
        // Then
        // Note: The exact result may vary based on NaturalLanguage framework behavior
        // We accept either English (dominant) or unknown (mixed) as valid results
        XCTAssertTrue(result == .english || result == .unknown, 
                     "Mixed language text should return either dominant language or unknown")
    }
    
    func testDetectLanguageFromText_NumbersAndPunctuation_ReturnsUnknown() {
        // Given
        let numbersText = "123 456 789 !@# $%^"
        
        // When
        let result = languageDetector.detectLanguageFromText(numbersText)
        
        // Then
        XCTAssertEqual(result, .unknown, "Numbers and punctuation should return unknown language")
    }
    
    // MARK: - Integration Tests for Language-Aware Merging Decisions
    
    func testLanguageAwareMergingDecision_EnglishText() {
        // Given
        let englishText = "This is an English sentence that needs word merging."
        
        // When
        let detectedLanguage = languageDetector.detectLanguageFromText(englishText)
        let shouldMerge = languageDetector.shouldApplyMerging(for: detectedLanguage)
        
        // Then
        XCTAssertEqual(detectedLanguage, .english)
        XCTAssertTrue(shouldMerge, "English text should be detected and merging should be applied")
    }
    
    func testLanguageAwareMergingDecision_ChineseText() {
        // Given
        let chineseText = "这是一个中文句子，不需要单词合并。"
        
        // When
        let detectedLanguage = languageDetector.detectLanguageFromText(chineseText)
        let shouldMerge = languageDetector.shouldApplyMerging(for: detectedLanguage)
        
        // Then
        XCTAssertEqual(detectedLanguage, .chinese)
        XCTAssertFalse(shouldMerge, "Chinese text should be detected and merging should not be applied")
    }
    
    func testLanguageAwareMergingDecision_UnknownText() {
        // Given
        let unknownText = "xyz"
        
        // When
        let detectedLanguage = languageDetector.detectLanguageFromText(unknownText)
        let shouldMerge = languageDetector.shouldApplyMerging(for: detectedLanguage)
        
        // Then
        XCTAssertEqual(detectedLanguage, .unknown)
        XCTAssertTrue(shouldMerge, "Unknown text should default to applying merging for safety")
    }
    
    // MARK: - Edge Cases and Error Handling
    
    func testDetectLanguageFromText_SpecialCharacters() {
        // Given
        let specialText = "Hello! @#$%^&*() 世界"
        
        // When
        let result = languageDetector.detectLanguageFromText(specialText)
        
        // Then
        // Should handle special characters gracefully and still detect language
        XCTAssertTrue(result == .english || result == .chinese || result == .unknown,
                     "Should handle special characters gracefully")
    }
    
    func testDetectLanguageFromText_LongText() {
        // Given
        let longEnglishText = String(repeating: "This is a long English sentence. ", count: 100)
        
        // When
        let result = languageDetector.detectLanguageFromText(longEnglishText)
        
        // Then
        XCTAssertEqual(result, .english, "Should handle long text correctly")
    }
    
    // MARK: - Performance Tests
    
    func testDetectLanguageFromText_Performance() {
        // Given
        let testText = "This is a performance test for language detection functionality."
        
        // When & Then
        measure {
            for _ in 0..<100 {
                _ = languageDetector.detectLanguageFromText(testText)
            }
        }
    }
    
    func testShouldApplyMerging_Performance() {
        // Given
        let languages: [RecognizedLanguage] = [.english, .chinese, .cantonese, .japanese, .unknown]
        
        // When & Then
        measure {
            for _ in 0..<1000 {
                for language in languages {
                    _ = languageDetector.shouldApplyMerging(for: language)
                }
            }
        }
    }
}