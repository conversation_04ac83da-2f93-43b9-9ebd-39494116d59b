//
//  SpeechRecognitionHistoryIntegrationTests.swift
//  RockerSTTTests
//
//  Created by Augment Agent on 2025-07-21.
//

import XCTest
import CoreData
import Combine
@testable import RockerSTT

class SpeechRecognitionHistoryIntegrationTests: XCTestCase {
    
    // MARK: - Properties
    
    var speechViewModel: SpeechRecognitionViewModel!
    var historyStorageService: HistoryStorageService!
    var mockAudioEngine: MockAudioEngineManager!
    var mockWebSocket: MockWebSocketManager!
    var testCoreDataStack: CoreDataStack!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        setupTestEnvironment()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        speechViewModel = nil
        historyStorageService = nil
        mockAudioEngine = nil
        mockWebSocket = nil
        testCoreDataStack = nil
        super.tearDown()
    }
    
    private func setupTestEnvironment() {
        // Setup in-memory Core Data stack
        testCoreDataStack = CoreDataStack.inMemoryStack()
        historyStorageService = HistoryStorageService(coreDataStack: testCoreDataStack)
        
        // Setup mock services
        mockAudioEngine = MockAudioEngineManager()
        mockWebSocket = MockWebSocketManager()
        
        // Setup speech recognition view model
        speechViewModel = SpeechRecognitionViewModel(
            audioEngine: mockAudioEngine,
            webSocketManager: mockWebSocket,
            historyStorageService: historyStorageService
        )
        
        cancellables = Set<AnyCancellable>()
    }
    
    // MARK: - Session Creation Tests
    
    func testAutomaticSessionCreationOnRecordingStop() throws {
        // Given
        let expectation = XCTestExpectation(description: "Session created")
        speechViewModel.startRecording()
        
        // Simulate transcription entries
        let entries = [
            TranscriptionEntry(text: "Hello world", isPartial: false, confidence: 0.95, timestamp: Date()),
            TranscriptionEntry(text: "This is a test", isPartial: false, confidence: 0.92, timestamp: Date()),
            TranscriptionEntry(text: "Recording session", isPartial: false, confidence: 0.88, timestamp: Date())
        ]
        
        speechViewModel.transcriptionEntries = entries
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for async session creation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // Then
            do {
                let sessions = try self.historyStorageService.fetchAllSessions()
                XCTAssertEqual(sessions.count, 1)
                
                let session = sessions.first!
                XCTAssertNotNil(session.id)
                XCTAssertNotNil(session.title)
                XCTAssertNotNil(session.createdAt)
                XCTAssertEqual(session.entries?.count, 3)
                
                expectation.fulfill()
            } catch {
                XCTFail("Failed to fetch sessions: \(error)")
            }
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testSessionMetadataGeneration() throws {
        // Given
        speechViewModel.startRecording()
        speechViewModel.selectedLanguage = "es-ES"
        
        let startTime = Date()
        let entries = [
            TranscriptionEntry(text: "Hola mundo", isPartial: false, confidence: 0.95, timestamp: startTime),
            TranscriptionEntry(text: "Esta es una prueba", isPartial: false, confidence: 0.92, timestamp: startTime.addingTimeInterval(5))
        ]
        speechViewModel.transcriptionEntries = entries
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for session creation
        let expectation = XCTestExpectation(description: "Metadata generated")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                let sessions = try self.historyStorageService.fetchAllSessions()
                let session = sessions.first!
                
                // Then - Verify metadata
                XCTAssertEqual(session.language, "es-ES")
                XCTAssertGreaterThan(session.duration, 0)
                XCTAssertFalse(session.isFavourite)
                XCTAssertFalse(session.isSaved)
                XCTAssertNotNil(session.detectedLanguages)
                XCTAssertTrue(session.detectedLanguages?.contains("es-ES") == true)
                
                expectation.fulfill()
            } catch {
                XCTFail("Failed to verify metadata: \(error)")
            }
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testContentPreviewCreation() throws {
        // Given
        speechViewModel.startRecording()
        
        let longText = "This is a very long transcription that should be truncated for the preview. It contains multiple sentences and should demonstrate how the preview generation works with longer content that exceeds the typical preview length."
        
        let entries = [
            TranscriptionEntry(text: longText, isPartial: false, confidence: 0.95, timestamp: Date())
        ]
        speechViewModel.transcriptionEntries = entries
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for session creation
        let expectation = XCTestExpectation(description: "Preview created")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                let sessions = try self.historyStorageService.fetchAllSessions()
                let session = sessions.first!
                
                // Then - Verify preview
                XCTAssertNotNil(session.contentPreview)
                XCTAssertLessThanOrEqual(session.contentPreview?.count ?? 0, 100) // Preview should be truncated
                XCTAssertTrue(session.contentPreview?.hasPrefix("This is a very long") == true)
                
                expectation.fulfill()
            } catch {
                XCTFail("Failed to verify preview: \(error)")
            }
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - Transcription Workflow Integration Tests
    
    func testRealTimeTranscriptionToHistoryFlow() throws {
        // Given
        let expectation = XCTestExpectation(description: "Real-time flow completed")
        speechViewModel.startRecording()
        
        var sessionCreated = false
        
        // Monitor session creation
        speechViewModel.$isRecording
            .sink { isRecording in
                if !isRecording && !sessionCreated {
                    sessionCreated = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        expectation.fulfill()
                    }
                }
            }
            .store(in: &cancellables)
        
        // When - Simulate real-time transcription
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.mockWebSocket.simulateTranscriptionResponse("Hello")
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.mockWebSocket.simulateTranscriptionResponse("Hello world")
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.mockWebSocket.simulateTranscriptionResponse("Hello world, this is a test")
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            self.speechViewModel.stopRecording()
        }
        
        wait(for: [expectation], timeout: 3.0)
        
        // Then - Verify session was created with correct entries
        let sessions = try historyStorageService.fetchAllSessions()
        XCTAssertEqual(sessions.count, 1)
        
        let session = sessions.first!
        XCTAssertGreaterThan(session.entries?.count ?? 0, 0)
        
        // Verify final transcription is saved
        let entries = session.entries?.allObjects as? [HistoryEntry] ?? []
        let finalEntry = entries.last
        XCTAssertEqual(finalEntry?.text, "Hello world, this is a test")
    }
    
    func testMultipleRecordingSessionsCreation() throws {
        // Given
        let numberOfSessions = 3
        
        for i in 0..<numberOfSessions {
            // When
            speechViewModel.startRecording()
            
            let entries = [
                TranscriptionEntry(text: "Session \(i + 1) content", isPartial: false, confidence: 0.95, timestamp: Date())
            ]
            speechViewModel.transcriptionEntries = entries
            
            speechViewModel.stopRecording()
            
            // Wait for session creation
            Thread.sleep(forTimeInterval: 0.6)
        }
        
        // Then
        let sessions = try historyStorageService.fetchAllSessions()
        XCTAssertEqual(sessions.count, numberOfSessions)
        
        // Verify each session has unique content
        let titles = sessions.compactMap { $0.title }
        XCTAssertEqual(Set(titles).count, numberOfSessions) // All titles should be unique
    }
    
    // MARK: - Error Handling Integration Tests
    
    func testSessionCreationWithEmptyTranscription() throws {
        // Given
        speechViewModel.startRecording()
        speechViewModel.transcriptionEntries = [] // No transcription entries
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for potential session creation
        Thread.sleep(forTimeInterval: 0.6)
        
        // Then - No session should be created for empty transcription
        let sessions = try historyStorageService.fetchAllSessions()
        XCTAssertEqual(sessions.count, 0)
    }
    
    func testSessionCreationWithOnlyPartialEntries() throws {
        // Given
        speechViewModel.startRecording()
        
        let partialEntries = [
            TranscriptionEntry(text: "Partial", isPartial: true, confidence: 0.5, timestamp: Date()),
            TranscriptionEntry(text: "Still partial", isPartial: true, confidence: 0.6, timestamp: Date())
        ]
        speechViewModel.transcriptionEntries = partialEntries
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for session creation
        Thread.sleep(forTimeInterval: 0.6)
        
        // Then - Session should still be created but marked appropriately
        let sessions = try historyStorageService.fetchAllSessions()
        XCTAssertEqual(sessions.count, 1)
        
        let session = sessions.first!
        let entries = session.entries?.allObjects as? [HistoryEntry] ?? []
        XCTAssertTrue(entries.allSatisfy { $0.isPartial })
    }
    
    func testSessionCreationFailureHandling() throws {
        // Given
        speechViewModel.startRecording()
        
        let entries = [
            TranscriptionEntry(text: "Test content", isPartial: false, confidence: 0.95, timestamp: Date())
        ]
        speechViewModel.transcriptionEntries = entries
        
        // Simulate storage failure by corrupting the Core Data stack
        testCoreDataStack = nil
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for error handling
        Thread.sleep(forTimeInterval: 0.6)
        
        // Then - Should handle error gracefully without crashing
        XCTAssertFalse(speechViewModel.isRecording)
        // Error should be logged but not crash the app
    }
    
    // MARK: - Language Detection Integration Tests
    
    func testLanguageDetectionIntegration() throws {
        // Given
        speechViewModel.startRecording()
        speechViewModel.selectedLanguage = "auto" // Auto-detect language
        
        let spanishEntries = [
            TranscriptionEntry(text: "Hola, ¿cómo estás?", isPartial: false, confidence: 0.95, timestamp: Date()),
            TranscriptionEntry(text: "Muy bien, gracias", isPartial: false, confidence: 0.92, timestamp: Date())
        ]
        speechViewModel.transcriptionEntries = spanishEntries
        
        // When
        speechViewModel.stopRecording()
        
        // Wait for session creation
        Thread.sleep(forTimeInterval: 0.6)
        
        // Then
        let sessions = try historyStorageService.fetchAllSessions()
        let session = sessions.first!
        
        // Should detect Spanish language
        XCTAssertTrue(session.detectedLanguages?.contains("es") == true || 
                     session.detectedLanguages?.contains("es-ES") == true)
    }
    
    // MARK: - Performance Integration Tests
    
    func testLargeTranscriptionSessionCreation() throws {
        // Given
        speechViewModel.startRecording()
        
        // Create a large number of transcription entries
        let largeEntries = (0..<100).map { index in
            TranscriptionEntry(
                text: "This is transcription entry number \(index) with some content to make it realistic",
                isPartial: false,
                confidence: 0.9,
                timestamp: Date().addingTimeInterval(TimeInterval(index))
            )
        }
        speechViewModel.transcriptionEntries = largeEntries
        
        // When
        let startTime = CFAbsoluteTimeGetCurrent()
        speechViewModel.stopRecording()
        
        // Wait for session creation
        Thread.sleep(forTimeInterval: 1.0)
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let executionTime = endTime - startTime
        
        // Then
        let sessions = try historyStorageService.fetchAllSessions()
        XCTAssertEqual(sessions.count, 1)
        
        let session = sessions.first!
        XCTAssertEqual(session.entries?.count, 100)
        
        // Performance should be reasonable (less than 2 seconds for 100 entries)
        XCTAssertLessThan(executionTime, 2.0)
    }
    
    // MARK: - Memory Management Integration Tests
    
    func testMemoryManagementDuringSessionCreation() throws {
        // Given
        let numberOfSessions = 10
        
        // When - Create multiple sessions rapidly
        for i in 0..<numberOfSessions {
            speechViewModel.startRecording()
            
            let entries = (0..<10).map { entryIndex in
                TranscriptionEntry(
                    text: "Session \(i) entry \(entryIndex)",
                    isPartial: false,
                    confidence: 0.9,
                    timestamp: Date()
                )
            }
            speechViewModel.transcriptionEntries = entries
            
            speechViewModel.stopRecording()
            Thread.sleep(forTimeInterval: 0.1) // Brief pause between sessions
        }
        
        // Wait for all sessions to be created
        Thread.sleep(forTimeInterval: 2.0)
        
        // Then
        let sessions = try historyStorageService.fetchAllSessions()
        XCTAssertEqual(sessions.count, numberOfSessions)
        
        // Verify memory usage is reasonable
        let totalEntries = sessions.reduce(0) { $0 + ($1.entries?.count ?? 0) }
        XCTAssertEqual(totalEntries, numberOfSessions * 10)
    }
}

// MARK: - Mock Services

class MockAudioEngineManager: AudioEngineManager {
    var isRecordingMock = false
    
    override var isRecording: Bool {
        return isRecordingMock
    }
    
    override func startRecording() throws {
        isRecordingMock = true
    }
    
    override func stopRecording() {
        isRecordingMock = false
    }
}

class MockWebSocketManager: WebSocketManager {
    var onTranscriptionReceived: ((String, Bool, Double) -> Void)?
    
    override func connect() {
        // Mock connection
    }
    
    override func disconnect() {
        // Mock disconnection
    }
    
    func simulateTranscriptionResponse(_ text: String, isPartial: Bool = false, confidence: Double = 0.95) {
        onTranscriptionReceived?(text, isPartial, confidence)
    }
}

// MARK: - Core Data Stack Extension

extension CoreDataStack {
    static func inMemoryStack() -> CoreDataStack {
        let stack = CoreDataStack()
        // Configure for in-memory testing
        // This would need to be implemented in the actual CoreDataStack class
        return stack
    }
}
