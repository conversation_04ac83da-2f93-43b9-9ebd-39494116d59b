//
//  FloatingHistoryIntegrationTests.swift
//  RockerSTTTests
//
//  Created by Augment Agent on 2025/1/25.
//

import XCTest
import SwiftUI
import ViewInspector
import Combine
@testable import RockerSTT

final class FloatingHistoryIntegrationTests: XCTestCase {
    
    var floatingHistoryViewModel: FloatingHistoryViewModel!
    var historyListViewModel: HistoryListViewModel!
    var searchViewModel: HistorySearchViewModel!
    var mockHistoryService: MockHistoryStorageService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockHistoryService = MockHistoryStorageService()
        floatingHistoryViewModel = FloatingHistoryViewModel(historyStorageService: mockHistoryService)
        historyListViewModel = HistoryListViewModel(historyStorageService: mockHistoryService)
        searchViewModel = HistorySearchViewModel(historyStorageService: mockHistoryService)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        floatingHistoryViewModel = nil
        historyListViewModel = nil
        searchViewModel = nil
        mockHistoryService = nil
        super.tearDown()
    }
    
    // MARK: - Data Model Integration Tests
    
    func testHistorySessionDataModelIntegration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        
        // When
        floatingHistoryViewModel.loadSessions()
        historyListViewModel.loadSessions()
        
        // Then
        XCTAssertEqual(floatingHistoryViewModel.allSessions.count, mockSessions.count)
        XCTAssertEqual(historyListViewModel.sessions.count, mockSessions.count)
        
        // Verify data consistency between view models
        for (index, session) in mockSessions.enumerated() {
            XCTAssertEqual(floatingHistoryViewModel.allSessions[index].id, session.id)
            XCTAssertEqual(historyListViewModel.sessions[index].id, session.id)
        }
    }
    
    func testSessionPropertySynchronization() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        historyListViewModel.loadSessions()
        
        let testSession = mockSessions.first!
        
        // When - Update session through floating history view model
        floatingHistoryViewModel.toggleFavorite(for: testSession)
        
        // Then - Verify changes are reflected in both view models
        XCTAssertTrue(testSession.isFavorite)
        XCTAssertTrue(mockHistoryService.updateSessionCalled)
        
        // Reload to simulate real-world scenario
        historyListViewModel.loadSessions()
        let updatedSession = historyListViewModel.sessions.first { $0.id == testSession.id }
        XCTAssertEqual(updatedSession?.isFavorite, testSession.isFavorite)
    }
    
    // MARK: - Card Action Integration Tests
    
    func testFavoriteActionIntegration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        let session = mockSessions.first!
        let initialFavoriteState = session.isFavorite
        
        // When
        floatingHistoryViewModel.toggleFavorite(for: session)
        
        // Then
        XCTAssertEqual(session.isFavorite, !initialFavoriteState)
        XCTAssertTrue(mockHistoryService.updateSessionCalled)
        
        // Verify filtering updates correctly
        floatingHistoryViewModel.selectedTab = .favorites
        if !initialFavoriteState {
            // Session should now appear in favorites
            XCTAssertTrue(floatingHistoryViewModel.filteredSessions.contains(session))
        } else {
            // Session should no longer appear in favorites
            XCTAssertFalse(floatingHistoryViewModel.filteredSessions.contains(session))
        }
    }
    
    func testSaveActionIntegration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        let session = mockSessions.first!
        let initialSavedState = session.isSaved
        
        // When
        floatingHistoryViewModel.toggleSaved(for: session)
        
        // Then
        XCTAssertEqual(session.isSaved, !initialSavedState)
        XCTAssertTrue(mockHistoryService.updateSessionCalled)
        
        // Verify filtering updates correctly
        floatingHistoryViewModel.selectedTab = .saved
        if !initialSavedState {
            // Session should now appear in saved
            XCTAssertTrue(floatingHistoryViewModel.filteredSessions.contains(session))
        } else {
            // Session should no longer appear in saved
            XCTAssertFalse(floatingHistoryViewModel.filteredSessions.contains(session))
        }
    }
    
    func testDeleteActionIntegration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        historyListViewModel.loadSessions()
        
        let session = mockSessions.first!
        let initialCount = floatingHistoryViewModel.allSessions.count
        
        // When
        floatingHistoryViewModel.deleteSession(session)
        
        // Then
        XCTAssertEqual(floatingHistoryViewModel.allSessions.count, initialCount - 1)
        XCTAssertTrue(mockHistoryService.deleteSessionCalled)
        XCTAssertFalse(floatingHistoryViewModel.allSessions.contains(session))
        
        // Verify deletion is reflected across all tabs
        floatingHistoryViewModel.selectedTab = .recents
        XCTAssertFalse(floatingHistoryViewModel.filteredSessions.contains(session))
        
        floatingHistoryViewModel.selectedTab = .favorites
        XCTAssertFalse(floatingHistoryViewModel.filteredSessions.contains(session))
        
        floatingHistoryViewModel.selectedTab = .saved
        XCTAssertFalse(floatingHistoryViewModel.filteredSessions.contains(session))
    }
    
    // MARK: - Navigation Flow Integration Tests
    
    func testNavigationToDetailView() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        let session = mockSessions.first!
        
        // When
        floatingHistoryViewModel.selectedSession = session
        
        // Then
        XCTAssertEqual(floatingHistoryViewModel.selectedSession?.id, session.id)
        XCTAssertTrue(floatingHistoryViewModel.isShowingDetail)
    }
    
    func testNavigationBackFromDetailView() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        let session = mockSessions.first!
        floatingHistoryViewModel.selectedSession = session
        XCTAssertTrue(floatingHistoryViewModel.isShowingDetail)
        
        // When
        floatingHistoryViewModel.selectedSession = nil
        
        // Then
        XCTAssertNil(floatingHistoryViewModel.selectedSession)
        XCTAssertFalse(floatingHistoryViewModel.isShowingDetail)
    }
    
    func testStateRestorationAfterNavigation() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        // Set initial state
        floatingHistoryViewModel.selectedTab = .favorites
        let initialFilteredCount = floatingHistoryViewModel.filteredSessions.count
        
        // Navigate to detail
        let session = mockSessions.first!
        floatingHistoryViewModel.selectedSession = session
        
        // When - Navigate back
        floatingHistoryViewModel.selectedSession = nil
        
        // Then - State should be restored
        XCTAssertEqual(floatingHistoryViewModel.selectedTab, .favorites)
        XCTAssertEqual(floatingHistoryViewModel.filteredSessions.count, initialFilteredCount)
    }
    
    // MARK: - Search Integration Tests
    
    func testSearchFunctionalityIntegration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        searchViewModel.allSessions = mockSessions
        
        let searchQuery = "Session 1"
        
        // When
        searchViewModel.searchText = searchQuery
        searchViewModel.performSearch()
        
        // Then
        XCTAssertFalse(searchViewModel.searchResults.isEmpty)
        XCTAssertTrue(searchViewModel.searchResults.allSatisfy { session in
            session.title?.contains(searchQuery) ?? false
        })
    }
    
    func testSearchWithTabFiltering() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        // Set up search results
        let searchResults = [mockSessions[0], mockSessions[1]] // First two sessions
        floatingHistoryViewModel.searchResults = searchResults
        floatingHistoryViewModel.isSearchActive = true
        
        // When - Switch to favorites tab while search is active
        floatingHistoryViewModel.selectedTab = .favorites
        
        // Then - Should filter search results, not all sessions
        let favoriteSearchResults = floatingHistoryViewModel.filteredSessions
        XCTAssertTrue(favoriteSearchResults.allSatisfy { $0.isFavorite })
        XCTAssertTrue(favoriteSearchResults.allSatisfy { searchResults.contains($0) })
    }
    
    func testTabBarVisibilityDuringSearch() {
        // Given
        let visibilityManager = TabBarVisibilityManager()
        floatingHistoryViewModel.tabBarVisibilityManager = visibilityManager
        
        // When - Activate search
        floatingHistoryViewModel.isSearchActive = true
        
        // Then - Tab bar should remain visible during search
        XCTAssertTrue(visibilityManager.isVisible)
        
        // When - Deactivate search
        floatingHistoryViewModel.isSearchActive = false
        
        // Then - Tab bar visibility should be restored to normal behavior
        XCTAssertTrue(visibilityManager.isVisible)
    }
    
    // MARK: - Modal Presentation Integration Tests
    
    func testModalPresentationState() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        // When - Present modal (e.g., export options)
        floatingHistoryViewModel.isShowingExportOptions = true
        
        // Then
        XCTAssertTrue(floatingHistoryViewModel.isShowingExportOptions)
        
        // Tab bar should remain accessible
        XCTAssertEqual(floatingHistoryViewModel.selectedTab, .recents) // Default state
    }
    
    func testModalDismissalStateRestoration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        // Set specific state
        floatingHistoryViewModel.selectedTab = .saved
        let initialFilteredCount = floatingHistoryViewModel.filteredSessions.count
        
        // Present and dismiss modal
        floatingHistoryViewModel.isShowingExportOptions = true
        floatingHistoryViewModel.isShowingExportOptions = false
        
        // Then - State should be preserved
        XCTAssertEqual(floatingHistoryViewModel.selectedTab, .saved)
        XCTAssertEqual(floatingHistoryViewModel.filteredSessions.count, initialFilteredCount)
    }
    
    // MARK: - Performance Integration Tests
    
    func testLargeDatasetIntegration() {
        // Given
        let largeMockSessions = createMockHistorySessions(count: 1000)
        mockHistoryService.mockSessions = largeMockSessions
        
        // When
        measure {
            floatingHistoryViewModel.loadSessions()
            floatingHistoryViewModel.selectedTab = .favorites
            floatingHistoryViewModel.selectedTab = .saved
            floatingHistoryViewModel.selectedTab = .recents
        }
        
        // Then
        XCTAssertEqual(floatingHistoryViewModel.allSessions.count, 1000)
    }
    
    func testConcurrentOperationsIntegration() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()
        
        let expectation = XCTestExpectation(description: "Concurrent operations should complete")
        expectation.expectedFulfillmentCount = 3
        
        // When - Perform multiple operations concurrently
        DispatchQueue.global().async {
            self.floatingHistoryViewModel.selectedTab = .favorites
            expectation.fulfill()
        }
        
        DispatchQueue.global().async {
            self.floatingHistoryViewModel.toggleFavorite(for: mockSessions.first!)
            expectation.fulfill()
        }
        
        DispatchQueue.global().async {
            self.floatingHistoryViewModel.selectedTab = .saved
            expectation.fulfill()
        }
        
        // Then
        wait(for: [expectation], timeout: 2.0)
        // Should not crash and should maintain consistent state
        XCTAssertNotNil(floatingHistoryViewModel.selectedTab)
    }
    
    // MARK: - Cross-Component Integration Tests

    func testFloatingTabBarWithViewModelIntegration() throws {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()

        let tabBar = FloatingTabBar(
            selectedTab: .constant(floatingHistoryViewModel.selectedTab),
            onTabSelected: { tab in
                self.floatingHistoryViewModel.selectedTab = tab
            }
        )

        // When
        let view = try tabBar.inspect()
        let buttons = try view.findAll(ViewType.Button.self)

        // Simulate tapping favorites tab
        try buttons[1].button().tap()

        // Then
        XCTAssertEqual(floatingHistoryViewModel.selectedTab, .favorites)

        // Verify filtered sessions update
        let favoriteCount = mockSessions.filter { $0.isFavorite }.count
        XCTAssertEqual(floatingHistoryViewModel.filteredSessions.count, favoriteCount)
    }

    func testScrollDetectionWithTabBarVisibility() {
        // Given
        let visibilityManager = TabBarVisibilityManager()
        floatingHistoryViewModel.tabBarVisibilityManager = visibilityManager

        // When - Simulate downward scroll
        visibilityManager.updateScrollOffset(100)

        // Then - Tab bar should hide
        XCTAssertFalse(visibilityManager.isVisible)

        // When - Switch tabs while hidden
        floatingHistoryViewModel.selectedTab = .favorites

        // Then - Tab bar should show for tab switch
        visibilityManager.forceShow()
        XCTAssertTrue(visibilityManager.isVisible)
    }

    func testAnimationIntegrationWithFiltering() {
        // Given
        let mockSessions = createMockHistorySessions()
        mockHistoryService.mockSessions = mockSessions
        floatingHistoryViewModel.loadSessions()

        let animationManager = floatingHistoryViewModel.animationManager

        // When - Switch tabs (triggers filtering animation)
        floatingHistoryViewModel.selectedTab = .favorites

        // Then - Animation should be in progress
        XCTAssertTrue(animationManager.isFilteringInProgress)

        // When - Animation completes
        animationManager.completeCurrentAnimation()

        // Then - Animation should be idle
        XCTAssertEqual(animationManager.currentAnimationPhase, .idle)
        XCTAssertFalse(animationManager.isFilteringInProgress)
    }

    // MARK: - Error Handling Integration Tests

    func testErrorHandlingWithCorruptedData() {
        // Given
        let corruptedSession = HistorySession(context: mockHistoryService.context)
        // Don't set required properties to simulate corruption
        mockHistoryService.mockSessions = [corruptedSession]

        // When & Then - Should handle gracefully without crashing
        XCTAssertNoThrow(floatingHistoryViewModel.loadSessions())
        XCTAssertNoThrow(floatingHistoryViewModel.selectedTab = .favorites)
        XCTAssertNoThrow(floatingHistoryViewModel.selectedTab = .saved)
    }

    func testErrorHandlingWithNetworkFailure() {
        // Given
        mockHistoryService.shouldFailOperations = true

        // When & Then - Should handle failures gracefully
        XCTAssertNoThrow(floatingHistoryViewModel.loadSessions())

        // Should maintain empty state
        XCTAssertTrue(floatingHistoryViewModel.allSessions.isEmpty)
        XCTAssertTrue(floatingHistoryViewModel.isShowingEmptyState)
    }

    // MARK: - Memory Management Integration Tests

    func testMemoryManagementWithLargeDataset() {
        // Given
        let largeMockSessions = createMockHistorySessions(count: 5000)
        mockHistoryService.mockSessions = largeMockSessions

        // When
        floatingHistoryViewModel.loadSessions()

        // Simulate memory pressure
        floatingHistoryViewModel.handleMemoryWarning()

        // Then - Should maintain core functionality
        XCTAssertFalse(floatingHistoryViewModel.allSessions.isEmpty)
        XCTAssertNotNil(floatingHistoryViewModel.selectedTab)
    }

    func testViewModelDeallocation() {
        // Given
        var viewModel: FloatingHistoryViewModel? = FloatingHistoryViewModel()
        viewModel?.loadSessions()

        // When
        viewModel = nil

        // Then - Should deallocate without memory leaks
        XCTAssertNil(viewModel)
    }

    // MARK: - Helper Methods

    private func createMockHistorySessions(count: Int = 10) -> [HistorySession] {
        var sessions: [HistorySession] = []

        for i in 0..<count {
            let session = HistorySession(context: mockHistoryService.context)
            session.id = UUID()
            session.title = "Session \(i)"
            session.createdAt = Date().addingTimeInterval(-Double(i * 3600))
            session.isFavorite = i % 3 == 0 // Every 3rd session is favorite
            session.isSaved = i % 4 == 0 // Every 4th session is saved
            sessions.append(session)
        }

        return sessions
    }
}

// MARK: - Enhanced Mock History Storage Service

extension MockHistoryStorageService {
    var shouldFailOperations: Bool {
        get { return false }
        set { /* Implementation for failure simulation */ }
    }
}
