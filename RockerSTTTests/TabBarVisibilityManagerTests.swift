//
//  TabBarVisibilityManagerTests.swift
//  RockerSTTTests
//
//  Created by Augment Agent on 2025/1/25.
//

import XCTest
import Combine
@testable import RockerSTT

final class TabBarVisibilityManagerTests: XCTestCase {
    
    var visibilityManager: TabBarVisibilityManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        visibilityManager = TabBarVisibilityManager()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        visibilityManager = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testTabBarVisibilityManagerInitialization() {
        // Given & When
        let manager = TabBarVisibilityManager()
        
        // Then
        XCTAssertTrue(manager.isVisible)
        XCTAssertEqual(manager.currentOffset, 0)
        XCTAssertEqual(manager.scrollDirection, .none)
    }
    
    // MARK: - Scroll Direction Detection Tests
    
    func testScrollDirectionDetectionDownward() {
        // Given
        let expectation = XCTestExpectation(description: "Scroll direction should be downward")
        
        visibilityManager.$scrollDirection
            .dropFirst() // Skip initial value
            .sink { direction in
                if direction == .down {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.updateScrollOffset(100) // Scroll down
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(visibilityManager.scrollDirection, .down)
    }
    
    func testScrollDirectionDetectionUpward() {
        // Given
        visibilityManager.updateScrollOffset(100) // Start with some offset
        
        let expectation = XCTestExpectation(description: "Scroll direction should be upward")
        
        visibilityManager.$scrollDirection
            .dropFirst() // Skip initial value
            .sink { direction in
                if direction == .up {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.updateScrollOffset(50) // Scroll up
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(visibilityManager.scrollDirection, .up)
    }
    
    func testScrollDirectionThreshold() {
        // Given
        let smallOffset: CGFloat = 5 // Below threshold
        
        // When
        visibilityManager.updateScrollOffset(smallOffset)
        
        // Then
        XCTAssertEqual(visibilityManager.scrollDirection, .none)
    }
    
    // MARK: - Visibility Logic Tests
    
    func testTabBarHidesOnDownwardScroll() {
        // Given
        let expectation = XCTestExpectation(description: "Tab bar should hide on downward scroll")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if !isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.updateScrollOffset(100) // Significant downward scroll
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertFalse(visibilityManager.isVisible)
    }
    
    func testTabBarShowsOnUpwardScroll() {
        // Given
        visibilityManager.updateScrollOffset(100) // Hide tab bar first
        XCTAssertFalse(visibilityManager.isVisible)
        
        let expectation = XCTestExpectation(description: "Tab bar should show on upward scroll")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.updateScrollOffset(50) // Upward scroll
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(visibilityManager.isVisible)
    }
    
    func testTabBarShowsAtTopOfContent() {
        // Given
        visibilityManager.updateScrollOffset(100) // Hide tab bar first
        XCTAssertFalse(visibilityManager.isVisible)
        
        let expectation = XCTestExpectation(description: "Tab bar should show at top of content")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.updateScrollOffset(0) // Scroll to top
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(visibilityManager.isVisible)
    }
    
    // MARK: - Auto-Show Timer Tests
    
    func testAutoShowTimerActivation() {
        // Given
        visibilityManager.updateScrollOffset(100) // Hide tab bar
        XCTAssertFalse(visibilityManager.isVisible)
        
        let expectation = XCTestExpectation(description: "Tab bar should auto-show after timeout")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        // Wait for auto-show timer (2 seconds + buffer)
        
        // Then
        wait(for: [expectation], timeout: 3.0)
        XCTAssertTrue(visibilityManager.isVisible)
    }
    
    func testAutoShowTimerCancellation() {
        // Given
        visibilityManager.updateScrollOffset(100) // Hide tab bar
        XCTAssertFalse(visibilityManager.isVisible)
        
        // When
        // Start timer, then scroll again before timeout
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.visibilityManager.updateScrollOffset(150) // Continue scrolling
        }
        
        // Then
        // Wait less than the auto-show timeout
        let expectation = XCTestExpectation(description: "Tab bar should remain hidden")
        expectation.isInverted = true
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.5)
        XCTAssertFalse(visibilityManager.isVisible)
    }
    
    // MARK: - Tap to Show Tests
    
    func testTapToShowWhenHidden() {
        // Given
        visibilityManager.updateScrollOffset(100) // Hide tab bar
        XCTAssertFalse(visibilityManager.isVisible)
        
        let expectation = XCTestExpectation(description: "Tab bar should show on tap")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.handleTapToShow()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(visibilityManager.isVisible)
    }
    
    func testTapToShowWhenVisible() {
        // Given
        XCTAssertTrue(visibilityManager.isVisible) // Already visible
        
        // When
        visibilityManager.handleTapToShow()
        
        // Then
        XCTAssertTrue(visibilityManager.isVisible) // Should remain visible
    }
    
    // MARK: - Force Show/Hide Tests
    
    func testForceShow() {
        // Given
        visibilityManager.updateScrollOffset(100) // Hide tab bar
        XCTAssertFalse(visibilityManager.isVisible)
        
        let expectation = XCTestExpectation(description: "Tab bar should force show")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.forceShow()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(visibilityManager.isVisible)
    }
    
    func testForceHide() {
        // Given
        XCTAssertTrue(visibilityManager.isVisible) // Initially visible
        
        let expectation = XCTestExpectation(description: "Tab bar should force hide")
        
        visibilityManager.$isVisible
            .dropFirst() // Skip initial value
            .sink { isVisible in
                if !isVisible {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        visibilityManager.forceHide()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertFalse(visibilityManager.isVisible)
    }
    
    // MARK: - Performance Tests
    
    func testScrollUpdatePerformance() {
        measure {
            for i in 0..<1000 {
                visibilityManager.updateScrollOffset(CGFloat(i))
            }
        }
    }
    
    // MARK: - Edge Cases
    
    func testNegativeScrollOffset() {
        // Given & When
        visibilityManager.updateScrollOffset(-50)
        
        // Then
        XCTAssertTrue(visibilityManager.isVisible) // Should show at top
        XCTAssertEqual(visibilityManager.scrollDirection, .up)
    }
    
    func testZeroScrollOffset() {
        // Given
        visibilityManager.updateScrollOffset(100) // Start with offset
        
        // When
        visibilityManager.updateScrollOffset(0)
        
        // Then
        XCTAssertTrue(visibilityManager.isVisible) // Should show at top
    }
    
    func testRapidScrollChanges() {
        // Given
        let expectation = XCTestExpectation(description: "Should handle rapid scroll changes")
        
        // When
        for i in 0..<10 {
            visibilityManager.updateScrollOffset(CGFloat(i * 20))
        }
        
        // Then
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
        // Should not crash and should have a valid state
        XCTAssertNotNil(visibilityManager.scrollDirection)
    }
    
    // MARK: - Memory Management Tests
    
    func testTimerCleanup() {
        // Given
        visibilityManager.updateScrollOffset(100) // Start timer
        
        // When
        visibilityManager = nil
        
        // Then
        // Should not crash or leak memory
        XCTAssertNil(visibilityManager)
    }
}
