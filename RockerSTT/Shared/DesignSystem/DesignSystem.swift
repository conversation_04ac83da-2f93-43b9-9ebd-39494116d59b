//
//  DesignSystem.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/15.
//

import SwiftUI
import UIKit

// MARK: - Design System Foundation

/// Centralized design system providing consistent design tokens throughout the app
struct DesignSystem {
    static let brandColors = BrandColorPalette()
    static let colors = ColorPalette() // Legacy - deprecated
    static let typography = TypographyScale()
    static let spacing = SpacingScale()
    static let animations = AnimationTimings()
}

// MARK: - Brand Color Palette

struct BrandColorPalette {
    // MARK: - Light Mode Brand Colors
    let persianPurple = Color(red: 0.239, green: 0.145, blue: 0.576) // #3D2593
    let orchid = Color(red: 0.549, green: 0.263, blue: 0.816) // #8C43D0
    let frenchLilac = Color(red: 0.914, green: 0.827, blue: 0.992) // #E9D3FD
    let amber = Color(red: 0.98, green: 0.922, blue: 0.573) // #FAEB92
    let alabaster = Color(red: 1.0, green: 1.0, blue: 1.0) // #FFFFFF
    
    // MARK: - Dark Mode Brand Color Variants
    // Adjusted for dark mode while maintaining brand recognition
    let persianPurpleDark = Color(red: 0.549, green: 0.263, blue: 0.816) // Lighter purple for better contrast
    let orchidDark = Color(red: 0.714, green: 0.463, blue: 0.916) // Brighter orchid for visibility
    let frenchLilacDark = Color(red: 0.239, green: 0.145, blue: 0.576) // Darker lilac becomes deep purple
    let amberDark = Color(red: 0.992, green: 0.773, blue: 0.2) // Slightly muted amber for dark backgrounds
    let alabasterDark = Color(red: 0.110, green: 0.110, blue: 0.118) // Dark background equivalent
    
    // MARK: - Accessibility-Enhanced Color Variants

    // High contrast variants for better accessibility (WCAG AA compliant)
    var persianPurpleHighContrast: Color {
        Color(red: 0.18, green: 0.10, blue: 0.45) // Darker for better contrast
    }

    var orchidHighContrast: Color {
        Color(red: 0.45, green: 0.20, blue: 0.70) // Enhanced contrast
    }

    // Interactive state colors with proper contrast ratios
    var interactiveHover: Color {
        persianPurple.opacity(0.1)
    }

    var interactivePressed: Color {
        persianPurple.opacity(0.2)
    }

    var interactiveFocus: Color {
        persianPurple.opacity(0.15)
    }

    // Loading and skeleton colors
    var skeletonBase: Color {
        orchid.opacity(0.08)
    }

    var skeletonHighlight: Color {
        orchid.opacity(0.15)
    }

    // Enhanced shadow colors
    var shadowLight: Color {
        persianPurple.opacity(0.15)
    }

    var shadowMedium: Color {
        persianPurple.opacity(0.25)
    }

    var shadowStrong: Color {
        persianPurple.opacity(0.35)
    }

    // MARK: - 50% Opacity Variants
    var persianPurple50: Color { persianPurple.opacity(0.5) }
    var orchid50: Color { orchid.opacity(0.5) }
    var frenchLilac50: Color { frenchLilac.opacity(0.5) }
    var amber50: Color { amber.opacity(0.5) }
    var alabaster50: Color { alabaster.opacity(0.5) }

    // Dark mode opacity variants
    var persianPurpleDark50: Color { persianPurpleDark.opacity(0.5) }
    var orchidDark50: Color { orchidDark.opacity(0.5) }
    var frenchLilacDark50: Color { frenchLilacDark.opacity(0.5) }
    var amberDark50: Color { amberDark.opacity(0.5) }
    var alabasterDark50: Color { alabasterDark.opacity(0.5) }
    
    // MARK: - Static Gradients (Light Mode)
    let primaryGradient = LinearGradient(
        colors: [Color(red: 0.239, green: 0.145, blue: 0.576), Color(red: 0.549, green: 0.263, blue: 0.816)], // Persian Purple to Orchid
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    let backgroundGradient = LinearGradient(
        colors: [Color(red: 0.914, green: 0.827, blue: 0.992).opacity(0.3), Color(red: 1.0, green: 1.0, blue: 1.0)], // French Lilac to Alabaster
        startPoint: .top,
        endPoint: .bottom
    )
    
    let accentGradient = LinearGradient(
        colors: [Color(red: 0.549, green: 0.263, blue: 0.816), Color(red: 0.914, green: 0.827, blue: 0.992)], // Orchid to French Lilac
        startPoint: .leading,
        endPoint: .trailing
    )
    
    // MARK: - Dark Mode Gradients
    let primaryGradientDark = LinearGradient(
        colors: [Color(red: 0.549, green: 0.263, blue: 0.816), Color(red: 0.714, green: 0.463, blue: 0.916)], // Persian Purple Dark to Orchid Dark
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    let backgroundGradientDark = LinearGradient(
        colors: [Color(red: 0.239, green: 0.145, blue: 0.576).opacity(0.2), Color(red: 0.110, green: 0.110, blue: 0.118)], // French Lilac Dark to Alabaster Dark
        startPoint: .top,
        endPoint: .bottom
    )
    
    let accentGradientDark = LinearGradient(
        colors: [Color(red: 0.714, green: 0.463, blue: 0.916), Color(red: 0.239, green: 0.145, blue: 0.576)], // Orchid Dark to French Lilac Dark
        startPoint: .leading,
        endPoint: .trailing
    )
    
    // MARK: - Adaptive Colors for Light/Dark Mode
    var adaptivePrimary: Color {
        Color.adaptive(light: persianPurple, dark: persianPurpleDark)
    }
    
    var adaptiveSecondary: Color {
        Color.adaptive(light: orchid, dark: orchidDark)
    }
    
    var adaptiveTertiary: Color {
        Color.adaptive(light: frenchLilac, dark: frenchLilacDark)
    }
    
    var adaptiveAccent: Color {
        Color.adaptive(light: amber, dark: amberDark)
    }
    
    var adaptiveBackground: Color {
        Color.adaptive(light: alabaster, dark: alabasterDark)
    }
    
    var adaptiveSurface: Color {
        Color.adaptive(light: frenchLilac.opacity(0.3), dark: frenchLilacDark.opacity(0.15))
    }
    
    var adaptiveCardBackground: Color {
        Color.adaptive(light: alabaster, dark: Color(.secondarySystemBackground))
    }
    
    var adaptiveCardBorder: Color {
        Color.adaptive(light: frenchLilac, dark: frenchLilacDark.opacity(0.3))
    }
    
    // MARK: - Adaptive Text Colors
    var adaptiveTextPrimary: Color {
        Color.adaptive(light: persianPurple, dark: orchidDark)
    }
    
    var adaptiveTextSecondary: Color {
        Color.adaptive(light: orchid, dark: frenchLilacDark.opacity(0.8))
    }
    
    var adaptiveTextTertiary: Color {
        Color.adaptive(light: frenchLilac.opacity(0.8), dark: frenchLilacDark.opacity(0.6))
    }
    
    // MARK: - Adaptive Gradients
    var adaptivePrimaryGradient: LinearGradient {
        LinearGradient(
            colors: [adaptivePrimary, adaptiveSecondary],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    var adaptiveBackgroundGradient: LinearGradient {
        LinearGradient(
            colors: [adaptiveSurface, adaptiveBackground],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    var adaptiveAccentGradient: LinearGradient {
        LinearGradient(
            colors: [adaptiveSecondary, adaptiveTertiary],
            startPoint: .leading,
            endPoint: .trailing
        )
    }
    
    // MARK: - Accessibility-Compliant Colors
    // High contrast variants for accessibility
    var accessibilityPrimary: Color {
        Color.adaptive(
            light: Color(red: 0.2, green: 0.1, blue: 0.5), // Darker for better contrast
            dark: Color(red: 0.8, green: 0.6, blue: 1.0)   // Brighter for dark mode
        )
    }
    
    var accessibilitySecondary: Color {
        Color.adaptive(
            light: Color(red: 0.4, green: 0.2, blue: 0.7), // Enhanced contrast
            dark: Color(red: 0.9, green: 0.7, blue: 1.0)   // High visibility
        )
    }
    
    var accessibilityBackground: Color {
        Color.adaptive(
            light: Color.white,
            dark: Color.black
        )
    }
    
    // MARK: - Color Scheme Detection
    func colorForScheme(_ colorScheme: ColorScheme, light: Color, dark: Color) -> Color {
        colorScheme == .dark ? dark : light
    }
    
    // MARK: - Semantic Color Roles
    var brandPrimary: Color { adaptivePrimary }
    var brandSecondary: Color { adaptiveSecondary }
    var brandTertiary: Color { adaptiveTertiary }
    var brandAccent: Color { adaptiveAccent }
    var brandBackground: Color { adaptiveBackground }
    var brandSurface: Color { adaptiveSurface }
    var brandTextPrimary: Color { adaptiveTextPrimary }
    var brandTextSecondary: Color { adaptiveTextSecondary }
    var brandTextTertiary: Color { adaptiveTextTertiary }
}

// MARK: - Legacy Color Palette (Deprecated - Use BrandColorPalette)

struct ColorPalette {
    // Primary Colors
    let primaryBlue = Color(red: 0.0, green: 0.478, blue: 1.0) // #007AFF
    let primaryPurple = Color(red: 0.345, green: 0.337, blue: 0.839) // #5856D6
    let primaryGradient = LinearGradient(
        gradient: Gradient(colors: [
            Color(red: 0.0, green: 0.478, blue: 1.0),
            Color(red: 0.345, green: 0.337, blue: 0.839)
        ]),
        startPoint: .leading,
        endPoint: .trailing
    )
    
    // Secondary Colors
    let secondaryLight = Color(red: 0.949, green: 0.949, blue: 0.969) // #F2F2F7
    let secondaryMedium = Color(red: 0.557, green: 0.557, blue: 0.576) // #8E8E93
    
    // Background Gradients
    let backgroundLight = LinearGradient(
        gradient: Gradient(colors: [
            Color(red: 0.973, green: 0.980, blue: 1.0), // #F8FAFF
            Color(red: 0.910, green: 0.949, blue: 1.0)  // #E8F2FF
        ]),
        startPoint: .top,
        endPoint: .bottom
    )
    
    let backgroundDark = LinearGradient(
        gradient: Gradient(colors: [
            Color(red: 0.110, green: 0.110, blue: 0.118), // #1C1C1E
            Color(red: 0.173, green: 0.173, blue: 0.180)  // #2C2C2E
        ]),
        startPoint: .top,
        endPoint: .bottom
    )
    
    // Adaptive background that responds to system appearance
    var adaptiveBackground: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(.systemBackground).opacity(0.95),
                Color(.secondarySystemBackground).opacity(0.8)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    // Accent Colors
    let recordingRed = Color(red: 1.0, green: 0.231, blue: 0.188) // #FF3B30
    let successGreen = Color(red: 0.204, green: 0.780, blue: 0.349) // #34C759
    let warningOrange = Color(red: 1.0, green: 0.584, blue: 0.0) // #FF9500
    
    // Card and Surface Colors
    let cardBackground = Color(.secondarySystemBackground)
    let cardBackgroundElevated = Color(.tertiarySystemBackground)
    
    // Text Colors
    let textPrimary = Color(.label)
    let textSecondary = Color(.secondaryLabel)
    let textTertiary = Color(.tertiaryLabel)
    
    // Border and Separator Colors
    let separator = Color(.separator)
    let border = Color(.quaternaryLabel)
}

// MARK: - Typography Scale

struct TypographyScale {
    // MARK: - iPhone 16 Optimized System Fonts with Dynamic Type Support
    
    // Large Title: 34pt, Bold - iPhone 16 optimized
    var largeTitle: Font {
        Font.custom("SF Pro Display", size: 36, relativeTo: .largeTitle).weight(.bold)
    }
    
    // Title 1: 28pt, Regular - iPhone 16 optimized
    var title1: Font {
        Font.custom("SF Pro Display", size: 30, relativeTo: .title).weight(.regular)
    }
    
    // Title 2: 22pt, Regular - iPhone 16 optimized
    var title2: Font {
        Font.custom("SF Pro Display", size: 24, relativeTo: .title2).weight(.regular)
    }
    
    // Title 3: 20pt, Semibold - iPhone 16 optimized
    var title3: Font {
        Font.custom("SF Pro Display", size: 22, relativeTo: .title3).weight(.semibold)
    }
    
    // Headline: 17pt, Semibold - iPhone 16 optimized
    var headline: Font {
        Font.custom("SF Pro Text", size: 18, relativeTo: .headline).weight(.semibold)
    }
    
    // Body: 17pt, Regular - iPhone 16 optimized
    var body: Font {
        Font.custom("SF Pro Text", size: 18, relativeTo: .body).weight(.regular)
    }
    
    // Callout: 16pt, Regular - iPhone 16 optimized
    var callout: Font {
        Font.custom("SF Pro Text", size: 17, relativeTo: .callout).weight(.regular)
    }
    
    // Subheadline: 15pt, Regular - iPhone 16 optimized
    var subheadline: Font {
        Font.custom("SF Pro Text", size: 16, relativeTo: .subheadline).weight(.regular)
    }
    
    // Footnote: 13pt, Regular - iPhone 16 optimized
    var footnote: Font {
        Font.custom("SF Pro Text", size: 14, relativeTo: .footnote).weight(.regular)
    }
    
    // Caption 1: 12pt, Regular - iPhone 16 optimized
    var caption: Font {
        Font.custom("SF Pro Text", size: 13, relativeTo: .caption).weight(.regular)
    }
    
    // Caption 2: 11pt, Regular - iPhone 16 optimized
    var caption2: Font {
        Font.custom("SF Pro Text", size: 12, relativeTo: .caption2).weight(.regular)
    }
    
    // MARK: - Semantic Typography Tokens
    
    // Primary heading for main screens and sections
    var heading1: Font {
        Font.custom("SF Pro Display", size: 30, relativeTo: .title).weight(.bold)
    }
    
    // Secondary heading for subsections and cards
    var heading2: Font {
        Font.custom("SF Pro Display", size: 24, relativeTo: .title2).weight(.semibold)
    }
    
    // Tertiary heading for component titles
    var heading3: Font {
        Font.custom("SF Pro Text", size: 20, relativeTo: .title3).weight(.semibold)
    }
    
    // Primary body text for main content
    var bodyPrimary: Font {
        Font.custom("SF Pro Text", size: 18, relativeTo: .body).weight(.regular)
    }
    
    // Secondary body text for supporting content
    var bodySecondary: Font {
        Font.custom("SF Pro Text", size: 16, relativeTo: .callout).weight(.regular)
    }
    
    // Caption text for metadata and timestamps
    var captionPrimary: Font {
        Font.custom("SF Pro Text", size: 14, relativeTo: .footnote).weight(.regular)
    }
    
    // Small caption for fine details
    var captionSecondary: Font {
        Font.custom("SF Pro Text", size: 12, relativeTo: .caption2).weight(.regular)
    }
    
    // MARK: - Component-Specific Typography
    
    // Tab bar labels - optimized for iPhone 16
    var tabBarLabel: Font {
        Font.custom("SF Pro Text", size: 11, relativeTo: .caption2).weight(.medium)
    }
    
    // Navigation titles - optimized for iPhone 16
    var navigationTitle: Font {
        Font.custom("SF Pro Display", size: 20, relativeTo: .title3).weight(.semibold)
    }
    
    // Button labels - optimized for iPhone 16 touch targets
    var buttonLabel: Font {
        Font.custom("SF Pro Text", size: 17, relativeTo: .callout).weight(.semibold)
    }
    
    // Recording button icon - larger for iPhone 16
    var recordingButtonIcon: Font {
        Font.custom("SF Pro Text", size: 26, relativeTo: .title2).weight(.medium)
    }
    
    // Timestamp text in transcription cards
    var timestampText: Font {
        Font.custom("SF Pro Text", size: 12, relativeTo: .caption).weight(.regular)
    }
    
    // Badge text for notifications
    var badgeText: Font {
        Font.custom("SF Pro Text", size: 11, relativeTo: .caption2).weight(.semibold)
    }
    
    // Search placeholder text
    var searchPlaceholder: Font {
        Font.custom("SF Pro Text", size: 17, relativeTo: .callout).weight(.regular)
    }
    
    // Settings section headers
    var settingsHeader: Font {
        Font.custom("SF Pro Text", size: 14, relativeTo: .footnote).weight(.semibold)
    }
    
    // List item titles
    var listItemTitle: Font {
        Font.custom("SF Pro Text", size: 17, relativeTo: .callout).weight(.medium)
    }
    
    // List item subtitles
    var listItemSubtitle: Font {
        Font.custom("SF Pro Text", size: 15, relativeTo: .subheadline).weight(.regular)
    }
    
    // MARK: - Dynamic Type Helpers
    
    /// Returns a font that scales with Dynamic Type accessibility sizes
    func scaledFont(size: CGFloat, weight: Font.Weight = .regular, relativeTo textStyle: Font.TextStyle = .body) -> Font {
        Font.custom("SF Pro Text", size: size, relativeTo: textStyle).weight(weight)
    }
    
    /// Returns a display font that scales with Dynamic Type accessibility sizes
    func scaledDisplayFont(size: CGFloat, weight: Font.Weight = .regular, relativeTo textStyle: Font.TextStyle = .title) -> Font {
        Font.custom("SF Pro Display", size: size, relativeTo: textStyle).weight(weight)
    }
}

// MARK: - Spacing Scale (8pt Grid System)

struct SpacingScale {
    // MARK: - Base 8pt Grid System
    
    /// Base unit: 8pt - Foundation of the spacing system
    static let baseUnit: CGFloat = 8
    
    // MARK: - Core Spacing Values (8pt Grid)
    
    /// Micro: 4pt (0.5 × base) - Minimal spacing for tight layouts
    let micro: CGFloat = 4
    
    /// XSmall: 8pt (1 × base) - Small spacing between related elements
    let xSmall: CGFloat = 8
    
    /// Small: 16pt (2 × base) - Standard spacing between elements
    let small: CGFloat = 16
    
    /// Medium: 24pt (3 × base) - Moderate spacing for sections
    let medium: CGFloat = 24
    
    /// Large: 32pt (4 × base) - Large spacing for major sections
    let large: CGFloat = 32
    
    /// XLarge: 40pt (5 × base) - Extra large spacing for screen sections
    let xLarge: CGFloat = 40
    
    /// XXLarge: 48pt (6 × base) - Maximum spacing for major separations
    let xxLarge: CGFloat = 48
    
    /// XXXLarge: 64pt (8 × base) - Exceptional spacing for hero sections
    let xxxLarge: CGFloat = 64
    
    // MARK: - Component-Specific Spacing Tokens
    
    // MARK: Card Components
    /// Card internal padding - optimized for iPhone 16
    let cardPadding: CGFloat = 16 // 2 × base
    
    /// Spacing between cards in lists
    let cardSpacing: CGFloat = 16 // 2 × base
    
    /// Card margin from screen edges
    let cardMargin: CGFloat = 16 // 2 × base
    
    /// Card header spacing
    let cardHeaderSpacing: CGFloat = 12 // 1.5 × base (exception for visual balance)
    
    /// Card footer spacing
    let cardFooterSpacing: CGFloat = 8 // 1 × base
    
    // MARK: Button Components
    /// Button internal padding (horizontal)
    let buttonPaddingHorizontal: CGFloat = 24 // 3 × base
    
    /// Button internal padding (vertical)
    let buttonPaddingVertical: CGFloat = 16 // 2 × base
    
    /// Spacing between buttons in groups
    let buttonSpacing: CGFloat = 16 // 2 × base
    
    /// Minimum touch target size for iPhone 16
    let minimumTouchTarget: CGFloat = 44 // Apple HIG requirement
    
    // MARK: List Components
    /// Spacing between list items
    let listItemSpacing: CGFloat = 8 // 1 × base
    
    /// List item internal padding
    let listItemPadding: CGFloat = 16 // 2 × base
    
    /// List section header spacing
    let listSectionSpacing: CGFloat = 32 // 4 × base
    
    /// List section header padding
    let listSectionPadding: CGFloat = 16 // 2 × base
    
    // MARK: Navigation Components
    /// Tab bar height - optimized for iPhone 16
    let tabBarHeight: CGFloat = 56 // 7 × base
    
    /// Tab bar item padding
    let tabBarItemPadding: CGFloat = 8 // 1 × base
    
    /// Navigation bar height
    let navigationBarHeight: CGFloat = 44 // Apple standard
    
    /// Navigation bar title padding
    let navigationTitlePadding: CGFloat = 16 // 2 × base
    
    // MARK: Form Components
    /// Text field padding
    let textFieldPadding: CGFloat = 16 // 2 × base
    
    /// Text field height - optimized for iPhone 16
    let textFieldHeight: CGFloat = 48 // 6 × base
    
    /// Form section spacing
    let formSectionSpacing: CGFloat = 32 // 4 × base
    
    /// Form field spacing
    let formFieldSpacing: CGFloat = 16 // 2 × base
    
    // MARK: Recording Interface
    /// Recording button size - optimized for iPhone 16
    let recordingButtonSize: CGFloat = 80 // 10 × base
    
    /// Recording button margin
    let recordingButtonMargin: CGFloat = 32 // 4 × base
    
    /// Audio visualization spacing
    let audioVisualizationSpacing: CGFloat = 8 // 1 × base
    
    /// Transcription text padding
    let transcriptionPadding: CGFloat = 24 // 3 × base
    
    // MARK: History Interface
    /// History grid spacing
    let historyGridSpacing: CGFloat = 16 // 2 × base
    
    /// History card minimum height
    let historyCardMinHeight: CGFloat = 120 // 15 × base
    
    /// Search bar padding
    let searchBarPadding: CGFloat = 16 // 2 × base
    
    /// Search bar height
    let searchBarHeight: CGFloat = 40 // 5 × base
    
    /// Filter pill spacing
    let filterPillSpacing: CGFloat = 8 // 1 × base
    
    /// Filter pill padding
    let filterPillPadding: CGFloat = 12 // 1.5 × base (exception for visual balance)
    
    // MARK: Settings Interface
    /// Settings section spacing
    let settingsSectionSpacing: CGFloat = 32 // 4 × base
    
    /// Settings row height
    let settingsRowHeight: CGFloat = 48 // 6 × base
    
    /// Settings row padding
    let settingsRowPadding: CGFloat = 16 // 2 × base
    
    /// Settings toggle spacing
    let settingsToggleSpacing: CGFloat = 16 // 2 × base
    
    // MARK: Modal and Sheet Components
    /// Modal padding
    let modalPadding: CGFloat = 24 // 3 × base
    
    /// Sheet handle spacing
    let sheetHandleSpacing: CGFloat = 8 // 1 × base
    
    /// Sheet content padding
    let sheetContentPadding: CGFloat = 16 // 2 × base
    
    // MARK: Screen Layout
    /// Screen edge padding - optimized for iPhone 16
    let screenPadding: CGFloat = 16 // 2 × base
    
    /// Safe area additional padding
    let safeAreaPadding: CGFloat = 8 // 1 × base
    
    /// Section divider spacing
    let sectionDividerSpacing: CGFloat = 24 // 3 × base
    
    /// Content max width for readability
    let contentMaxWidth: CGFloat = 400 // 50 × base
    
    // MARK: - Dynamic Spacing Helpers
    
    /// Returns spacing value based on 8pt grid multiplier
    func gridSpacing(_ multiplier: CGFloat) -> CGFloat {
        return SpacingScale.baseUnit * multiplier
    }
    
    /// Returns responsive spacing that adapts to screen size
    func responsiveSpacing(compact: CGFloat, regular: CGFloat) -> CGFloat {
        // This would be enhanced with actual screen size detection
        // For now, returning regular spacing as iPhone 16 has regular width
        return regular
    }
    
    /// Returns spacing adjusted for accessibility (larger touch targets)
    func accessibilitySpacing(_ baseSpacing: CGFloat, isAccessibilityEnabled: Bool = false) -> CGFloat {
        return isAccessibilityEnabled ? max(baseSpacing, minimumTouchTarget) : baseSpacing
    }
}

// MARK: - Animation Timings

struct AnimationTimings {
    // Quick: 0.2s ease-out
    let quick = Animation.easeOut(duration: 0.2)
    
    // Standard: 0.3s ease-in-out (matches tab switching requirement)
    let standard = Animation.easeInOut(duration: 0.3)
    
    // Slow: 0.5s ease-in-out
    let slow = Animation.easeInOut(duration: 0.5)
    
    // Spring: response 0.6, damping 0.8
    let spring = Animation.spring(response: 0.6, dampingFraction: 0.8)
    
    // MARK: - Enhanced Animation Presets

    // Button press animation with proper haptic timing
    let buttonPress = Animation.easeOut(duration: 0.12)

    // Card entrance with spring physics (as per requirements)
    let cardEntrance = Animation.spring(response: 0.55, dampingFraction: 0.75, blendDuration: 0.08)

    // Recording button pulse animation using brand colors (as per requirements)
    let recordingPulse = Animation.easeInOut(duration: 1.2).repeatForever(autoreverses: true)

    // Tab switching animation (enhanced for better feel)
    let tabSwitch = Animation.interpolatingSpring(
        mass: 1.0,
        stiffness: 170,
        damping: 26,
        initialVelocity: 0
    )

    // Modal presentation transitions (smooth as per requirements)
    let modalPresent = Animation.spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0)
    let modalDismiss = Animation.easeInOut(duration: 0.3)

    // MARK: - Micro-Interaction Animations

    // Hover feedback for interactive elements
    let hoverFeedback = Animation.interpolatingSpring(
        mass: 0.8,
        stiffness: 200,
        damping: 20,
        initialVelocity: 0
    )

    // Selection feedback with haptic timing
    let selectionFeedback = Animation.interpolatingSpring(
        mass: 0.9,
        stiffness: 180,
        damping: 22,
        initialVelocity: 0
    )

    // Loading state transitions
    let loadingTransition = Animation.easeInOut(duration: 0.4)

    // Empty state entrance
    let emptyStateEntrance = Animation.interpolatingSpring(
        mass: 1.1,
        stiffness: 140,
        damping: 30,
        initialVelocity: 0
    )

    // Skeleton shimmer effect
    let skeletonShimmer = Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true)
    
    // Recording visualization rings
    let recordingRings = Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true)
    
    // Legacy animations
    let shimmer = Animation.linear(duration: 1.8).repeatForever(autoreverses: false)
    let breathingEffect = Animation.easeInOut(duration: 2.5).repeatForever(autoreverses: true)
    
    // MARK: - Accessibility-Aware Animations
    
    /// Returns animation that respects reduced motion settings
    func respectingAccessibility(_ animation: Animation) -> Animation {
        UIAccessibility.isReduceMotionEnabled ? .linear(duration: 0.01) : animation
    }
    
    /// Tab switching that respects accessibility
    var accessibleTabSwitch: Animation {
        respectingAccessibility(tabSwitch)
    }
    
    /// Card entrance that respects accessibility
    var accessibleCardEntrance: Animation {
        respectingAccessibility(cardEntrance)
    }
    
    /// Button press that respects accessibility
    var accessibleButtonPress: Animation {
        UIAccessibility.isReduceMotionEnabled ? .easeOut(duration: 0.08) : buttonPress
    }
    
    /// Recording pulse that respects accessibility
    var accessibleRecordingPulse: Animation {
        respectingAccessibility(recordingPulse)
    }
}

// MARK: - Shadow Styles

struct ShadowStyles {
    static let subtle = (
        color: Color.black.opacity(0.05),
        radius: CGFloat(2),
        x: CGFloat(0),
        y: CGFloat(1)
    )
    
    static let card = (
        color: Color.black.opacity(0.1),
        radius: CGFloat(8),
        x: CGFloat(0),
        y: CGFloat(2)
    )
    
    static let elevated = (
        color: Color.black.opacity(0.15),
        radius: CGFloat(16),
        x: CGFloat(0),
        y: CGFloat(4)
    )
    
    static let recordingButton = (
        color: Color.black.opacity(0.2),
        radius: CGFloat(12),
        x: CGFloat(0),
        y: CGFloat(6)
    )
}

// MARK: - Corner Radius

struct CornerRadius {
    static let small: CGFloat = 8
    static let medium: CGFloat = 12
    static let large: CGFloat = 16
    static let xLarge: CGFloat = 24
    
    // Specific component radii
    static let card: CGFloat = 12
    static let button: CGFloat = 10
    static let recordingButton: CGFloat = 36 // Half of 72pt diameter
    static let badge: CGFloat = 6
}

// MARK: - Haptic Feedback Patterns

enum HapticPattern {
    case light
    case medium
    case heavy
    case success
    case warning
    case error
    case selection
    
    func trigger() {
        switch self {
        case .light:
            let impact = UIImpactFeedbackGenerator(style: .light)
            impact.impactOccurred()
        case .medium:
            let impact = UIImpactFeedbackGenerator(style: .medium)
            impact.impactOccurred()
        case .heavy:
            let impact = UIImpactFeedbackGenerator(style: .heavy)
            impact.impactOccurred()
        case .success:
            let notification = UINotificationFeedbackGenerator()
            notification.notificationOccurred(.success)
        case .warning:
            let notification = UINotificationFeedbackGenerator()
            notification.notificationOccurred(.warning)
        case .error:
            let notification = UINotificationFeedbackGenerator()
            notification.notificationOccurred(.error)
        case .selection:
            let selection = UISelectionFeedbackGenerator()
            selection.selectionChanged()
        }
    }
}

// MARK: - Brand Color Extensions

extension Color {
    /// Static brand color shortcuts for specific use cases (light mode colors)
    static let brandPersianPurple = DesignSystem.brandColors.persianPurple
    static let brandOrchid = DesignSystem.brandColors.orchid
    static let brandFrenchLilac = DesignSystem.brandColors.frenchLilac
    static let brandAmber = DesignSystem.brandColors.amber
    static let brandAlabaster = DesignSystem.brandColors.alabaster
    
    /// Dark mode brand color shortcuts
    static let brandPersianPurpleDark = DesignSystem.brandColors.persianPurpleDark
    static let brandOrchidDark = DesignSystem.brandColors.orchidDark
    static let brandFrenchLilacDark = DesignSystem.brandColors.frenchLilacDark
    static let brandAmberDark = DesignSystem.brandColors.amberDark
    static let brandAlabasterDark = DesignSystem.brandColors.alabasterDark
    
    /// Adaptive brand colors that respond to light/dark mode (RECOMMENDED)
    static let brandPrimary = DesignSystem.brandColors.brandPrimary
    static let brandSecondary = DesignSystem.brandColors.brandSecondary
    static let brandTertiary = DesignSystem.brandColors.brandTertiary
    static let brandAccent = DesignSystem.brandColors.brandAccent
    static let brandBackground = DesignSystem.brandColors.brandBackground
    static let brandSurface = DesignSystem.brandColors.brandSurface
    static let brandCardBackground = DesignSystem.brandColors.adaptiveCardBackground
    static let brandCardBorder = DesignSystem.brandColors.adaptiveCardBorder
    
    /// Adaptive text colors for proper contrast
    static let brandTextPrimary = DesignSystem.brandColors.brandTextPrimary
    static let brandTextSecondary = DesignSystem.brandColors.brandTextSecondary
    static let brandTextTertiary = DesignSystem.brandColors.brandTextTertiary
    
    /// Accessibility-compliant colors
    static let brandAccessibilityPrimary = DesignSystem.brandColors.accessibilityPrimary
    static let brandAccessibilitySecondary = DesignSystem.brandColors.accessibilitySecondary
    static let brandAccessibilityBackground = DesignSystem.brandColors.accessibilityBackground
}

// MARK: - Typography Extensions

extension View {
    /// Apply semantic heading styles with adaptive brand colors
    func headingStyle(_ level: Int = 1) -> some View {
        Group {
            switch level {
            case 1:
                self.font(DesignSystem.typography.heading1)
                    .foregroundColor(.brandTextPrimary)
            case 2:
                self.font(DesignSystem.typography.heading2)
                    .foregroundColor(.brandTextPrimary)
            case 3:
                self.font(DesignSystem.typography.heading3)
                    .foregroundColor(.brandTextSecondary)
            default:
                self.font(DesignSystem.typography.bodyPrimary)
                    .foregroundColor(.brandTextPrimary)
            }
        }
    }
    
    /// Apply semantic body text styles with adaptive colors
    func bodyStyle(secondary: Bool = false) -> some View {
        self.font(secondary ? DesignSystem.typography.bodySecondary : DesignSystem.typography.bodyPrimary)
            .foregroundColor(secondary ? .brandTextSecondary : .brandTextPrimary)
    }
    
    /// Apply semantic caption styles with adaptive colors
    func captionStyle(secondary: Bool = false) -> some View {
        self.font(secondary ? DesignSystem.typography.captionSecondary : DesignSystem.typography.captionPrimary)
            .foregroundColor(secondary ? .brandTextTertiary : .brandTextSecondary)
    }
    
    /// Apply consistent spacing using 8pt grid
    func gridSpacing(_ multiplier: CGFloat) -> some View {
        self.padding(DesignSystem.spacing.gridSpacing(multiplier))
    }
    
    /// Apply component-specific spacing
    func componentSpacing(_ component: SpacingComponent) -> some View {
        Group {
            switch component {
            case .card:
                self.padding(DesignSystem.spacing.cardPadding)
            case .button:
                self.padding(.horizontal, DesignSystem.spacing.buttonPaddingHorizontal)
                    .padding(.vertical, DesignSystem.spacing.buttonPaddingVertical)
            case .listItem:
                self.padding(DesignSystem.spacing.listItemPadding)
            case .screen:
                self.padding(DesignSystem.spacing.screenPadding)
            case .modal:
                self.padding(DesignSystem.spacing.modalPadding)
            }
        }
    }
}

// MARK: - Spacing Component Types

enum SpacingComponent {
    case card
    case button
    case listItem
    case screen
    case modal
}

// MARK: - Accessibility-Enhanced Design System Extensions

extension View {
    /// Apply consistent card styling with accessibility support and dark mode
    func cardStyle() -> some View {
        self
            .background(.brandCardBackground)
            .cornerRadius(CornerRadius.card)
            .shadow(
                color: ShadowStyles.card.color,
                radius: ShadowStyles.card.radius,
                x: ShadowStyles.card.x,
                y: ShadowStyles.card.y
            )
            .accessibilityElement(children: .contain)
    }
    
    /// Apply elevated card styling with dark mode support
    func elevatedCardStyle() -> some View {
        self
            .background(.brandCardBackground)
            .cornerRadius(CornerRadius.card)
            .shadow(
                color: ShadowStyles.elevated.color,
                radius: ShadowStyles.elevated.radius,
                x: ShadowStyles.elevated.x,
                y: ShadowStyles.elevated.y
            )
    }
    
    /// Apply subtle shadow with adaptive colors
    func subtleShadow() -> some View {
        self.shadow(
            color: Color.brandPrimary.opacity(0.1),
            radius: ShadowStyles.subtle.radius,
            x: ShadowStyles.subtle.x,
            y: ShadowStyles.subtle.y
        )
    }
    
    /// Apply brand card styling with adaptive colors and dark mode support
    func brandCardStyle() -> some View {
        self
            .background(.brandCardBackground)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.card)
                    .stroke(.brandCardBorder, lineWidth: 1)
            )
            .cornerRadius(CornerRadius.card)
            .shadow(
                color: Color.brandPrimary.opacity(0.1),
                radius: ShadowStyles.card.radius,
                x: ShadowStyles.card.x,
                y: ShadowStyles.card.y
            )
    }
    
    /// Apply brand primary button styling with accessibility support and dark mode
    func brandPrimaryButtonStyle() -> some View {
        self
            .background(DesignSystem.brandColors.adaptivePrimaryGradient)
            .foregroundColor(.white)
            .cornerRadius(CornerRadius.button)
            .shadow(
                color: Color.brandPrimary.opacity(0.3),
                radius: 8,
                x: 0,
                y: 4
            )
            .frame(minHeight: 44)
    }
    
    /// Apply brand secondary button styling with accessibility support and dark mode
    func brandSecondaryButtonStyle() -> some View {
        self
            .background(.brandTertiary)
            .foregroundColor(.brandPrimary)
            .cornerRadius(CornerRadius.button)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.button)
                    .stroke(.brandSecondary.opacity(0.3), lineWidth: 1)
            )
            .frame(minHeight: 44)
    }
    
    /// Apply accessibility-compliant styling for high contrast mode
    func accessibilityCompliantStyle() -> some View {
        self
            .background(.brandAccessibilityBackground)
            .foregroundColor(.brandAccessibilityPrimary)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.card)
                    .stroke(.brandAccessibilitySecondary, lineWidth: 2)
            )
    }
}

// MARK: - ShapeStyle Extensions for Brand Colors

extension ShapeStyle where Self == Color {
    /// Brand card background color
    static var brandCardBackground: Color { Color.brandCardBackground }

    /// Brand card border color
    static var brandCardBorder: Color { Color.brandCardBorder }

    /// Brand secondary color
    static var brandSecondary: Color { Color.brandSecondary }

    /// Brand tertiary color
    static var brandTertiary: Color { Color.brandTertiary }

    /// Brand accessibility background color
    static var brandAccessibilityBackground: Color { Color.brandAccessibilityBackground }

    /// Brand accessibility secondary color
    static var brandAccessibilitySecondary: Color { Color.brandAccessibilitySecondary }
}

// MARK: - Color Extensions for System Appearance

extension Color {
    /// Create adaptive color that responds to light/dark mode
    static func adaptive(light: Color, dark: Color) -> Color {
        Color(UIColor { traitCollection in
            traitCollection.userInterfaceStyle == .dark ? UIColor(dark) : UIColor(light)
        })
    }
}

// MARK: - Preview Support

#if DEBUG
struct DesignSystemPreview: View {
    var body: some View {
        ScrollView {
            VStack(spacing: DesignSystem.spacing.large) {
                // Brand Colors Preview
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Brand Colors")
                        .font(DesignSystem.typography.title2)
                    
                    VStack(spacing: DesignSystem.spacing.small) {
                        HStack(spacing: DesignSystem.spacing.small) {
                            VStack {
                                Rectangle()
                                    .fill(DesignSystem.brandColors.persianPurple)
                                    .frame(width: 50, height: 50)
                                    .cornerRadius(CornerRadius.small)
                                Text("Persian Purple")
                                    .font(DesignSystem.typography.caption)
                            }
                            
                            VStack {
                                Rectangle()
                                    .fill(DesignSystem.brandColors.orchid)
                                    .frame(width: 50, height: 50)
                                    .cornerRadius(CornerRadius.small)
                                Text("Orchid")
                                    .font(DesignSystem.typography.caption)
                            }
                            
                            VStack {
                                Rectangle()
                                    .fill(DesignSystem.brandColors.frenchLilac)
                                    .frame(width: 50, height: 50)
                                    .cornerRadius(CornerRadius.small)
                                Text("French Lilac")
                                    .font(DesignSystem.typography.caption)
                            }
                        }
                        
                        HStack(spacing: DesignSystem.spacing.small) {
                            VStack {
                                Rectangle()
                                    .fill(DesignSystem.brandColors.amber)
                                    .frame(width: 50, height: 50)
                                    .cornerRadius(CornerRadius.small)
                                Text("Amber")
                                    .font(DesignSystem.typography.caption)
                            }
                            
                            VStack {
                                Rectangle()
                                    .fill(DesignSystem.brandColors.alabaster)
                                    .frame(width: 50, height: 50)
                                    .cornerRadius(CornerRadius.small)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: CornerRadius.small)
                                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                                Text("Alabaster")
                                    .font(DesignSystem.typography.caption)
                            }
                        }
                        
                        // Gradient Preview
                        VStack {
                            Rectangle()
                                .fill(DesignSystem.brandColors.primaryGradient)
                                .frame(height: 30)
                                .cornerRadius(CornerRadius.small)
                            Text("Primary Gradient")
                                .font(DesignSystem.typography.caption)
                        }
                        
                        VStack {
                            Rectangle()
                                .fill(DesignSystem.brandColors.backgroundGradient)
                                .frame(height: 30)
                                .cornerRadius(CornerRadius.small)
                            Text("Background Gradient")
                                .font(DesignSystem.typography.caption)
                        }
                    }
                }
                
                // Typography Preview
                VStack(alignment: .leading, spacing: DesignSystem.spacing.small) {
                    Text("Typography System")
                        .font(DesignSystem.typography.title2)
                    
                    VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
                        Group {
                            Text("Heading 1 - Main Screen Titles")
                                .font(DesignSystem.typography.heading1)
                                .foregroundColor(.brandPersianPurple)
                            
                            Text("Heading 2 - Section Headers")
                                .font(DesignSystem.typography.heading2)
                                .foregroundColor(.brandOrchid)
                            
                            Text("Body Primary - Main content text with Dynamic Type support")
                                .font(DesignSystem.typography.bodyPrimary)
                            
                            Text("Body Secondary - Supporting content")
                                .font(DesignSystem.typography.bodySecondary)
                                .foregroundColor(DesignSystem.colors.textSecondary)
                            
                            Text("Caption Primary - Metadata and timestamps")
                                .font(DesignSystem.typography.captionPrimary)
                                .foregroundColor(DesignSystem.colors.textSecondary)
                            
                            Text("Caption Secondary - Fine details")
                                .font(DesignSystem.typography.captionSecondary)
                                .foregroundColor(DesignSystem.colors.textTertiary)
                        }
                        
                        Divider()
                            .padding(.vertical, DesignSystem.spacing.xSmall)
                        
                        Group {
                            Text("Button Label")
                                .font(DesignSystem.typography.buttonLabel)
                                .padding(.horizontal, DesignSystem.spacing.buttonPaddingHorizontal)
                                .padding(.vertical, DesignSystem.spacing.buttonPaddingVertical)
                                .brandPrimaryButtonStyle()
                            
                            Text("Tab Bar Label")
                                .font(DesignSystem.typography.tabBarLabel)
                                .foregroundColor(.brandOrchid)
                            
                            Text("Navigation Title")
                                .font(DesignSystem.typography.navigationTitle)
                                .foregroundColor(.brandPersianPurple)
                        }
                    }
                }
                
                // Spacing System Preview
                VStack(alignment: .leading, spacing: DesignSystem.spacing.small) {
                    Text("8pt Grid Spacing System")
                        .font(DesignSystem.typography.title2)
                    
                    VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
                        HStack {
                            Text("Micro (4pt)")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Rectangle()
                                .fill(Color.brandAmber)
                                .frame(width: DesignSystem.spacing.micro, height: 20)
                        }
                        
                        HStack {
                            Text("XSmall (8pt)")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Rectangle()
                                .fill(Color.brandAmber)
                                .frame(width: DesignSystem.spacing.xSmall, height: 20)
                        }
                        
                        HStack {
                            Text("Small (16pt)")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Rectangle()
                                .fill(Color.brandAmber)
                                .frame(width: DesignSystem.spacing.small, height: 20)
                        }
                        
                        HStack {
                            Text("Medium (24pt)")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Rectangle()
                                .fill(Color.brandAmber)
                                .frame(width: DesignSystem.spacing.medium, height: 20)
                        }
                        
                        HStack {
                            Text("Large (32pt)")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Rectangle()
                                .fill(Color.brandAmber)
                                .frame(width: DesignSystem.spacing.large, height: 20)
                        }
                    }
                    
                    Divider()
                        .padding(.vertical, DesignSystem.spacing.xSmall)
                    
                    Text("Component Spacing Examples")
                        .font(DesignSystem.typography.heading3)
                        .foregroundColor(.brandOrchid)
                    
                    VStack(spacing: DesignSystem.spacing.xSmall) {
                        HStack {
                            Text("Card Padding:")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Text("\(Int(DesignSystem.spacing.cardPadding))pt")
                                .font(DesignSystem.typography.captionPrimary)
                                .foregroundColor(.brandPersianPurple)
                        }
                        
                        HStack {
                            Text("Button Padding:")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Text("\(Int(DesignSystem.spacing.buttonPaddingHorizontal))pt × \(Int(DesignSystem.spacing.buttonPaddingVertical))pt")
                                .font(DesignSystem.typography.captionPrimary)
                                .foregroundColor(.brandPersianPurple)
                        }
                        
                        HStack {
                            Text("Touch Target:")
                                .font(DesignSystem.typography.captionPrimary)
                            Spacer()
                            Text("\(Int(DesignSystem.spacing.minimumTouchTarget))pt")
                                .font(DesignSystem.typography.captionPrimary)
                                .foregroundColor(.brandAmber)
                        }
                    }
                }
                
                // Card Preview
                VStack {
                    Text("Card Example")
                        .font(DesignSystem.typography.headline)
                        .padding()
                }
                .cardStyle()
            }
            .padding()
        }
        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
    }
}

#Preview {
    DesignSystemPreview()
}
#endif
