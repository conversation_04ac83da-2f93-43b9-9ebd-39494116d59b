//
//  ModernEffects.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

// MARK: - Modern Design Effects Extension
extension DesignSystem {
    
    /// Modern design effects for enhanced visual appeal
    struct ModernEffects {
        
        // MARK: - Glassmorphism Effects
        
        /// Subtle glassmorphism background with blur and transparency
        static let glassmorphismBackground = Material.ultraThinMaterial
        
        /// Medium glassmorphism for cards and overlays
        static let glassmorphismCard = Material.thinMaterial
        
        /// Strong glassmorphism for modals and prominent elements
        static let glassmorphismModal = Material.regularMaterial
        
        /// Custom glassmorphism with brand colors
        static func brandGlassmorphism(opacity: Double = 0.1) -> some ShapeStyle {
            return AnyShapeStyle(
                LinearGradient(
                    gradient: Gradient(colors: [
                        DesignSystem.brandColors.adaptivePrimary.opacity(opacity),
                        DesignSystem.brandColors.adaptiveSecondary.opacity(opacity * 0.7),
                        DesignSystem.brandColors.adaptiveTertiary.opacity(opacity * 0.5)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        
        // MARK: - Modern Shadows
        
        /// Subtle elevation shadow for cards
        static let cardShadow = Shadow(
            color: DesignSystem.brandColors.adaptivePrimary.opacity(0.08),
            radius: 8,
            x: 0,
            y: 2
        )
        
        /// Medium elevation shadow for floating elements
        static let floatingShadow = Shadow(
            color: DesignSystem.brandColors.adaptivePrimary.opacity(0.12),
            radius: 16,
            x: 0,
            y: 4
        )
        
        /// Strong elevation shadow for modals and overlays
        static let modalShadow = Shadow(
            color: DesignSystem.brandColors.adaptivePrimary.opacity(0.16),
            radius: 24,
            x: 0,
            y: 8
        )
        
        /// Dynamic shadow that adapts to brand colors
        static func brandShadow(elevation: ShadowElevation = .medium) -> Shadow {
            switch elevation {
            case .subtle:
                return Shadow(
                    color: DesignSystem.brandColors.adaptivePrimary.opacity(0.06),
                    radius: 4,
                    x: 0,
                    y: 1
                )
            case .medium:
                return Shadow(
                    color: DesignSystem.brandColors.adaptivePrimary.opacity(0.1),
                    radius: 12,
                    x: 0,
                    y: 3
                )
            case .strong:
                return Shadow(
                    color: DesignSystem.brandColors.adaptivePrimary.opacity(0.15),
                    radius: 20,
                    x: 0,
                    y: 6
                )
            case .dramatic:
                return Shadow(
                    color: DesignSystem.brandColors.adaptivePrimary.opacity(0.2),
                    radius: 32,
                    x: 0,
                    y: 12
                )
            }
        }
        
        // MARK: - Enhanced Gradients
        
        /// Subtle mesh gradient for backgrounds
        static let meshGradient = LinearGradient(
            gradient: Gradient(colors: [
                DesignSystem.brandColors.adaptiveBackground,
                DesignSystem.brandColors.adaptiveBackground.opacity(0.95),
                DesignSystem.brandColors.adaptiveSurface.opacity(0.8)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        /// Animated shimmer gradient for loading states
        static let shimmerGradient = LinearGradient(
            gradient: Gradient(colors: [
                Color.clear,
                DesignSystem.brandColors.adaptivePrimary.opacity(0.1),
                Color.clear
            ]),
            startPoint: .leading,
            endPoint: .trailing
        )
        
        /// Radial gradient for spotlight effects
        static let spotlightGradient = RadialGradient(
            gradient: Gradient(colors: [
                DesignSystem.brandColors.adaptivePrimary.opacity(0.15),
                DesignSystem.brandColors.adaptiveSecondary.opacity(0.08),
                Color.clear
            ]),
            center: .center,
            startRadius: 50,
            endRadius: 200
        )
        
        // MARK: - Texture Effects
        
        /// Subtle noise texture overlay
        static let noiseTexture = Color.black.opacity(0.02)
        
        /// Paper-like texture for cards
        static let paperTexture = LinearGradient(
            gradient: Gradient(colors: [
                Color.white.opacity(0.01),
                Color.black.opacity(0.005),
                Color.white.opacity(0.01)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        // MARK: - Blur Effects
        
        /// Light blur for subtle effects
        static let lightBlur: CGFloat = 2
        
        /// Medium blur for overlays
        static let mediumBlur: CGFloat = 8
        
        /// Strong blur for backgrounds
        static let strongBlur: CGFloat = 16
        
        /// Dynamic blur for modals
        static let modalBlur: CGFloat = 24
    }
}

// MARK: - Shadow Elevation Enum
enum ShadowElevation {
    case subtle
    case medium
    case strong
    case dramatic
}

// MARK: - Shadow Helper Struct
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Modifiers for Modern Effects

extension View {
    
    /// Apply glassmorphism effect to any view
    func glassmorphism(style: Material = DesignSystem.ModernEffects.glassmorphismCard) -> some View {
        self
            .background(style)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.medium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.clear,
                                Color.black.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    /// Apply modern shadow with elevation
    func modernShadow(elevation: ShadowElevation = .medium) -> some View {
        let shadow = DesignSystem.ModernEffects.brandShadow(elevation: elevation)
        return self.shadow(
            color: shadow.color,
            radius: shadow.radius,
            x: shadow.x,
            y: shadow.y
        )
    }
    
    /// Apply brand-colored glassmorphism
    func brandGlassmorphism(opacity: Double = 0.1, cornerRadius: CGFloat = CornerRadius.medium) -> some View {
        self
            .background(DesignSystem.ModernEffects.brandGlassmorphism(opacity: opacity))
            .cornerRadius(cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        DesignSystem.brandColors.adaptivePrimary.opacity(0.2),
                        lineWidth: 0.5
                    )
            )
    }
    
    /// Apply shimmer loading effect
    func shimmerEffect(isLoading: Bool = true) -> some View {
        self
            .overlay(
                Rectangle()
                    .fill(DesignSystem.ModernEffects.shimmerGradient)
                    .opacity(isLoading ? 1 : 0)
                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: false), value: isLoading)
            )
    }
    
    /// Apply subtle texture overlay
    func textureOverlay() -> some View {
        self
            .overlay(
                Rectangle()
                    .fill(DesignSystem.ModernEffects.paperTexture)
                    .blendMode(.overlay)
                    .opacity(0.3)
            )
    }
}

// MARK: - Modern Card Style
struct ModernCardStyle: ViewModifier {
    let elevation: ShadowElevation
    let hasGlassmorphism: Bool
    
    func body(content: Content) -> some View {
        content
            .padding()
            .background(
                Group {
                    if hasGlassmorphism {
                        Rectangle()
                            .fill(DesignSystem.ModernEffects.glassmorphismCard)
                    } else {
                        Color.brandCardBackground
                    }
                }
            )
            .cornerRadius(CornerRadius.large)
            .modernShadow(elevation: elevation)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.large)
                    .stroke(Color.brandCardBorder, lineWidth: 0.5)
            )
    }
}

extension View {
    func modernCardStyle(elevation: ShadowElevation = .medium, glassmorphism: Bool = false) -> some View {
        self.modifier(ModernCardStyle(elevation: elevation, hasGlassmorphism: glassmorphism))
    }
}
