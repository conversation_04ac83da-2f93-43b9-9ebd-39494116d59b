//
//  ToolbarMenuButton.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/26.
//

import SwiftUI

/// Toolbar menu button with elegant custom dropdown navigation options
struct ToolbarMenuButton: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        // Menu button
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                navigationCoordinator.showingDropdownMenu.toggle()
            }
            HapticFeedbackManager.shared.buttonPressed()
        }) {
                ZStack {
                    // Elegant glassmorphism background
                    RoundedRectangle(cornerRadius: 10)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.brandOrchid.opacity(0.3),
                                            Color.brandFrenchLilac.opacity(0.2)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                        .frame(width: 36, height: 36)
                        .shadow(
                            color: Color.brandPersianPurple.opacity(0.1),
                            radius: 4,
                            x: 0,
                            y: 2
                        )

                    Image(systemName: "line.3.horizontal")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.brandOrchid)
                        .rotationEffect(.degrees(navigationCoordinator.showingDropdownMenu ? 90 : 0))
                        .animation(.easeInOut(duration: 0.2), value: navigationCoordinator.showingDropdownMenu)
                }
            }
            .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("Navigation Menu")
        .accessibilityHint("Opens navigation menu with app sections")
    }
}

// MARK: - Preview

// MARK: - Preview

#Preview {
    NavigationView {
        VStack {
            Text("Sample Content")
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                ToolbarMenuButton()
            }
        }
    }
    .environmentObject(NavigationCoordinator())
}
