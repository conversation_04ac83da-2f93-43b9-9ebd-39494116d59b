//
//  BrandTabBar.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

/// Custom tab bar with brand styling and smooth animations
struct BrandTabBar: View {
    @Binding var selectedTab: AppTab
    let badgeCount: Int
    
    init(selectedTab: Binding<AppTab>, badgeCount: Int = 0) {
        self._selectedTab = selectedTab
        self.badgeCount = badgeCount
    }
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(AppTab.allCases, id: \.self) { tab in
                BrandTabBarItem(
                    tab: tab,
                    isSelected: selectedTab == tab,
                    badgeCount: tab == .history ? badgeCount : 0
                ) {
                    withAnimation(AnimationManager.accessibleTabSwitch) {
                        selectedTab = tab
                        HapticFeedbackManager.shared.selectionChanged()
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .frame(height: DesignSystem.spacing.tabBarHeight)
        .background(
            Rectangle()
                .fill(.ultraThinMaterial)
                .overlay(
                    Rectangle()
                        .fill(DesignSystem.brandColors.adaptiveBackground.opacity(0.8))
                )
        )
        .overlay(
            Rectangle()
                .fill(Color.brandCardBorder.opacity(0.3))
                .frame(height: 0.5),
            alignment: .top
        )
    }
}

/// Individual tab bar item with brand styling
private struct BrandTabBarItem: View {
    let tab: AppTab
    let isSelected: Bool
    let badgeCount: Int
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.spacing.micro) {
                ZStack {
                    // Tab icon with brand colors
                    Image(systemName: isSelected ? tab.selectedIconName : tab.iconName)
                        .font(.system(size: 20, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(iconColor)
                        .scaleEffect(isPressed ? 0.9 : 1.0)
                        .animation(AnimationManager.accessibleButtonPress, value: isPressed)
                    
                    // Badge for notifications
                    if badgeCount > 0 {
                        BrandBadge(
                            badgeCount > 99 ? "99+" : "\(badgeCount)",
                            style: .notification,
                            size: .small
                        )
                        .offset(x: 12, y: -8)
                    }
                }
                .frame(height: 24)
                
                // Tab label
                Text(tab.title)
                    .font(DesignSystem.typography.tabBarLabel)
                    .foregroundColor(labelColor)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: DesignSystem.spacing.tabBarHeight)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .frame(minHeight: 44)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityAddTraits(isSelected ? [.isButton, .isSelected] : .isButton)
        .accessibilityIdentifier("tab_\(tab.rawValue)")
    }
    
    // MARK: - Computed Properties
    
    private var iconColor: Color {
        if isSelected {
            return .brandPrimary
        } else {
            return .brandSecondary.opacity(0.7)
        }
    }
    
    private var labelColor: Color {
        if isSelected {
            return .brandPrimary
        } else {
            return .brandSecondary.opacity(0.6)
        }
    }
    
    private var accessibilityLabel: String {
        var label = tab.title
        if badgeCount > 0 {
            label += ", \(badgeCount) new items"
        }
        return label
    }
    
    private var accessibilityHint: String {
        if isSelected {
            return "Currently selected"
        } else {
            return "Double tap to switch to \(tab.title)"
        }
    }
}

// MARK: - Preview

#Preview("Brand Tab Bar") {
    VStack {
        Spacer()
        
        BrandTabBar(
            selectedTab: .constant(.record),
            badgeCount: 3
        )
    }
    .background(DesignSystem.brandColors.adaptiveBackground)
}

#Preview("All Tab States") {
    VStack(spacing: 20) {
        // Record selected
        BrandTabBar(
            selectedTab: .constant(.record),
            badgeCount: 0
        )
        
        // History selected with badge
        BrandTabBar(
            selectedTab: .constant(.history),
            badgeCount: 5
        )
        
        // Settings selected
        BrandTabBar(
            selectedTab: .constant(.settings),
            badgeCount: 0
        )
        
        Spacer()
    }
    .background(DesignSystem.brandColors.adaptiveBackground)
}