//
//  BrandComponents.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

// MARK: - Brand Button Component

struct BrandButton: View {
    let title: String
    let action: () -> Void
    let style: BrandButtonStyle
    let size: BrandButtonSize
    let isEnabled: Bool
    let isLoading: Bool
    
    @State private var isPressed = false
    
    init(
        _ title: String,
        style: BrandButtonStyle = .primary,
        size: BrandButtonSize = .medium,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.action = action
        self.style = style
        self.size = size
        self.isEnabled = isEnabled
        self.isLoading = isLoading
    }
    
    var body: some View {
        Button(action: {
            if isEnabled && !isLoading {
                HapticFeedbackManager.shared.buttonPressed()
                action()
            }
        }) {
            HStack(spacing: DesignSystem.spacing.xSmall) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(style.foregroundColor)
                } else {
                    Text(title)
                        .font(size.font)
                        .fontWeight(.semibold)
                }
            }
            .frame(minWidth: size.minWidth, minHeight: size.minHeight)
            .padding(.horizontal, size.horizontalPadding)
            .padding(.vertical, size.verticalPadding)
            .background(backgroundView)
            .foregroundColor(style.foregroundColor)
            .cornerRadius(CornerRadius.button)
            .overlay(overlayView)
            .shadow(
                color: style.shadowColor,
                radius: isPressed ? 4 : 8,
                x: 0,
                y: isPressed ? 2 : 4
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .opacity(isEnabled ? 1.0 : 0.6)
            .animation(AnimationManager.accessibleButtonPress, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .disabled(!isEnabled || isLoading)
        .accessibleTouchTarget(minSize: size.minHeight)
        .accessibilityLabel(title)
        .accessibilityHint(isLoading ? "Loading, please wait" : "Double tap to activate")
        .accessibilityValue(isLoading ? "Loading" : "")
        .accessibilityAddTraits(.isButton)
        .accessibilityIdentifier("brand_button_\(title.lowercased().replacingOccurrences(of: " ", with: "_"))")
        .accessibilityRemoveTraits(isEnabled ? [] : .isButton)
        .accessibilityAddTraits(isEnabled ? .isButton : [])
    }
    
    @ViewBuilder
    private var backgroundView: some View {
        switch style {
        case .primary:
            DesignSystem.brandColors.adaptivePrimaryGradient
        case .secondary:
            Color.brandTertiary
        case .tertiary:
            Color.clear
        case .destructive:
            Color.red
        }
    }
    
    @ViewBuilder
    private var overlayView: some View {
        switch style {
        case .secondary, .tertiary:
            RoundedRectangle(cornerRadius: CornerRadius.button)
                .stroke(style.borderColor, lineWidth: style.borderWidth)
        default:
            EmptyView()
        }
    }
}

// MARK: - Brand Button Styles

enum BrandButtonStyle {
    case primary
    case secondary
    case tertiary
    case destructive
    
    var foregroundColor: Color {
        switch self {
        case .primary, .destructive:
            return .white
        case .secondary:
            return .brandPrimary
        case .tertiary:
            return .brandSecondary
        }
    }
    
    var borderColor: Color {
        switch self {
        case .secondary:
            return .brandSecondary.opacity(0.3)
        case .tertiary:
            return .brandSecondary
        default:
            return .clear
        }
    }
    
    var borderWidth: CGFloat {
        switch self {
        case .secondary, .tertiary:
            return 1
        default:
            return 0
        }
    }
    
    var shadowColor: Color {
        switch self {
        case .primary:
            return .brandPrimary.opacity(0.3)
        case .secondary:
            return .brandSecondary.opacity(0.2)
        case .tertiary:
            return .brandSecondary.opacity(0.1)
        case .destructive:
            return .red.opacity(0.3)
        }
    }
}

// MARK: - Brand Button Sizes

enum BrandButtonSize {
    case small
    case medium
    case large
    
    var font: Font {
        switch self {
        case .small:
            return DesignSystem.typography.footnote
        case .medium:
            return DesignSystem.typography.buttonLabel
        case .large:
            return DesignSystem.typography.headline
        }
    }
    
    var minWidth: CGFloat {
        switch self {
        case .small:
            return 80
        case .medium:
            return 120
        case .large:
            return 160
        }
    }
    
    var minHeight: CGFloat {
        return max(DesignSystem.spacing.minimumTouchTarget, 32)
    }
    
    var horizontalPadding: CGFloat {
        switch self {
        case .small:
            return DesignSystem.spacing.small
        case .medium:
            return DesignSystem.spacing.buttonPaddingHorizontal
        case .large:
            return DesignSystem.spacing.large
        }
    }
    
    var verticalPadding: CGFloat {
        switch self {
        case .small:
            return DesignSystem.spacing.micro
        case .medium:
            return DesignSystem.spacing.buttonPaddingVertical
        case .large:
            return DesignSystem.spacing.medium
        }
    }
}

// MARK: - Brand Card Component

struct BrandCard<Content: View>: View {
    let content: Content
    let style: BrandCardStyle
    let padding: CGFloat
    let cornerRadius: CGFloat
    
    init(
        style: BrandCardStyle = .default,
        padding: CGFloat = DesignSystem.spacing.cardPadding,
        cornerRadius: CGFloat = CornerRadius.card,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.style = style
        self.padding = padding
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        content
            .padding(padding)
            .background(style.backgroundColor)
            .cornerRadius(cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(style.borderColor, lineWidth: style.borderWidth)
            )
            .shadow(
                color: style.shadowColor,
                radius: style.shadowRadius,
                x: style.shadowX,
                y: style.shadowY
            )
            .accessibilityLabel("Card content")
            .accessibilityIdentifier("brand_card")
            .accessibilityElement(children: .contain)
    }
}

// MARK: - Brand Card Styles

enum BrandCardStyle {
    case `default`
    case elevated
    case outlined
    case subtle
    
    var backgroundColor: Color {
        switch self {
        case .default, .elevated:
            return .brandCardBackground
        case .outlined:
            return .clear
        case .subtle:
            return .brandSurface
        }
    }
    
    var borderColor: Color {
        switch self {
        case .default, .subtle:
            return .brandCardBorder
        case .elevated:
            return .brandCardBorder.opacity(0.5)
        case .outlined:
            return .brandSecondary
        }
    }
    
    var borderWidth: CGFloat {
        switch self {
        case .default, .subtle:
            return 1
        case .elevated:
            return 0.5
        case .outlined:
            return 1.5
        }
    }
    
    var shadowColor: Color {
        switch self {
        case .default:
            return .brandPrimary.opacity(0.1)
        case .elevated:
            return .brandPrimary.opacity(0.15)
        case .outlined, .subtle:
            return .brandSecondary.opacity(0.05)
        }
    }
    
    var shadowRadius: CGFloat {
        switch self {
        case .default:
            return 8
        case .elevated:
            return 16
        case .outlined, .subtle:
            return 4
        }
    }
    
    var shadowX: CGFloat { 0 }
    
    var shadowY: CGFloat {
        switch self {
        case .default:
            return 2
        case .elevated:
            return 4
        case .outlined, .subtle:
            return 1
        }
    }
}

// MARK: - Brand Text Field Component

struct BrandTextField: View {
    @Binding var text: String
    let placeholder: String
    let style: BrandTextFieldStyle
    let isSecure: Bool
    let keyboardType: UIKeyboardType
    let autocapitalization: TextInputAutocapitalization
    let isEnabled: Bool
    
    @FocusState private var isFocused: Bool
    @State private var isSecureVisible = false
    
    init(
        text: Binding<String>,
        placeholder: String,
        style: BrandTextFieldStyle = .default,
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        autocapitalization: TextInputAutocapitalization = .sentences,
        isEnabled: Bool = true
    ) {
        self._text = text
        self.placeholder = placeholder
        self.style = style
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.autocapitalization = autocapitalization
        self.isEnabled = isEnabled
    }
    
    var body: some View {
        HStack(spacing: DesignSystem.spacing.small) {
            textFieldView
            
            if isSecure {
                Button(action: {
                    isSecureVisible.toggle()
                    HapticFeedbackManager.shared.selectionChanged()
                }) {
                    Image(systemName: isSecureVisible ? "eye.slash" : "eye")
                        .foregroundColor(.brandSecondary)
                        .font(.system(size: 16, weight: .medium))
                }
                .frame(minWidth: 44, minHeight: 44)
                .accessibilityLabel(isSecureVisible ? "Hide password" : "Show password")
                .accessibilityHint("Double tap to toggle password visibility")
                .accessibilityAddTraits(.isButton)
                .accessibilityIdentifier("password_visibility_toggle")
            }
        }
        .padding(.horizontal, DesignSystem.spacing.textFieldPadding)
        .padding(.vertical, DesignSystem.spacing.small)
        .frame(minHeight: max(DesignSystem.spacing.textFieldHeight, 44))
        .background(style.backgroundColor)
        .cornerRadius(CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: CornerRadius.medium)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .shadow(
            color: style.shadowColor,
            radius: isFocused ? 4 : 2,
            x: 0,
            y: isFocused ? 2 : 1
        )
        .animation(AnimationManager.accessibleButtonPress, value: isFocused)
        .disabled(!isEnabled)
        .opacity(isEnabled ? 1.0 : 0.6)
    }
    
    @ViewBuilder
    private var textFieldView: some View {
        if isSecure && !isSecureVisible {
            SecureField(placeholder, text: $text)
                .font(DesignSystem.typography.bodyPrimary)
                .foregroundColor(.primary)
                .keyboardType(keyboardType)
                .textInputAutocapitalization(autocapitalization)
                .disableAutocorrection(false)
                .focused($isFocused)
                .accessibilityLabel(placeholder)
                .accessibilityHint("Secure text input field")
                .accessibilityAddTraits([.isSearchField])
                .accessibilityIdentifier("brand_secure_textfield_\(placeholder.lowercased().replacingOccurrences(of: " ", with: "_"))")
        } else {
            TextField(placeholder, text: $text)
                .font(DesignSystem.typography.bodyPrimary)
                .foregroundColor(.primary)
                .keyboardType(keyboardType)
                .textInputAutocapitalization(autocapitalization)
                .disableAutocorrection(false)
                .focused($isFocused)
                .accessibilityLabel(placeholder)
                .accessibilityHint("Text input field")
                .accessibilityAddTraits(.isSearchField)
                .accessibilityIdentifier("brand_textfield_\(placeholder.lowercased().replacingOccurrences(of: " ", with: "_"))")
        }
    }
    
    private var borderColor: Color {
        if !isEnabled {
            return style.disabledBorderColor
        } else if isFocused {
            return style.focusedBorderColor
        } else {
            return style.defaultBorderColor
        }
    }
    
    private var borderWidth: CGFloat {
        isFocused ? 2 : 1
    }
}

// MARK: - Brand Text Field Styles

enum BrandTextFieldStyle {
    case `default`
    case outlined
    case filled
    
    var backgroundColor: Color {
        switch self {
        case .default:
            return .brandCardBackground
        case .outlined:
            return .clear
        case .filled:
            return .brandSurface
        }
    }
    
    var defaultBorderColor: Color {
        switch self {
        case .default:
            return .brandCardBorder
        case .outlined:
            return .brandSecondary.opacity(0.5)
        case .filled:
            return .clear
        }
    }
    
    var focusedBorderColor: Color {
        return .brandSecondary
    }
    
    var disabledBorderColor: Color {
        return .brandCardBorder.opacity(0.5)
    }
    
    var shadowColor: Color {
        return .brandSecondary.opacity(0.1)
    }
}



// MARK: - Brand Toggle Component

struct BrandToggle: View {
    @Binding var isOn: Bool
    let label: String
    let description: String?
    let style: BrandToggleStyle
    let isEnabled: Bool
    
    init(
        _ label: String,
        isOn: Binding<Bool>,
        description: String? = nil,
        style: BrandToggleStyle = .default,
        isEnabled: Bool = true
    ) {
        self.label = label
        self._isOn = isOn
        self.description = description
        self.style = style
        self.isEnabled = isEnabled
    }
    
    var body: some View {
        HStack(spacing: DesignSystem.spacing.medium) {
            VStack(alignment: .leading, spacing: DesignSystem.spacing.micro) {
                Text(label)
                    .font(DesignSystem.typography.bodyPrimary)
                    .foregroundColor(.primary)
                
                if let description = description {
                    Text(description)
                        .font(DesignSystem.typography.captionPrimary)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(BrandToggleStyle.ToggleStyleImpl(
                    style: style,
                    isEnabled: isEnabled
                ))
                .disabled(!isEnabled)
        }
        .padding(.vertical, DesignSystem.spacing.xSmall)
        .contentShape(Rectangle())
        .onTapGesture {
            if isEnabled {
                withAnimation(AnimationManager.accessibleButtonPress) {
                    isOn.toggle()
                }
                HapticFeedbackManager.shared.selectionChanged()
            }
        }
        .opacity(isEnabled ? 1.0 : 0.6)
        .frame(minHeight: 44)
        .accessibilityLabel(label)
        .accessibilityHint(description ?? "Double tap to toggle")
        .accessibilityValue(isOn ? "On" : "Off")
        .accessibilityAddTraits(.isButton)
        .accessibilityIdentifier("brand_toggle_\(label.lowercased().replacingOccurrences(of: " ", with: "_"))")
        .accessibilityElement(children: .combine)
    }
}

// MARK: - Brand Toggle Styles

enum BrandToggleStyle {
    case `default`
    case compact
    
    var onColor: Color {
        return .brandSecondary
    }
    
    var offColor: Color {
        return .brandTertiary
    }
    
    var thumbColor: Color {
        return .brandCardBackground
    }
    
    var size: CGSize {
        switch self {
        case .default:
            return CGSize(width: 50, height: 30)
        case .compact:
            return CGSize(width: 40, height: 24)
        }
    }
    
    struct ToggleStyleImpl: ToggleStyle {
        let style: BrandToggleStyle
        let isEnabled: Bool
        
        func makeBody(configuration: Configuration) -> some View {
            HStack {
                configuration.label
                
                RoundedRectangle(cornerRadius: style.size.height / 2)
                    .fill(configuration.isOn ? style.onColor : style.offColor)
                    .frame(width: style.size.width, height: style.size.height)
                    .overlay(
                        Circle()
                            .fill(style.thumbColor)
                            .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                            .padding(2)
                            .offset(x: configuration.isOn ? (style.size.width - style.size.height) / 2 : -(style.size.width - style.size.height) / 2)
                    )
                    .animation(AnimationManager.accessibleButtonPress, value: configuration.isOn)
                    .onTapGesture {
                        if isEnabled {
                            configuration.isOn.toggle()
                        }
                    }
            }
        }
    }
}

// MARK: - Brand Badge Component

struct BrandBadge: View {
    let text: String
    let style: BrandBadgeStyle
    let size: BrandBadgeSize
    
    init(
        _ text: String,
        style: BrandBadgeStyle = .notification,
        size: BrandBadgeSize = .medium
    ) {
        self.text = text
        self.style = style
        self.size = size
    }

    private var badgeAccessibilityHint: String {
        switch style {
        case .notification:
            return "Notification badge"
        case .success:
            return "Success indicator"
        case .warning:
            return "Warning indicator"
        case .error:
            return "Error indicator"
        case .info:
            return "Information badge"
        case .neutral:
            return "Status badge"
        }
    }

    var body: some View {
        Text(text)
            .font(size.font)
            .fontWeight(.semibold)
            .foregroundColor(style.foregroundColor)
            .padding(.horizontal, size.horizontalPadding)
            .padding(.vertical, size.verticalPadding)
            .background(style.backgroundColor)
            .cornerRadius(size.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: size.cornerRadius)
                    .stroke(style.borderColor, lineWidth: style.borderWidth)
            )
            .shadow(
                color: style.shadowColor,
                radius: 2,
                x: 0,
                y: 1
            )
            .accessibilityLabel("Badge: \(text)")
            .accessibilityHint(badgeAccessibilityHint)
            .accessibilityAddTraits(.isStaticText)
            .accessibilityIdentifier("brand_badge_\(text.lowercased().replacingOccurrences(of: " ", with: "_"))")
    }
}

// MARK: - Brand Badge Styles

enum BrandBadgeStyle {
    case notification
    case success
    case warning
    case error
    case info
    case neutral
    
    var backgroundColor: Color {
        switch self {
        case .notification:
            return .brandAccent
        case .success:
            return .green
        case .warning:
            return .orange
        case .error:
            return .red
        case .info:
            return .brandSecondary
        case .neutral:
            return .brandTertiary
        }
    }
    
    var foregroundColor: Color {
        switch self {
        case .notification, .success, .warning, .error, .info:
            return .white
        case .neutral:
            return .brandPrimary
        }
    }
    
    var borderColor: Color {
        return backgroundColor.opacity(0.3)
    }
    
    var borderWidth: CGFloat {
        switch self {
        case .neutral:
            return 1
        default:
            return 0
        }
    }
    
    var shadowColor: Color {
        return backgroundColor.opacity(0.3)
    }
}

// MARK: - Brand Badge Sizes

enum BrandBadgeSize {
    case small
    case medium
    case large
    
    var font: Font {
        switch self {
        case .small:
            return DesignSystem.typography.caption2
        case .medium:
            return DesignSystem.typography.badgeText
        case .large:
            return DesignSystem.typography.footnote
        }
    }
    
    var horizontalPadding: CGFloat {
        switch self {
        case .small:
            return 6
        case .medium:
            return 8
        case .large:
            return 12
        }
    }
    
    var verticalPadding: CGFloat {
        switch self {
        case .small:
            return 2
        case .medium:
            return 4
        case .large:
            return 6
        }
    }
    
    var cornerRadius: CGFloat {
        switch self {
        case .small:
            return 4
        case .medium:
            return CornerRadius.badge
        case .large:
            return 8
        }
    }
}

// MARK: - Preview Support

#if DEBUG
struct BrandComponentsPreview: View {
    @State private var textFieldText = ""
    @State private var isToggleOn = false
    @State private var isSecureToggleOn = true
    
    var body: some View {
        ScrollView {
            VStack(spacing: DesignSystem.spacing.large) {
                // Brand Buttons
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Brand Buttons")
                        .font(DesignSystem.typography.title2)
                        .foregroundColor(.brandPersianPurple)
                    
                    VStack(spacing: DesignSystem.spacing.small) {
                        BrandButton("Primary Button", style: .primary) {
                            print("Primary button tapped")
                        }
                        
                        BrandButton("Secondary Button", style: .secondary) {
                            print("Secondary button tapped")
                        }
                        
                        BrandButton("Secondary Button", style: .secondary, size: .small) {
                            print("Secondary button tapped")
                        }
                        
                        BrandButton("Tertiary Button", style: .tertiary) {
                            print("Tertiary button tapped")
                        }
                        
                        BrandButton("Loading Button", style: .primary, isLoading: true) {
                            print("Loading button tapped")
                        }
                        
                        BrandButton("Disabled Button", style: .primary, isEnabled: false) {
                            print("Disabled button tapped")
                        }
                    }
                }
                
                // Brand Cards
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Brand Cards")
                        .font(DesignSystem.typography.title2)
                        .foregroundColor(.brandPrimary)
                    
                    VStack(spacing: DesignSystem.spacing.small) {
                        BrandCard(style: .default) {
                            VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
                                Text("Default Card")
                                    .font(DesignSystem.typography.headline)
                                    .foregroundColor(.brandPrimary)
                                Text("This is a default brand card with French Lilac accents.")
                                    .font(DesignSystem.typography.bodySecondary)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        BrandCard(style: .elevated) {
                            VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
                                Text("Elevated Card")
                                    .font(DesignSystem.typography.headline)
                                    .foregroundColor(.brandPrimary)
                                Text("This is an elevated card with enhanced shadows.")
                                    .font(DesignSystem.typography.bodySecondary)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        BrandCard(style: .outlined) {
                            VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
                                Text("Outlined Card")
                                    .font(DesignSystem.typography.headline)
                                    .foregroundColor(.brandOrchid)
                                Text("This is an outlined card with Orchid borders.")
                                    .font(DesignSystem.typography.bodySecondary)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                // Brand Text Fields
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Brand Text Fields")
                        .font(DesignSystem.typography.title2)
                        .foregroundColor(.brandPersianPurple)
                    
                    VStack(spacing: DesignSystem.spacing.small) {
                        BrandTextField(
                            text: $textFieldText,
                            placeholder: "Enter your text here",
                            style: .default
                        )
                        
                        BrandTextField(
                            text: $textFieldText,
                            placeholder: "Outlined text field",
                            style: .outlined
                        )
                        
                        BrandTextField(
                            text: $textFieldText,
                            placeholder: "Filled text field",
                            style: .filled
                        )
                        
                        BrandTextField(
                            text: $textFieldText,
                            placeholder: "Secure text field",
                            style: .default,
                            isSecure: true
                        )
                    }
                }
                
                // Brand Toggles
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Brand Toggles")
                        .font(DesignSystem.typography.title2)
                        .foregroundColor(.brandPersianPurple)
                    
                    VStack(spacing: DesignSystem.spacing.small) {
                        BrandToggle(
                            "Enable notifications",
                            isOn: $isToggleOn,
                            description: "Receive push notifications for new transcriptions"
                        )
                        
                        BrandToggle(
                            "Auto-save transcriptions",
                            isOn: $isSecureToggleOn,
                            style: .compact
                        )
                        
                        BrandToggle(
                            "Disabled toggle",
                            isOn: .constant(false),
                            description: "This toggle is disabled",
                            isEnabled: false
                        )
                    }
                }
                
                // Brand Badges
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Brand Badges")
                        .font(DesignSystem.typography.title2)
                        .foregroundColor(.brandPersianPurple)
                    
                    VStack(spacing: DesignSystem.spacing.small) {
                        HStack(spacing: DesignSystem.spacing.small) {
                            BrandBadge("New", style: .notification, size: .small)
                            BrandBadge("Success", style: .success, size: .medium)
                            BrandBadge("Warning", style: .warning, size: .large)
                        }
                        
                        HStack(spacing: DesignSystem.spacing.small) {
                            BrandBadge("Error", style: .error, size: .medium)
                            BrandBadge("Info", style: .info, size: .medium)
                            BrandBadge("Neutral", style: .neutral, size: .medium)
                        }
                        
                        HStack(spacing: DesignSystem.spacing.small) {
                            BrandBadge("3", style: .notification, size: .small)
                            BrandBadge("12", style: .notification, size: .medium)
                            BrandBadge("99+", style: .notification, size: .large)
                        }
                    }
                }

                // Modern Effects Showcase
                VStack(alignment: .leading, spacing: DesignSystem.spacing.medium) {
                    Text("Modern Effects")
                        .font(DesignSystem.typography.title2)
                        .foregroundColor(.brandPrimary)

                    VStack(spacing: DesignSystem.spacing.medium) {
                        // Modern button styles
                        VStack(spacing: DesignSystem.spacing.small) {
                            Text("Modern Button Styles")
                                .font(DesignSystem.typography.headline)
                                .foregroundColor(.brandPrimary)

                            HStack(spacing: DesignSystem.spacing.small) {
                                Button("Glassmorphism") {}
                                    .glassmorphismButtonStyle()

                                Button("Elevated") {}
                                    .elevatedButtonStyle()

                                Button("Gradient") {}
                                    .gradientButtonStyle(glow: true)
                            }

                            HStack(spacing: DesignSystem.spacing.small) {
                                Button("Neumorphism") {}
                                    .neumorphismButtonStyle()

                                Button("FAB") {
                                    // Action
                                }
                                .floatingActionButtonStyle(size: 44)
                            }
                        }

                        // Modern cards
                        VStack(spacing: DesignSystem.spacing.small) {
                            Text("Modern Card Variants")
                                .font(DesignSystem.typography.headline)
                                .foregroundColor(.brandPrimary)

                            ModernCard(style: .glassmorphism) {
                                Text("Glassmorphism Card with subtle blur effects")
                                    .font(DesignSystem.typography.body)
                                    .foregroundColor(.brandPrimary)
                            }

                            ModernCard(style: .elevated) {
                                Text("Elevated Card with dramatic shadows")
                                    .font(DesignSystem.typography.body)
                                    .foregroundColor(.brandPrimary)
                            }

                            ModernCard(style: .gradient) {
                                Text("Gradient Card with adaptive backgrounds")
                                    .font(DesignSystem.typography.body)
                                    .foregroundColor(.brandPrimary)
                            }
                        }
                    }
                }
            }
            .padding(DesignSystem.spacing.screenPadding)
        }
        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
    }
}

struct BrandComponentsPreview_Previews: PreviewProvider {
    static var previews: some View {
        BrandComponentsPreview()
            .preferredColorScheme(.light)
            .previewDisplayName("Light Mode")
        
        BrandComponentsPreview()
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Mode")
    }
}
#endif
