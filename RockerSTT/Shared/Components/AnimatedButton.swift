//
//  AnimatedButton.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/15.
//

import SwiftUI

// MARK: - Button Style Enum

enum AnimatedButtonStyle {
    case primary
    case secondary
    case destructive
}

// MARK: - Animated Button Component

struct AnimatedButton: View {
    // MARK: - Properties
    
    let title: String
    let style: AnimatedButtonStyle
    let action: () -> Void
    
    @State private var isPressed = false
    @State private var isDisabled = false
    
    // MARK: - Computed Properties
    
    private var backgroundColor: Color {
        switch style {
        case .primary:
            return isDisabled ? 
                DesignSystem.colors.secondaryMedium : 
                DesignSystem.colors.primaryBlue
        case .secondary:
            return isDisabled ? 
                DesignSystem.colors.secondaryLight : 
                DesignSystem.colors.cardBackground
        case .destructive:
            return isDisabled ? 
                DesignSystem.colors.secondaryMedium : 
                DesignSystem.colors.recordingRed
        }
    }
    
    private var foregroundColor: Color {
        switch style {
        case .primary:
            return isDisabled ? 
                DesignSystem.colors.textTertiary : 
                Color.white
        case .secondary:
            return isDisabled ? 
                DesignSystem.colors.textTertiary : 
                DesignSystem.colors.textPrimary
        case .destructive:
            return isDisabled ? 
                DesignSystem.colors.textTertiary : 
                Color.white
        }
    }
    
    private var shadowColor: Color {
        switch style {
        case .primary:
            return DesignSystem.colors.primaryBlue.opacity(0.3)
        case .secondary:
            return Color.black.opacity(0.1)
        case .destructive:
            return DesignSystem.colors.recordingRed.opacity(0.3)
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            performAction()
        }) {
            Text(title)
                .font(.system(size: 17, weight: .semibold))
                .foregroundColor(foregroundColor)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(backgroundColor)
                .cornerRadius(10)
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .shadow(
                    color: isPressed ? Color.clear : shadowColor,
                    radius: isPressed ? 0 : 8,
                    x: 0,
                    y: isPressed ? 0 : 4
                )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeOut(duration: 0.15)) {
                isPressed = pressing
            }
        }, perform: {})
        .animation(.easeOut(duration: 0.15), value: isPressed)
    }
    
    // MARK: - Methods
    
    private func performAction() {
        // Trigger haptic feedback
        triggerHapticFeedback()
        
        // Perform the action
        action()
    }
    
    private func triggerHapticFeedback() {
        HapticPattern.medium.trigger()
    }
    
    // MARK: - Public Methods
    
    func disabled(_ disabled: Bool) -> AnimatedButton {
        let button = self
        button.isDisabled = disabled
        return button
    }
}

// MARK: - Convenience Initializers

extension AnimatedButton {
    /// Create a primary button
    static func primary(_ title: String, action: @escaping () -> Void) -> AnimatedButton {
        AnimatedButton(title: title, style: .primary, action: action)
    }
    
    /// Create a secondary button
    static func secondary(_ title: String, action: @escaping () -> Void) -> AnimatedButton {
        AnimatedButton(title: title, style: .secondary, action: action)
    }
    
    /// Create a destructive button
    static func destructive(_ title: String, action: @escaping () -> Void) -> AnimatedButton {
        AnimatedButton(title: title, style: .destructive, action: action)
    }
}

// MARK: - Preview

#if DEBUG
struct AnimatedButtonPreview: View {
    var body: some View {
        VStack(spacing: 20) {
            AnimatedButton.primary("Primary Button") {
                print("Primary button tapped")
            }
            
            AnimatedButton.secondary("Secondary Button") {
                print("Secondary button tapped")
            }
            
            AnimatedButton.destructive("Delete") {
                print("Destructive button tapped")
            }
            
            AnimatedButton.primary("Disabled Button") {
                print("This shouldn't print")
            }
            .disabled(true)
        }
        .padding()
        .background(DesignSystem.colors.adaptiveBackground)
    }
}

#Preview {
    AnimatedButtonPreview()
}
#endif