//
//  GradientBackground.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/15.
//

import SwiftUI

// MARK: - Gradient Background Types

enum GradientBackgroundType {
    case main
    case card
    case recording
    case settings
    case empty
    case brandRecording // New brand-styled recording background
}

// MARK: - Gradient Background Component

struct GradientBackground: View {
    // MARK: - Properties
    
    let type: GradientBackgroundType
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - Computed Properties
    
    private var gradient: LinearGradient {
        switch type {
        case .main:
            return mainGradient
        case .card:
            return cardGradient
        case .recording:
            return recordingGradient
        case .settings:
            return settingsGradient
        case .empty:
            return emptyStateGradient
        case .brandRecording:
            return brandRecordingGradient
        }
    }
    
    private var mainGradient: LinearGradient {
        if colorScheme == .dark {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.110, green: 0.110, blue: 0.118), // #1C1C1E
                    Color(red: 0.173, green: 0.173, blue: 0.180)  // #2C2C2E
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.973, green: 0.980, blue: 1.0), // #F8FAFF
                    Color(red: 0.910, green: 0.949, blue: 1.0)  // #E8F2FF
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
    }
    
    private var cardGradient: LinearGradient {
        if colorScheme == .dark {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor.secondarySystemBackground),
                    Color(UIColor.tertiarySystemBackground)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color.white,
                    Color(red: 0.98, green: 0.98, blue: 1.0)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private var recordingGradient: LinearGradient {
        if colorScheme == .dark {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.2, green: 0.05, blue: 0.05), // Dark red base
                    Color(red: 0.15, green: 0.15, blue: 0.18)  // Dark background
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 1.0, green: 0.95, blue: 0.95), // Light red tint
                    Color(red: 0.98, green: 0.98, blue: 1.0)  // Light background
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private var settingsGradient: LinearGradient {
        if colorScheme == .dark {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(UIColor.systemBackground),
                    Color(UIColor.secondarySystemBackground)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.99, green: 0.99, blue: 1.0),
                    Color(red: 0.95, green: 0.95, blue: 0.98)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
    }
    
    private var emptyStateGradient: LinearGradient {
        if colorScheme == .dark {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.08, green: 0.12, blue: 0.18), // Deep blue-gray
                    Color(red: 0.12, green: 0.12, blue: 0.15)  // Dark background
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.96, green: 0.98, blue: 1.0), // Very light blue
                    Color(red: 0.92, green: 0.96, blue: 1.0)  // Light blue
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private var brandRecordingGradient: LinearGradient {
        if colorScheme == .dark {
            return LinearGradient(
                gradient: Gradient(colors: [
                    DesignSystem.brandColors.frenchLilac.opacity(0.1), // Subtle French Lilac in dark mode
                    Color.black
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    DesignSystem.brandColors.frenchLilac.opacity(0.4), // French Lilac
                    DesignSystem.brandColors.alabaster // Alabaster
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        Rectangle()
            .fill(gradient)
            .ignoresSafeArea()
            .animation(.easeInOut(duration: 0.3), value: colorScheme)
    }
}

// MARK: - Convenience Initializers

extension GradientBackground {
    /// Create a main app background gradient
    static var main: GradientBackground {
        GradientBackground(type: .main)
    }
    
    /// Create a card background gradient
    static var card: GradientBackground {
        GradientBackground(type: .card)
    }
    
    /// Create a recording state background gradient
    static var recording: GradientBackground {
        GradientBackground(type: .recording)
    }
    
    /// Create a settings background gradient
    static var settings: GradientBackground {
        GradientBackground(type: .settings)
    }
    
    /// Create an empty state background gradient
    static var empty: GradientBackground {
        GradientBackground(type: .empty)
    }
    
    /// Create a brand-styled recording background gradient
    static var brandRecording: GradientBackground {
        GradientBackground(type: .brandRecording)
    }
}

// MARK: - View Extensions

extension View {
    /// Apply a gradient background of the specified type
    func gradientBackground(_ type: GradientBackgroundType) -> some View {
        self.background(GradientBackground(type: type))
    }
    
    /// Apply the main app gradient background
    func mainGradientBackground() -> some View {
        self.background(GradientBackground.main)
    }
    
    /// Apply a card gradient background
    func cardGradientBackground() -> some View {
        self.background(GradientBackground.card)
    }
    
    /// Apply a recording state gradient background
    func recordingGradientBackground() -> some View {
        self.background(GradientBackground.recording)
    }
    
    /// Apply a settings gradient background
    func settingsGradientBackground() -> some View {
        self.background(GradientBackground.settings)
    }
    
    /// Apply an empty state gradient background
    func emptyGradientBackground() -> some View {
        self.background(GradientBackground.empty)
    }
    
    /// Apply a brand-styled recording gradient background
    func brandRecordingGradientBackground() -> some View {
        self.background(GradientBackground.brandRecording)
    }
}

// MARK: - Animated Gradient Background

struct AnimatedGradientBackground: View {
    // MARK: - Properties
    
    let type: GradientBackgroundType
    @State private var animationOffset: CGFloat = 0
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // Base gradient
            GradientBackground(type: type)
            
            // Animated overlay for subtle movement
            Rectangle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.1),
                            Color.clear,
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .offset(x: animationOffset, y: animationOffset * 0.5)
                .ignoresSafeArea()
                .onAppear {
                    withAnimation(
                        .easeInOut(duration: 8.0)
                        .repeatForever(autoreverses: true)
                    ) {
                        animationOffset = 50
                    }
                }
        }
    }
}

// MARK: - Preview

#if DEBUG
struct GradientBackgroundPreview: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("Main Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .mainGradientBackground()
                    .cornerRadius(12)
                
                Text("Card Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .cardGradientBackground()
                    .cornerRadius(12)
                
                Text("Recording Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .recordingGradientBackground()
                    .cornerRadius(12)
                
                Text("Settings Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .settingsGradientBackground()
                    .cornerRadius(12)
                
                Text("Empty State Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .emptyGradientBackground()
                    .cornerRadius(12)
                
                Text("Brand Recording Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .brandRecordingGradientBackground()
                    .cornerRadius(12)
                
                Text("Animated Background")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 100)
                    .background(AnimatedGradientBackground(type: .main))
                    .cornerRadius(12)
            }
            .padding()
        }
        .background(Color(UIColor.systemBackground))
    }
}

#Preview {
    GradientBackgroundPreview()
}
#endif