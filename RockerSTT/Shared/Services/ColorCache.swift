//
//  ColorCache.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

/// High-performance color caching system for expensive color operations
class ColorCache {
    static let shared = ColorCache()
    
    private var colorCache: [String: Color] = [:]
    private var gradientCache: [String: LinearGradient] = [:]
    private var materialCache: [String: Material] = [:]
    private let cacheQueue = DispatchQueue(label: "color.cache", qos: .userInitiated)
    private let maxCacheSize = 200
    
    private init() {}
    
    // MARK: - Color Caching
    
    /// Get or create a cached color
    func color(for key: String, factory: @escaping () -> Color) -> Color {
        if let cachedColor = colorCache[key] {
            return cachedColor
        }
        
        let color = factory()
        
        cacheQueue.async { [weak self] in
            self?.storeColor(color, for: key)
        }
        
        return color
    }
    
    private func storeColor(_ color: Color, for key: String) {
        if colorCache.count >= maxCacheSize {
            // Remove oldest entries (simple FIFO)
            let keysToRemove = Array(colorCache.keys.prefix(maxCacheSize / 4))
            keysToRemove.forEach { colorCache.removeValue(forKey: $0) }
        }
        
        colorCache[key] = color
    }
    
    // MARK: - Gradient Caching
    
    /// Get or create a cached gradient
    func gradient(for key: String, factory: @escaping () -> LinearGradient) -> LinearGradient {
        if let cachedGradient = gradientCache[key] {
            return cachedGradient
        }
        
        let gradient = factory()
        
        cacheQueue.async { [weak self] in
            self?.storeGradient(gradient, for: key)
        }
        
        return gradient
    }
    
    private func storeGradient(_ gradient: LinearGradient, for key: String) {
        if gradientCache.count >= maxCacheSize {
            let keysToRemove = Array(gradientCache.keys.prefix(maxCacheSize / 4))
            keysToRemove.forEach { gradientCache.removeValue(forKey: $0) }
        }
        
        gradientCache[key] = gradient
    }
    
    // MARK: - Material Caching
    
    /// Get or create a cached material
    func material(for key: String, factory: @escaping () -> Material) -> Material {
        if let cachedMaterial = materialCache[key] {
            return cachedMaterial
        }
        
        let material = factory()
        
        cacheQueue.async { [weak self] in
            self?.storeMaterial(material, for: key)
        }
        
        return material
    }
    
    private func storeMaterial(_ material: Material, for key: String) {
        if materialCache.count >= maxCacheSize {
            let keysToRemove = Array(materialCache.keys.prefix(maxCacheSize / 4))
            keysToRemove.forEach { materialCache.removeValue(forKey: $0) }
        }
        
        materialCache[key] = material
    }
    
    // MARK: - Cache Management
    
    /// Clear all caches
    func clearAll() {
        cacheQueue.async { [weak self] in
            self?.colorCache.removeAll()
            self?.gradientCache.removeAll()
            self?.materialCache.removeAll()
        }
    }
    
    /// Get cache statistics
    func getCacheStats() -> CacheStats {
        return CacheStats(
            colorCacheSize: colorCache.count,
            gradientCacheSize: gradientCache.count,
            materialCacheSize: materialCache.count,
            totalMemoryEstimate: estimateMemoryUsage()
        )
    }
    
    private func estimateMemoryUsage() -> Int {
        // Rough estimate: each color ~32 bytes, gradient ~64 bytes, material ~48 bytes
        return (colorCache.count * 32) + (gradientCache.count * 64) + (materialCache.count * 48)
    }
}

// MARK: - Cache Statistics

struct CacheStats {
    let colorCacheSize: Int
    let gradientCacheSize: Int
    let materialCacheSize: Int
    let totalMemoryEstimate: Int
    
    var description: String {
        """
        Color Cache: \(colorCacheSize) items
        Gradient Cache: \(gradientCacheSize) items
        Material Cache: \(materialCacheSize) items
        Estimated Memory: \(totalMemoryEstimate) bytes
        """
    }
}

// MARK: - Optimized Color Extensions

extension Color {
    /// Create an adaptive color with caching
    static func cachedAdaptive(
        light: Color,
        dark: Color,
        key: String
    ) -> Color {
        return ColorCache.shared.color(for: "adaptive_\(key)") {
            Color.adaptive(light: light, dark: dark)
        }
    }
    
    /// Create a brand color with caching
    static func cachedBrandColor(
        _ colorName: String,
        factory: @escaping () -> Color
    ) -> Color {
        return ColorCache.shared.color(for: "brand_\(colorName)", factory: factory)
    }
}

extension LinearGradient {
    /// Create a cached gradient
    static func cached(
        key: String,
        colors: [Color],
        startPoint: UnitPoint = .topLeading,
        endPoint: UnitPoint = .bottomTrailing
    ) -> LinearGradient {
        return ColorCache.shared.gradient(for: key) {
            LinearGradient(
                colors: colors,
                startPoint: startPoint,
                endPoint: endPoint
            )
        }
    }
}

extension Material {
    /// Create a cached material
    static func cached(
        key: String,
        factory: @escaping () -> Material
    ) -> Material {
        return ColorCache.shared.material(for: key, factory: factory)
    }
}

// MARK: - Performance Optimized Design System Extensions

extension DesignSystem {
    /// Optimized brand colors with caching
    struct OptimizedBrandColors {
        static let persianPurple = Color.cachedBrandColor("persian_purple") {
            DesignSystem.brandColors.persianPurple
        }
        
        static let orchid = Color.cachedBrandColor("orchid") {
            DesignSystem.brandColors.orchid
        }
        
        static let frenchLilac = Color.cachedBrandColor("french_lilac") {
            DesignSystem.brandColors.frenchLilac
        }
        
        static let amber = Color.cachedBrandColor("amber") {
            DesignSystem.brandColors.amber
        }
        
        static let alabaster = Color.cachedBrandColor("alabaster") {
            DesignSystem.brandColors.alabaster
        }
        
        // Adaptive colors with caching
        static let adaptivePrimary = Color.cachedAdaptive(
            light: DesignSystem.brandColors.persianPurple,
            dark: DesignSystem.brandColors.persianPurpleDark,
            key: "primary"
        )
        
        static let adaptiveSecondary = Color.cachedAdaptive(
            light: DesignSystem.brandColors.orchid,
            dark: DesignSystem.brandColors.orchidDark,
            key: "secondary"
        )
        
        static let adaptiveTertiary = Color.cachedAdaptive(
            light: DesignSystem.brandColors.frenchLilac,
            dark: DesignSystem.brandColors.frenchLilacDark,
            key: "tertiary"
        )
        
        static let adaptiveAccent = Color.cachedAdaptive(
            light: DesignSystem.brandColors.amber,
            dark: DesignSystem.brandColors.amberDark,
            key: "accent"
        )
        
        static let adaptiveBackground = Color.cachedAdaptive(
            light: DesignSystem.brandColors.alabaster,
            dark: DesignSystem.brandColors.alabasterDark,
            key: "background"
        )
    }
    
    /// Optimized gradients with caching
    struct OptimizedGradients {
        static let primaryGradient = LinearGradient.cached(
            key: "primary_gradient",
            colors: [
                DesignSystem.brandColors.persianPurple,
                DesignSystem.brandColors.orchid
            ]
        )
        
        static let backgroundGradient = LinearGradient.cached(
            key: "background_gradient",
            colors: [
                DesignSystem.brandColors.alabaster,
                DesignSystem.brandColors.frenchLilac.opacity(0.1)
            ]
        )
        
        static let accentGradient = LinearGradient.cached(
            key: "accent_gradient",
            colors: [
                DesignSystem.brandColors.amber,
                DesignSystem.brandColors.orchid
            ]
        )
        
        static let adaptivePrimaryGradient = LinearGradient.cached(
            key: "adaptive_primary_gradient",
            colors: [
                OptimizedBrandColors.adaptivePrimary,
                OptimizedBrandColors.adaptiveSecondary
            ]
        )
        
        static let adaptiveBackgroundGradient = LinearGradient.cached(
            key: "adaptive_background_gradient",
            colors: [
                OptimizedBrandColors.adaptiveBackground,
                OptimizedBrandColors.adaptiveTertiary.opacity(0.1)
            ]
        )
    }
    
    /// Optimized materials with caching
    struct OptimizedMaterials {
        static let glassmorphismCard = Material.cached(key: "glassmorphism_card") {
            Material.ultraThinMaterial
        }
        
        static let glassmorphismModal = Material.cached(key: "glassmorphism_modal") {
            Material.thickMaterial
        }
        
        static let brandGlassmorphism = Material.cached(key: "brand_glassmorphism") {
            Material.regularMaterial
        }
    }
}
