//
//  OptimizedAnimationManager.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

/// High-performance animation manager with accessibility support and optimization
class OptimizedAnimationManager: ObservableObject {
    static let shared = OptimizedAnimationManager()
    
    @Published var isReducedMotionEnabled: Bool = false
    @Published var animationQuality: AnimationQuality = .high
    
    private var animationCache: [String: Animation] = [:]
    private let maxCacheSize = 50
    
    private init() {
        setupAccessibilityObserver()
        detectAnimationQuality()
    }
    
    // MARK: - Accessibility Support
    
    private func setupAccessibilityObserver() {
        // Monitor reduced motion preference
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceMotionStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.updateReducedMotionStatus()
        }
        
        updateReducedMotionStatus()
    }
    
    private func updateReducedMotionStatus() {
        isReducedMotionEnabled = UIAccessibility.isReduceMotionEnabled
    }
    
    private func detectAnimationQuality() {
        // Detect device performance capabilities
        let deviceModel = UIDevice.current.model
        let _ = UIDevice.current.systemVersion
        
        // Simplified device performance detection
        if deviceModel.contains("iPhone") {
            animationQuality = .high
        } else {
            animationQuality = .medium
        }
    }
    
    // MARK: - Optimized Animations
    
    /// Get an optimized animation based on context and accessibility settings
    func animation(for context: AnimationContext) -> Animation {
        let cacheKey = "\(context.rawValue)_\(isReducedMotionEnabled)_\(animationQuality.rawValue)"
        
        if let cachedAnimation = animationCache[cacheKey] {
            return cachedAnimation
        }
        
        let animation = createOptimizedAnimation(for: context)
        
        cacheAnimation(animation, for: cacheKey)
        return animation
    }
    
    private func createOptimizedAnimation(for context: AnimationContext) -> Animation {
        if isReducedMotionEnabled {
            return createReducedMotionAnimation(for: context)
        }
        
        switch context {
        case .buttonPress:
            return createButtonPressAnimation()
        case .stateChange:
            return createStateChangeAnimation()
        case .recordingRings:
            return createRecordingRingsAnimation()
        case .shimmer:
            return createShimmerAnimation()
        case .modalPresentation:
            return createModalPresentationAnimation()
        case .cardAppearance:
            return createCardAppearanceAnimation()
        case .textUpdate:
            return createTextUpdateAnimation()
        case .navigationTransition:
            return createNavigationTransitionAnimation()
        }
    }
    
    private func createReducedMotionAnimation(for context: AnimationContext) -> Animation {
        // Simplified animations for accessibility
        switch context {
        case .buttonPress:
            return .linear(duration: 0.1)
        case .stateChange:
            return .linear(duration: 0.2)
        case .recordingRings, .shimmer:
            return .linear(duration: 0.01) // Minimal animation for accessibility
        case .modalPresentation:
            return .linear(duration: 0.3)
        case .cardAppearance:
            return .linear(duration: 0.2)
        case .textUpdate:
            return .linear(duration: 0.15)
        case .navigationTransition:
            return .linear(duration: 0.25)
        }
    }
    
    // MARK: - Specific Animation Creators
    
    private func createButtonPressAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0.1)
        case .medium:
            return .easeInOut(duration: 0.15)
        case .low:
            return .linear(duration: 0.1)
        }
    }
    
    private func createStateChangeAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0.2)
        case .medium:
            return .easeInOut(duration: 0.3)
        case .low:
            return .linear(duration: 0.2)
        }
    }
    
    private func createRecordingRingsAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .easeInOut(duration: 2.0).repeatForever(autoreverses: true)
        case .medium:
            return .linear(duration: 2.5).repeatForever(autoreverses: true)
        case .low:
            return .linear(duration: 3.0).repeatForever(autoreverses: true)
        }
    }
    
    private func createShimmerAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .linear(duration: 1.5).repeatForever(autoreverses: false)
        case .medium:
            return .linear(duration: 2.0).repeatForever(autoreverses: false)
        case .low:
            return .linear(duration: 2.5).repeatForever(autoreverses: false)
        }
    }
    
    private func createModalPresentationAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.3)
        case .medium:
            return .easeInOut(duration: 0.4)
        case .low:
            return .linear(duration: 0.3)
        }
    }
    
    private func createCardAppearanceAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0.2)
        case .medium:
            return .easeOut(duration: 0.3)
        case .low:
            return .linear(duration: 0.2)
        }
    }
    
    private func createTextUpdateAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .easeInOut(duration: 0.2)
        case .medium:
            return .linear(duration: 0.15)
        case .low:
            return .linear(duration: 0.1)
        }
    }
    
    private func createNavigationTransitionAnimation() -> Animation {
        switch animationQuality {
        case .high:
            return .spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0.25)
        case .medium:
            return .easeInOut(duration: 0.35)
        case .low:
            return .linear(duration: 0.25)
        }
    }
    
    // MARK: - Cache Management
    
    private func cacheAnimation(_ animation: Animation, for key: String) {
        if animationCache.count >= maxCacheSize {
            // Remove oldest entries
            let keysToRemove = Array(animationCache.keys.prefix(maxCacheSize / 4))
            keysToRemove.forEach { animationCache.removeValue(forKey: $0) }
        }
        
        animationCache[key] = animation
    }
    
    func clearAnimationCache() {
        animationCache.removeAll()
    }
    
    // MARK: - Performance Monitoring
    
    func measureAnimationPerformance<T: View>(
        _ view: T,
        animation: Animation,
        completion: @escaping () -> Void
    ) -> some View {
        view
            .onAppear {
                // Performance monitoring removed
            }
            .onDisappear {
                completion()
            }
    }
    
    // MARK: - Convenience Methods
    
    /// Get button press animation with performance monitoring
    var buttonPress: Animation {
        animation(for: .buttonPress)
    }
    
    /// Get state change animation with performance monitoring
    var stateChange: Animation {
        animation(for: .stateChange)
    }
    
    /// Get recording rings animation with performance monitoring
    var recordingRings: Animation {
        animation(for: .recordingRings)
    }
    
    /// Get shimmer animation with performance monitoring
    var shimmer: Animation {
        animation(for: .shimmer)
    }
    
    /// Get modal presentation animation with performance monitoring
    var modalPresentation: Animation {
        animation(for: .modalPresentation)
    }
    
    /// Get card appearance animation with performance monitoring
    var cardAppearance: Animation {
        animation(for: .cardAppearance)
    }
    
    /// Get text update animation with performance monitoring
    var textUpdate: Animation {
        animation(for: .textUpdate)
    }
    
    /// Get navigation transition animation with performance monitoring
    var navigationTransition: Animation {
        animation(for: .navigationTransition)
    }
}

// MARK: - Supporting Types

enum AnimationContext: String, CaseIterable {
    case buttonPress = "button_press"
    case stateChange = "state_change"
    case recordingRings = "recording_rings"
    case shimmer = "shimmer"
    case modalPresentation = "modal_presentation"
    case cardAppearance = "card_appearance"
    case textUpdate = "text_update"
    case navigationTransition = "navigation_transition"
}

enum AnimationQuality: String, CaseIterable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var description: String {
        switch self {
        case .high: return "High Quality"
        case .medium: return "Medium Quality"
        case .low: return "Low Quality"
        }
    }
}

// MARK: - View Extensions

extension View {
    /// Apply optimized animation with performance monitoring
    func optimizedAnimation(_ context: AnimationContext) -> some View {
        let animation = OptimizedAnimationManager.shared.animation(for: context)
        return self.animation(animation, value: UUID())
    }
    
    /// Apply optimized button press animation
    func optimizedButtonPress() -> some View {
        self.animation(OptimizedAnimationManager.shared.buttonPress, value: UUID())
    }
    
    /// Apply optimized state change animation
    func optimizedStateChange() -> some View {
        self.animation(OptimizedAnimationManager.shared.stateChange, value: UUID())
    }
}
