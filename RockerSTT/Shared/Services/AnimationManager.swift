//
//  AnimationManager.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Centralized animation manager providing consistent animations throughout the app
/// Handles different types of animations for various UI interactions and state changes
class AnimationManager: ObservableObject {
    
    // MARK: - Singleton Instance
    
    static let shared = AnimationManager()
    
    // MARK: - Animation Constants
    
    struct Duration {
        static let fast: Double = 0.2
        static let medium: Double = 0.3
        static let slow: Double = 0.5
        static let loading: Double = 1.0
    }
    
    struct Spring {
        static let bouncy = Animation.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)
        static let smooth = Animation.spring(response: 0.4, dampingFraction: 1.0, blendDuration: 0)
        static let gentle = Animation.spring(response: 0.8, dampingFraction: 0.9, blendDuration: 0)
    }
    
    struct Easing {
        static let easeInOut = Animation.easeInOut(duration: Duration.medium)
        static let easeOut = Animation.easeOut(duration: Duration.medium)
        static let easeIn = Animation.easeIn(duration: Duration.medium)
        static let linear = Animation.linear(duration: Duration.medium)
    }
    
    // MARK: - Initialization
    
    private init() {}
    
    // MARK: - List Animations
    
    /// Animation for list item appearance
    static var listItemAppear: Animation {
        .spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0)
    }
    
    /// Animation for list item removal
    static var listItemRemove: Animation {
        .easeInOut(duration: Duration.fast)
    }
    
    /// Animation for list item selection
    static var listItemSelect: Animation {
        .easeOut(duration: Duration.fast)
    }
    
    /// Animation for list refresh
    static var listRefresh: Animation {
        .spring(response: 0.6, dampingFraction: 0.7, blendDuration: 0)
    }
    
    // MARK: - Card Animations
    
    /// Animation for card hover/press
    static var cardPress: Animation {
        .easeInOut(duration: Duration.fast)
    }
    
    /// Animation for card expansion
    static var cardExpand: Animation {
        .spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0)
    }
    
    /// Animation for card collapse
    static var cardCollapse: Animation {
        .easeInOut(duration: Duration.medium)
    }
    
    // MARK: - Search Animations
    
    /// Animation for search bar appearance
    static var searchBarAppear: Animation {
        .spring(response: 0.4, dampingFraction: 0.9, blendDuration: 0)
    }
    
    /// Animation for search results
    static var searchResults: Animation {
        .easeOut(duration: Duration.medium)
    }
    
    /// Animation for search filter changes
    static var searchFilter: Animation {
        .easeInOut(duration: Duration.fast)
    }
    
    // MARK: - Loading Animations
    
    /// Animation for loading states
    static var loading: Animation {
        .easeInOut(duration: Duration.loading).repeatForever(autoreverses: true)
    }
    
    /// Animation for skeleton loading
    static var skeleton: Animation {
        .linear(duration: 1.5).repeatForever(autoreverses: false)
    }
    
    /// Animation for progress indicators
    static var progress: Animation {
        .linear(duration: Duration.medium)
    }
    
    // MARK: - State Change Animations
    
    /// Animation for favorite state change
    static var favoriteToggle: Animation {
        .spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0)
    }
    
    /// Animation for selection state change
    static var selectionToggle: Animation {
        .easeInOut(duration: Duration.fast)
    }
    
    /// Animation for tag addition/removal
    static var tagChange: Animation {
        .spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0)
    }
    
    // MARK: - Navigation Animations
    
    /// Animation for tab switching (0.3s ease-in-out as per requirements)
    static var tabSwitch: Animation {
        .easeInOut(duration: 0.3)
    }
    
    /// Animation for modal presentation with smooth spring physics
    static var modalPresent: Animation {
        .spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0)
    }
    
    /// Animation for modal dismissal
    static var modalDismiss: Animation {
        .easeInOut(duration: Duration.medium)
    }
    
    // MARK: - Enhanced Button Animations
    
    /// Enhanced button press animation with proper timing
    static var buttonPress: Animation {
        .easeOut(duration: 0.12)
    }
    
    /// Button release animation
    static var buttonRelease: Animation {
        .easeInOut(duration: 0.15)
    }
    
    // MARK: - Recording Interface Animations
    
    /// Recording button pulse animation using brand colors
    static var recordingPulse: Animation {
        .easeInOut(duration: 1.2).repeatForever(autoreverses: true)
    }
    
    /// Recording visualization rings animation
    static var recordingRings: Animation {
        .easeInOut(duration: 1.5).repeatForever(autoreverses: true)
    }
    
    /// Recording state transition animation
    static var recordingStateChange: Animation {
        .spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0)
    }
    
    // MARK: - Card Entrance Animations with Spring Physics
    
    /// Enhanced card entrance animation with spring physics
    static var cardEntranceSpring: Animation {
        .spring(response: 0.55, dampingFraction: 0.75, blendDuration: 0.08)
    }
    
    /// Staggered card entrance for multiple cards
    static func cardEntranceStaggered(index: Int, baseDelay: Double = 0.1) -> Animation {
        cardEntranceSpring.delay(Double(index) * baseDelay)
    }
    
    /// Card exit animation
    static var cardExit: Animation {
        .easeInOut(duration: 0.25)
    }
    
    // MARK: - Error and Success Animations
    
    /// Animation for error states
    static var error: Animation {
        .spring(response: 0.3, dampingFraction: 0.5, blendDuration: 0)
    }
    
    /// Animation for success states
    static var success: Animation {
        .spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0)
    }
    
    /// Animation for warning states
    static var warning: Animation {
        .easeInOut(duration: Duration.medium)
    }
    
    // MARK: - Custom Animation Helpers
    
    /// Create a delayed animation
    static func delayed(_ delay: Double, animation: Animation) -> Animation {
        animation.delay(delay)
    }
    
    /// Create a staggered animation for multiple items
    static func staggered(index: Int, baseDelay: Double = 0.1, animation: Animation) -> Animation {
        animation.delay(Double(index) * baseDelay)
    }
    
    /// Create a bounce animation
    static func bounce(intensity: Double = 1.0) -> Animation {
        .spring(response: 0.4, dampingFraction: 0.6 * intensity, blendDuration: 0)
    }
    
    /// Create a pulse animation
    static func pulse(duration: Double = 1.0) -> Animation {
        .easeInOut(duration: duration).repeatForever(autoreverses: true)
    }
}

// MARK: - Modal Presentation Transitions

extension AnyTransition {
    
    /// Smooth modal presentation transition
    static var modalPresentation: AnyTransition {
        .asymmetric(
            insertion: .move(edge: .bottom).combined(with: .opacity),
            removal: .move(edge: .bottom).combined(with: .opacity)
        )
    }
    
    /// Enhanced modal presentation with scale
    static var modalPresentationWithScale: AnyTransition {
        .asymmetric(
            insertion: .scale(scale: 0.95).combined(with: .move(edge: .bottom)).combined(with: .opacity),
            removal: .scale(scale: 0.95).combined(with: .move(edge: .bottom)).combined(with: .opacity)
        )
    }
}

// MARK: - Animation Modifiers

extension View {
    
    /// Apply list item animation
    func listItemAnimation() -> some View {
        self.animation(AnimationManager.listItemAppear, value: UUID())
    }
    
    /// Apply card press animation
    func cardPressAnimation() -> some View {
        self.animation(AnimationManager.cardPress, value: UUID())
    }
    
    /// Apply favorite toggle animation
    func favoriteAnimation() -> some View {
        self.animation(AnimationManager.favoriteToggle, value: UUID())
    }
    
    /// Apply search animation
    func searchAnimation() -> some View {
        self.animation(AnimationManager.searchResults, value: UUID())
    }
    
    /// Apply loading animation
    func loadingAnimation() -> some View {
        self.animation(AnimationManager.loading, value: UUID())
    }
    
    /// Apply staggered animation with index
    func staggeredAnimation(index: Int) -> some View {
        self.animation(AnimationManager.staggered(index: index, animation: AnimationManager.listItemAppear), value: UUID())
    }
    
    /// Apply bounce animation on appear
    func bounceOnAppear() -> some View {
        self.onAppear {
            withAnimation(AnimationManager.bounce()) {
                // Trigger animation
            }
        }
    }
    
    /// Apply scale animation on press
    func scaleOnPress(scale: CGFloat = 0.95) -> some View {
        self.scaleEffect(scale)
            .animation(AnimationManager.cardPress, value: scale)
    }
    
    /// Apply enhanced button press animation with haptic feedback
    func enhancedButtonPress(action: @escaping () -> Void) -> some View {
        self.onTapGesture {
            HapticFeedbackManager.shared.buttonPressed()
            withAnimation(AnimationManager.accessibleButtonPress) {
                action()
            }
        }
    }
    
    /// Apply tab switching animation
    func tabSwitchAnimation() -> some View {
        self.animation(AnimationManager.accessibleTabSwitch, value: UUID())
    }
    
    /// Apply card entrance animation with spring physics
    func cardEntranceAnimation(index: Int = 0) -> some View {
        self.animation(AnimationManager.cardEntranceStaggered(index: index), value: UUID())
    }
    
    /// Apply recording pulse animation
    func recordingPulseAnimation(isRecording: Bool) -> some View {
        self.scaleEffect(isRecording ? 1.05 : 1.0)
            .animation(AnimationManager.accessibleRecordingPulse, value: isRecording)
    }
    
    /// Apply smooth modal presentation
    func smoothModalPresentation() -> some View {
        self.transition(.modalPresentation)
            .animation(AnimationManager.modalPresent, value: UUID())
    }
    
    /// Apply accessibility-aware animation
    func accessibilityAwareAnimation(_ animation: Animation, value: some Equatable) -> some View {
        self.animation(AnimationManager.respectingAccessibility(animation), value: value)
    }
}

// MARK: - Accessibility Support for Reduced Motion

extension AnimationManager {
    
    /// Check if reduced motion is enabled in system settings
    static var isReducedMotionEnabled: Bool {
        UIAccessibility.isReduceMotionEnabled
    }
    
    /// Returns appropriate animation based on accessibility settings
    static func respectingAccessibility(_ animation: Animation) -> Animation {
        isReducedMotionEnabled ? .linear(duration: 0.01) : animation
    }
    
    /// Returns reduced animation for accessibility
    static func reducedMotion(_ fullAnimation: Animation, reduced: Animation = .easeInOut(duration: 0.2)) -> Animation {
        isReducedMotionEnabled ? reduced : fullAnimation
    }
    
    /// Tab switching animation that respects accessibility settings
    static var accessibleTabSwitch: Animation {
        respectingAccessibility(tabSwitch)
    }
    
    /// Card entrance animation that respects accessibility settings
    static var accessibleCardEntrance: Animation {
        respectingAccessibility(cardEntranceSpring)
    }
    
    /// Recording pulse animation that respects accessibility settings
    static var accessibleRecordingPulse: Animation {
        isReducedMotionEnabled ? .linear(duration: 0.01) : recordingPulse
    }
    
    /// Button press animation that respects accessibility settings
    static var accessibleButtonPress: Animation {
        reducedMotion(buttonPress, reduced: .easeOut(duration: 0.08))
    }
}

// MARK: - Animation State Manager

/// Manages animation states for complex UI components
class AnimationStateManager: ObservableObject {
    
    @Published var isAnimating = false
    @Published var animationProgress: Double = 0.0
    @Published var currentAnimation: Animation?
    
    /// Start an animation with progress tracking
    func startAnimation(_ animation: Animation, duration: Double) {
        isAnimating = true
        currentAnimation = animation
        
        withAnimation(animation) {
            animationProgress = 1.0
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
            self.isAnimating = false
            self.animationProgress = 0.0
            self.currentAnimation = nil
        }
    }
    
    /// Stop current animation
    func stopAnimation() {
        isAnimating = false
        animationProgress = 0.0
        currentAnimation = nil
    }
    
    /// Reset animation state
    func reset() {
        withAnimation(.none) {
            stopAnimation()
        }
    }
}

// MARK: - Transition Extensions

extension AnyTransition {
    
    /// Custom slide transition for list items
    static var listItemSlide: AnyTransition {
        .asymmetric(
            insertion: .move(edge: .trailing).combined(with: .opacity),
            removal: .move(edge: .leading).combined(with: .opacity)
        )
    }
    
    /// Custom scale transition for cards
    static var cardScale: AnyTransition {
        .scale(scale: 0.8).combined(with: .opacity)
    }
    
    /// Custom fade transition for search results
    static var searchFade: AnyTransition {
        .opacity.combined(with: .move(edge: .top))
    }
    
    /// Custom bounce transition
    static var bounce: AnyTransition {
        .scale(scale: 0.6).combined(with: .opacity)
    }
}
