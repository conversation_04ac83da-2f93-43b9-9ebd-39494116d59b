//
//  HapticFeedbackManager.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/17.
//

import SwiftUI
import UIKit

/// Centralized haptic feedback manager providing consistent haptic patterns throughout the app
/// Handles different types of haptic feedback for various user interactions and app states
class HapticFeedbackManager: ObservableObject {
    
    // MARK: - Singleton Instance
    
    static let shared = HapticFeedbackManager()
    
    // MARK: - Private Properties
    
    private let impactLight = UIImpactFeedbackGenerator(style: .light)
    private let impactMedium = UIImpactFeedbackGenerator(style: .medium)
    private let impactHeavy = UIImpactFeedbackGenerator(style: .heavy)
    private let notificationGenerator = UINotificationFeedbackGenerator()
    private let selectionGenerator = UISelectionFeedbackGenerator()
    
    // MARK: - Initialization
    
    private init() {
        // Prepare generators for better performance
        prepareGenerators()
    }
    
    // MARK: - Public Methods
    
    /// Trigger haptic feedback when recording starts
    /// Uses medium impact to provide clear feedback for this important action
    func recordingStarted() {
        impactMedium.impactOccurred()
        print("🔄 HapticFeedback: Recording started - medium impact")
    }
    
    /// Trigger haptic feedback when recording stops
    /// Uses heavy impact to provide definitive feedback that recording has ended
    func recordingEnded() {
        impactHeavy.impactOccurred()
        print("🛑 HapticFeedback: Recording ended - heavy impact")
    }
    
    /// Trigger haptic feedback when a new transcription is received
    /// Uses light impact to provide subtle feedback without being intrusive
    func transcriptionReceived() {
        impactLight.impactOccurred()
        print("📝 HapticFeedback: Transcription received - light impact")
    }
    
    /// Trigger haptic feedback when an error occurs
    /// Uses error notification to clearly communicate problem state
    func errorOccurred() {
        notificationGenerator.notificationOccurred(.error)
        print("❌ HapticFeedback: Error occurred - error notification")
    }
    
    /// Trigger haptic feedback for successful operations
    /// Uses success notification for positive feedback
    func successFeedback() {
        notificationGenerator.notificationOccurred(.success)
        print("✅ HapticFeedback: Success - success notification")
    }
    
    /// Trigger haptic feedback for warnings
    /// Uses warning notification for cautionary feedback
    func warningFeedback() {
        notificationGenerator.notificationOccurred(.warning)
        print("⚠️ HapticFeedback: Warning - warning notification")
    }
    
    /// Trigger haptic feedback for button presses and UI interactions
    /// Uses light impact for subtle interaction feedback
    func buttonPressed() {
        impactLight.impactOccurred()
        print("👆 HapticFeedback: Button pressed - light impact")
    }
    
    /// Trigger haptic feedback for selection changes
    /// Uses selection generator for list/picker interactions
    func selectionChanged() {
        selectionGenerator.selectionChanged()
        print("🔄 HapticFeedback: Selection changed - selection feedback")
    }
    
    /// Trigger haptic feedback for connection state changes
    /// Uses different patterns based on connection state
    func connectionStateChanged(isConnected: Bool) {
        if isConnected {
            notificationGenerator.notificationOccurred(.success)
            print("🌐 HapticFeedback: Connected - success notification")
        } else {
            notificationGenerator.notificationOccurred(.warning)
            print("🌐 HapticFeedback: Disconnected - warning notification")
        }
    }
    
    /// Trigger haptic feedback for long press gestures
    /// Uses medium impact for gesture recognition
    func longPressDetected() {
        impactMedium.impactOccurred()
        print("👆 HapticFeedback: Long press detected - medium impact")
    }
    
    /// Trigger haptic feedback for swipe gestures
    /// Uses light impact for gesture feedback
    func swipeGesture() {
        impactLight.impactOccurred()
        print("👆 HapticFeedback: Swipe gesture - light impact")
    }

    // MARK: - History-Specific Haptic Feedback

    /// Trigger haptic feedback when a session is selected
    /// Uses light impact for session navigation
    func sessionSelected() {
        impactLight.impactOccurred()
        print("📋 HapticFeedback: Session selected - light impact")
    }

    /// Trigger haptic feedback when a session is favorited
    /// Uses medium impact for important state change
    func sessionFavorited() {
        impactMedium.impactOccurred()
        print("⭐ HapticFeedback: Session favorited - medium impact")
    }

    /// Trigger haptic feedback when a session is unfavorited
    /// Uses light impact for state change
    func sessionUnfavorited() {
        impactLight.impactOccurred()
        print("☆ HapticFeedback: Session unfavorited - light impact")
    }

    /// Trigger haptic feedback when a session is deleted
    /// Uses heavy impact for destructive action
    func sessionDeleted() {
        impactHeavy.impactOccurred()
        print("🗑️ HapticFeedback: Session deleted - heavy impact")
    }

    /// Trigger haptic feedback when multiple sessions are selected
    /// Uses selection feedback for multi-selection
    func multipleSessionsSelected() {
        selectionGenerator.selectionChanged()
        print("📋 HapticFeedback: Multiple sessions selected - selection feedback")
    }

    /// Trigger haptic feedback when search results are found
    /// Uses light impact for search feedback
    func searchResultsFound() {
        impactLight.impactOccurred()
        print("🔍 HapticFeedback: Search results found - light impact")
    }

    /// Trigger haptic feedback when no search results are found
    /// Uses warning notification for empty results
    func noSearchResults() {
        notificationGenerator.notificationOccurred(.warning)
        print("🔍 HapticFeedback: No search results - warning notification")
    }

    /// Trigger haptic feedback when export starts
    /// Uses medium impact for export action
    func exportStarted() {
        impactMedium.impactOccurred()
        print("📤 HapticFeedback: Export started - medium impact")
    }

    /// Trigger haptic feedback when export completes
    /// Uses success notification for completion
    func exportCompleted() {
        notificationGenerator.notificationOccurred(.success)
        print("📤 HapticFeedback: Export completed - success notification")
    }

    /// Trigger haptic feedback when tag is added
    /// Uses light impact for tagging action
    func tagAdded() {
        impactLight.impactOccurred()
        print("🏷️ HapticFeedback: Tag added - light impact")
    }

    /// Trigger haptic feedback when tag is removed
    /// Uses light impact for tag removal
    func tagRemoved() {
        impactLight.impactOccurred()
        print("🏷️ HapticFeedback: Tag removed - light impact")
    }

    /// Trigger haptic feedback for pull-to-refresh
    /// Uses medium impact for refresh action
    func pullToRefresh() {
        impactMedium.impactOccurred()
        print("🔄 HapticFeedback: Pull to refresh - medium impact")
    }

    /// Trigger haptic feedback when loading completes
    /// Uses light impact for completion
    func loadingCompleted() {
        impactLight.impactOccurred()
        print("⏳ HapticFeedback: Loading completed - light impact")
    }
    
    // MARK: - Private Methods
    
    /// Prepare all haptic generators for better performance
    /// Called during initialization to reduce latency
    private func prepareGenerators() {
        impactLight.prepare()
        impactMedium.prepare()
        impactHeavy.prepare()
        notificationGenerator.prepare()
        selectionGenerator.prepare()
    }
    
    /// Re-prepare generators when app becomes active
    /// Helps maintain responsiveness after app backgrounding
    func prepareForActiveUse() {
        prepareGenerators()
        print("🔄 HapticFeedback: Generators prepared for active use")
    }
}

// MARK: - SwiftUI Environment Integration

/// Environment key for HapticFeedbackManager
struct HapticFeedbackManagerKey: EnvironmentKey {
    static let defaultValue = HapticFeedbackManager.shared
}

extension EnvironmentValues {
    var hapticFeedback: HapticFeedbackManager {
        get { self[HapticFeedbackManagerKey.self] }
        set { self[HapticFeedbackManagerKey.self] = newValue }
    }
}

// MARK: - View Extensions

extension View {
    /// Add haptic feedback to button press actions
    func withHapticFeedback() -> some View {
        self.onTapGesture {
            HapticFeedbackManager.shared.buttonPressed()
        }
    }
    
    /// Add haptic feedback to long press gestures
    func withLongPressHaptic(minimumDuration: Double = 0.5, action: @escaping () -> Void) -> some View {
        self.onLongPressGesture(minimumDuration: minimumDuration) {
            HapticFeedbackManager.shared.longPressDetected()
            action()
        }
    }
}

