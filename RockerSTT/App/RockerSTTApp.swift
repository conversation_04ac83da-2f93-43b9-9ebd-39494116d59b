//
//  RockerSTTApp.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import SwiftUI
import CoreData

@main
struct RockerSTTApp: App {
    // Initialize Core Data stack on app launch
    let coreDataStack = CoreDataStack.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, coreDataStack.mainContext)
        }
    }
}

extension Bundle {
    var displayName: String {
        return object(forInfoDictionaryKey: "CFBundleDisplayName") as? String ?? "SikTing"
    }
}
