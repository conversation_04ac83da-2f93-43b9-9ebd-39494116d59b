//
//  TranslationService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/7/21.
//

import Foundation
import Combine

// MARK: - Translation Models

/// Supported translation languages based on Google Translate API language codes
enum TranslationLanguage: String, CaseIterable, Identifiable {
    // Major World Languages
    case english = "en"
    case spanish = "es"
    case french = "fr"
    case german = "de"
    case portuguese = "pt"
    case russian = "ru"
    case japanese = "ja"
    case korean = "ko"
    case chineseSimplified = "zh-CN"
    case chineseTraditional = "zh-TW"
    case arabic = "ar"
    case hindi = "hi"
    case italian = "it"
    case dutch = "nl"
    case vietnamese = "vi"
    case thai = "th"

    // European Languages
    case polish = "pl"
    case czech = "cs"
    case hungarian = "hu"
    case romanian = "ro"
    case bulgarian = "bg"
    case croatian = "hr"
    case serbian = "sr"
    case slovak = "sk"
    case slovenian = "sl"
    case lithuanian = "lt"
    case latvian = "lv"
    case estonian = "et"
    case finnish = "fi"
    case danish = "da"
    case swedish = "sv"
    case norwegian = "no"
    case icelandic = "is"
    case greek = "el"
    case turkish = "tr"
    case ukrainian = "uk"
    case belarusian = "be"

    // Asian Languages
    case cantonese = "yue"
    case indonesian = "id"
    case malay = "ms"
    case tagalog = "tl"
    case bengali = "bn"
    case urdu = "ur"
    case punjabi = "pa"
    case gujarati = "gu"
    case tamil = "ta"
    case telugu = "te"
    case kannada = "kn"
    case malayalam = "ml"
    case marathi = "mr"
    case nepali = "ne"
    case sinhala = "si"
    case myanmar = "my"
    case khmer = "km"
    case lao = "lo"
    case mongolian = "mn"
    case kazakh = "kk"
    case uzbek = "uz"
    case kyrgyz = "ky"
    case tajik = "tg"
    case persian = "fa"
    case pashto = "ps"
    case hebrew = "he"

    // African Languages
    case swahili = "sw"
    case amharic = "am"
    case yoruba = "yo"
    case igbo = "ig"
    case hausa = "ha"
    case somali = "so"
    case afrikaans = "af"
    case zulu = "zu"
    case xhosa = "xh"

    // American Languages
    case portuguese_brazil = "pt-BR"
    case french_canada = "fr-CA"
    case quechua = "qu"
    case guarani = "gn"
    case haitian_creole = "ht"

    // Other Languages
    case esperanto = "eo"
    case latin = "la"
    case irish = "ga"
    case welsh = "cy"
    case basque = "eu"
    case catalan = "ca"
    case galician = "gl"
    case maltese = "mt"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        // Major World Languages
        case .english: return "English"
        case .spanish: return "Español"
        case .french: return "Français"
        case .german: return "Deutsch"
        case .portuguese: return "Português"
        case .russian: return "Русский"
        case .japanese: return "日本語"
        case .korean: return "한국어"
        case .chineseSimplified: return "中文 (简体)"
        case .chineseTraditional: return "中文 (繁體)"
        case .arabic: return "العربية"
        case .hindi: return "हिन्दी"
        case .italian: return "Italiano"
        case .dutch: return "Nederlands"
        case .vietnamese: return "Tiếng Việt"
        case .thai: return "ไทย"

        // European Languages
        case .polish: return "Polski"
        case .czech: return "Čeština"
        case .hungarian: return "Magyar"
        case .romanian: return "Română"
        case .bulgarian: return "Български"
        case .croatian: return "Hrvatski"
        case .serbian: return "Српски"
        case .slovak: return "Slovenčina"
        case .slovenian: return "Slovenščina"
        case .lithuanian: return "Lietuvių"
        case .latvian: return "Latviešu"
        case .estonian: return "Eesti"
        case .finnish: return "Suomi"
        case .danish: return "Dansk"
        case .swedish: return "Svenska"
        case .norwegian: return "Norsk"
        case .icelandic: return "Íslenska"
        case .greek: return "Ελληνικά"
        case .turkish: return "Türkçe"
        case .ukrainian: return "Українська"
        case .belarusian: return "Беларуская"

        // Asian Languages
        case .cantonese: return "粵語"
        case .indonesian: return "Bahasa Indonesia"
        case .malay: return "Bahasa Melayu"
        case .tagalog: return "Filipino"
        case .bengali: return "বাংলা"
        case .urdu: return "اردو"
        case .punjabi: return "ਪੰਜਾਬੀ"
        case .gujarati: return "ગુજરાતી"
        case .tamil: return "தமிழ்"
        case .telugu: return "తెలుగు"
        case .kannada: return "ಕನ್ನಡ"
        case .malayalam: return "മലയാളം"
        case .marathi: return "मराठी"
        case .nepali: return "नेपाली"
        case .sinhala: return "සිංහල"
        case .myanmar: return "မြန်မာ"
        case .khmer: return "ខ្មែរ"
        case .lao: return "ລາວ"
        case .mongolian: return "Монгол"
        case .kazakh: return "Қазақ"
        case .uzbek: return "O'zbek"
        case .kyrgyz: return "Кыргыз"
        case .tajik: return "Тоҷикӣ"
        case .persian: return "فارسی"
        case .pashto: return "پښتو"
        case .hebrew: return "עברית"

        // African Languages
        case .swahili: return "Kiswahili"
        case .amharic: return "አማርኛ"
        case .yoruba: return "Yorùbá"
        case .igbo: return "Igbo"
        case .hausa: return "Hausa"
        case .somali: return "Soomaali"
        case .afrikaans: return "Afrikaans"
        case .zulu: return "isiZulu"
        case .xhosa: return "isiXhosa"

        // American Languages
        case .portuguese_brazil: return "Português (Brasil)"
        case .french_canada: return "Français (Canada)"
        case .quechua: return "Quechua"
        case .guarani: return "Guaraní"
        case .haitian_creole: return "Kreyòl Ayisyen"

        // Other Languages
        case .esperanto: return "Esperanto"
        case .latin: return "Latina"
        case .irish: return "Gaeilge"
        case .welsh: return "Cymraeg"
        case .basque: return "Euskera"
        case .catalan: return "Català"
        case .galician: return "Galego"
        case .maltese: return "Malti"
        }
    }
    
    var flag: String {
        switch self {
        // Major World Languages
        case .english: return "🇺🇸"
        case .spanish: return "🇪🇸"
        case .french: return "🇫🇷"
        case .german: return "🇩🇪"
        case .portuguese: return "🇵🇹"
        case .russian: return "🇷🇺"
        case .japanese: return "🇯🇵"
        case .korean: return "🇰🇷"
        case .chineseSimplified: return "🇨🇳"
        case .chineseTraditional: return "🇨🇳"
        case .arabic: return "🇸🇦"
        case .hindi: return "🇮🇳"
        case .italian: return "🇮🇹"
        case .dutch: return "🇳🇱"
        case .vietnamese: return "🇻🇳"
        case .thai: return "🇹🇭"

        // European Languages
        case .polish: return "🇵🇱"
        case .czech: return "🇨🇿"
        case .hungarian: return "🇭🇺"
        case .romanian: return "🇷🇴"
        case .bulgarian: return "🇧🇬"
        case .croatian: return "🇭🇷"
        case .serbian: return "🇷🇸"
        case .slovak: return "🇸🇰"
        case .slovenian: return "🇸🇮"
        case .lithuanian: return "🇱🇹"
        case .latvian: return "🇱🇻"
        case .estonian: return "🇪🇪"
        case .finnish: return "🇫🇮"
        case .danish: return "🇩🇰"
        case .swedish: return "🇸🇪"
        case .norwegian: return "🇳🇴"
        case .icelandic: return "🇮🇸"
        case .greek: return "🇬🇷"
        case .turkish: return "🇹🇷"
        case .ukrainian: return "🇺🇦"
        case .belarusian: return "🇧🇾"

        // Asian Languages
        case .cantonese: return "🇭🇰"
        case .indonesian: return "🇮🇩"
        case .malay: return "🇲🇾"
        case .tagalog: return "🇵🇭"
        case .bengali: return "🇧🇩"
        case .urdu: return "🇵🇰"
        case .punjabi: return "🇮🇳"
        case .gujarati: return "🇮🇳"
        case .tamil: return "🇮🇳"
        case .telugu: return "🇮🇳"
        case .kannada: return "🇮🇳"
        case .malayalam: return "🇮🇳"
        case .marathi: return "🇮🇳"
        case .nepali: return "🇳🇵"
        case .sinhala: return "🇱🇰"
        case .myanmar: return "🇲🇲"
        case .khmer: return "🇰🇭"
        case .lao: return "🇱🇦"
        case .mongolian: return "🇲🇳"
        case .kazakh: return "🇰🇿"
        case .uzbek: return "🇺🇿"
        case .kyrgyz: return "🇰🇬"
        case .tajik: return "🇹🇯"
        case .persian: return "🇮🇷"
        case .pashto: return "🇦🇫"
        case .hebrew: return "🇮🇱"

        // African Languages
        case .swahili: return "🇰🇪"
        case .amharic: return "🇪🇹"
        case .yoruba: return "🇳🇬"
        case .igbo: return "🇳🇬"
        case .hausa: return "🇳🇬"
        case .somali: return "🇸🇴"
        case .afrikaans: return "🇿🇦"
        case .zulu: return "🇿🇦"
        case .xhosa: return "🇿🇦"

        // American Languages
        case .portuguese_brazil: return "🇧🇷"
        case .french_canada: return "🇨🇦"
        case .quechua: return "🇵🇪"
        case .guarani: return "🇵🇾"
        case .haitian_creole: return "🇭🇹"

        // Other Languages
        case .esperanto: return "🌍"
        case .latin: return "🏛️"
        case .irish: return "🇮🇪"
        case .welsh: return "🏴󠁧󠁢󠁷󠁬󠁳󠁿"
        case .basque: return "🇪🇸"
        case .catalan: return "🇪🇸"
        case .galician: return "🇪🇸"
        case .maltese: return "🇲🇹"
        }
    }
}

/// Maps RecognizedLanguage to TranslationLanguage for source language detection
extension TranslationLanguage {
    static func from(recognizedLanguage: RecognizedLanguage) -> TranslationLanguage {
        switch recognizedLanguage {
        case .english:
            return .english
        case .chinese:
            return .chineseSimplified
        case .cantonese:
            return .cantonese
        case .japanese:
            return .japanese
        case .unknown:
            return .english // Default to English for unknown languages
        }
    }
}

/// Google Translate API response models
struct GoogleTranslateResponse: Codable {
    let sentences: [GoogleTranslateSentence]
    let src: String
    let confidence: Double?
    let ld_result: GoogleLanguageDetectionResult?

    enum CodingKeys: String, CodingKey {
        case sentences, src, confidence, ld_result
    }
}

struct GoogleTranslateSentence: Codable {
    let trans: String?
    let orig: String?
    let src_translit: String?

    enum CodingKeys: String, CodingKey {
        case trans, orig, src_translit
    }
}

struct GoogleLanguageDetectionResult: Codable {
    let srclangs: [String]
    let srclangs_confidences: [Double]
    let extended_srclangs: [String]
}

/// Translation state for UI feedback
enum TranslationState: Equatable {
    case idle
    case translating
    case completed(String)
    case failed(Error)

    static func == (lhs: TranslationState, rhs: TranslationState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.translating, .translating):
            return true
        case (.completed(let lhsText), .completed(let rhsText)):
            return lhsText == rhsText
        case (.failed(let lhsError), .failed(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - Translation Service

/// Service for handling translation requests to Google Translate API
@MainActor
class TranslationService: ObservableObject {

    // MARK: - Properties

    @Published var isTranslating = false
    @Published var selectedTargetLanguage: TranslationLanguage = .spanish {
        didSet {
            // Save to UserDefaults when changed
            UserDefaults.standard.set(selectedTargetLanguage.rawValue, forKey: "selectedTargetLanguage")
        }
    }
    @Published var isTranslationEnabled = true {
        didSet {
            // Save to UserDefaults when changed
            UserDefaults.standard.set(isTranslationEnabled, forKey: "isTranslationEnabled")
        }
    }

    private let baseURL: String
    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()

    // Translation cache for performance optimization
    private var translationCache: [String: String] = [:]
    private let cacheQueue = DispatchQueue(label: "translation.cache", qos: .utility)

    // MARK: - Initialization

    init(baseURL: String = "https://translate.google.com") {
        self.baseURL = baseURL

        // Configure URLSession with timeout and headers
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0

        // Add User-Agent header to mimic browser requests
        config.httpAdditionalHeaders = [
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"
        ]

        self.session = URLSession(configuration: config)

        // Load saved preferences
        loadSavedPreferences()

        print("🌐 TranslationService: Initialized with Google Translate API")
    }

    /// Load saved translation preferences from UserDefaults
    private func loadSavedPreferences() {
        // Load selected target language
        if let savedLanguageCode = UserDefaults.standard.string(forKey: "selectedTargetLanguage"),
           let savedLanguage = TranslationLanguage.allCases.first(where: { $0.rawValue == savedLanguageCode }) {
            selectedTargetLanguage = savedLanguage
            print("🔄 TranslationService: Loaded saved target language: \(savedLanguage.displayName)")
        }

        // Load translation enabled state
        if UserDefaults.standard.object(forKey: "isTranslationEnabled") != nil {
            isTranslationEnabled = UserDefaults.standard.bool(forKey: "isTranslationEnabled")
            print("🔄 TranslationService: Loaded translation enabled state: \(isTranslationEnabled)")
        }
    }
    
    // MARK: - Public Methods
    
    /// Translates text using the Google Translate API
    /// - Parameters:
    ///   - text: The text to translate
    ///   - sourceLanguage: The source language (use 'auto' for auto-detection)
    ///   - targetLanguage: The target language for translation
    /// - Returns: The translated text
    func translate(
        text: String,
        from sourceLanguage: TranslationLanguage,
        to targetLanguage: TranslationLanguage
    ) async throws -> String {

        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TranslationError.emptyText
        }

        guard sourceLanguage != targetLanguage else {
            // No translation needed if source and target are the same
            return text
        }

        print("🔄 TranslationService: Translating '\(text)' from \(sourceLanguage.displayName) to \(targetLanguage.displayName)")

        isTranslating = true
        defer { isTranslating = false }

        do {
            let translatedText = try await performGoogleTranslation(
                text: text,
                source: sourceLanguage.rawValue,
                target: targetLanguage.rawValue
            )

            print("✅ TranslationService: Translation completed: '\(translatedText)'")
            return translatedText

        } catch {
            print("❌ TranslationService: Translation failed: \(error)")
            throw error
        }
    }

    /// Translates text with auto-detection of source language
    /// - Parameters:
    ///   - text: The text to translate
    ///   - targetLanguage: The target language for translation
    /// - Returns: The translated text
    func translateWithAutoDetection(
        text: String,
        to targetLanguage: TranslationLanguage
    ) async throws -> String {

        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TranslationError.emptyText
        }

        print("🔄 TranslationService: Auto-translating '\(text)' to \(targetLanguage.displayName)")

        isTranslating = true
        defer { isTranslating = false }

        do {
            let translatedText = try await performGoogleTranslation(
                text: text,
                source: "auto", // Google Translate auto-detection
                target: targetLanguage.rawValue
            )

            print("✅ TranslationService: Auto-translation completed: '\(translatedText)'")
            return translatedText

        } catch {
            print("❌ TranslationService: Auto-translation failed: \(error)")
            throw error
        }
    }
    
    /// Detects the language of the given text using Google Translate
    /// - Parameter text: The text to analyze
    /// - Returns: The detected language and confidence score
    func detectLanguage(text: String) async throws -> (language: TranslationLanguage, confidence: Double) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TranslationError.emptyText
        }

        print("🔍 TranslationService: Detecting language for '\(text)'")

        do {
            // Use Google Translate with auto-detection to detect language
            let response = try await performGoogleTranslationWithResponse(
                text: text,
                source: "auto",
                target: "en" // Translate to English to get source language detection
            )

            // Map Google language code to our TranslationLanguage enum
            let detectedLanguageCode = response.src
            let translationLanguage = TranslationLanguage.allCases.first { $0.rawValue == detectedLanguageCode } ?? .english
            let confidence = response.confidence ?? 0.9 // Default confidence if not provided

            print("✅ TranslationService: Detected language: \(translationLanguage.displayName) (confidence: \(confidence))")
            return (language: translationLanguage, confidence: confidence)

        } catch {
            print("❌ TranslationService: Language detection failed: \(error)")
            throw error
        }
    }
    
    // MARK: - Private Methods

    private func performGoogleTranslation(text: String, source: String, target: String) async throws -> String {
        let response = try await performGoogleTranslationWithResponse(text: text, source: source, target: target)

        // Extract translated text from sentences
        let translatedText = response.sentences
            .compactMap { $0.trans }
            .joined()

        guard !translatedText.isEmpty else {
            throw TranslationError.invalidResponse
        }

        return translatedText
    }

    private func performGoogleTranslationWithResponse(text: String, source: String, target: String) async throws -> GoogleTranslateResponse {
        // Build Google Translate URL with query parameters (matching original implementation)
        var urlComponents = URLComponents(string: "\(baseURL)/translate_a/single")!
        urlComponents.queryItems = [
            URLQueryItem(name: "client", value: "at"),
            URLQueryItem(name: "dt", value: "t"),    // return sentences
            URLQueryItem(name: "dt", value: "rm"),   // add translit to sentences
            URLQueryItem(name: "dj", value: "1")     // result as pretty json
        ]

        guard let url = urlComponents.url else {
            throw TranslationError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/x-www-form-urlencoded;charset=utf-8", forHTTPHeaderField: "Content-Type")

        // Build POST body parameters (matching original implementation)
        var bodyComponents = URLComponents()
        bodyComponents.queryItems = [
            URLQueryItem(name: "sl", value: source), // source language
            URLQueryItem(name: "tl", value: target), // target language
            URLQueryItem(name: "q", value: text)     // text to translate
        ]

        request.httpBody = bodyComponents.percentEncodedQuery?.data(using: .utf8)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw TranslationError.invalidResponse
        }

        // Handle rate limiting
        if httpResponse.statusCode == 429 {
            let responseText = String(data: data, encoding: .utf8) ?? ""
            print("⚠️ TranslationService: Rate limited by Google Translate: \(responseText)")
            throw TranslationError.rateLimited
        }

        guard httpResponse.statusCode == 200 else {
            let responseText = String(data: data, encoding: .utf8) ?? ""
            print("❌ TranslationService: HTTP \(httpResponse.statusCode): \(responseText)")
            throw TranslationError.serverError(httpResponse.statusCode)
        }

        do {
            let translationResponse = try JSONDecoder().decode(GoogleTranslateResponse.self, from: data)
            return translationResponse
        } catch {
            print("❌ TranslationService: Failed to decode response: \(error)")
            print("Raw response: \(String(data: data, encoding: .utf8) ?? "Unable to decode")")
            throw TranslationError.decodingError(error)
        }
    }
}

// MARK: - Translation Errors

enum TranslationError: LocalizedError {
    case emptyText
    case invalidURL
    case invalidResponse
    case serverError(Int)
    case networkError(Error)
    case decodingError(Error)
    case rateLimited
    case unsupportedLanguage(String)

    var errorDescription: String? {
        switch self {
        case .emptyText:
            return "Text cannot be empty"
        case .invalidURL:
            return "Invalid translation service URL"
        case .invalidResponse:
            return "Invalid response from translation service"
        case .serverError(let code):
            return "Translation service error (HTTP \(code))"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .decodingError(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .rateLimited:
            return "Translation rate limit exceeded. Please try again later."
        case .unsupportedLanguage(let message):
            return "Unsupported language: \(message)"
        }
    }
}
