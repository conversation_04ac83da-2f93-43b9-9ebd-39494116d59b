//
//  TranslationServiceConfig.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/7/26.
//

import Foundation

// MARK: - Translation Service Types

/// Available translation services
enum TranslationServiceType: String, CaseIterable, Identifiable {
    case google = "google"
    case googleCloud = "google_cloud"
    case nllb = "nllb"

    var id: String { rawValue }

    var displayName: String {
        switch self {
        case .google:
            return "Google Translate (Free)"
        case .googleCloud:
            return "Google Cloud Translation"
        case .nllb:
            return "NLLB (Local)"
        }
    }

    var description: String {
        switch self {
        case .google:
            return "Google's free translation service (rate limited)"
        case .googleCloud:
            return "Google Cloud Translation API (paid, reliable)"
        case .nllb:
            return "Meta's NLLB model with high-quality translations"
        }
    }
}

// MARK: - Service Configuration

/// Configuration for translation services
struct TranslationServiceConfig {
    let preferredService: TranslationServiceType
    let fallbackService: TranslationServiceType?
    let nllbBaseURL: String
    let languageServiceMapping: [TranslationLanguage: TranslationServiceType]
    
    static let `default` = TranslationServiceConfig(
        preferredService: .google,
        fallbackService: .nllb,
        nllbBaseURL: "http://rockerwww.ddns.net:7860",
        languageServiceMapping: [:]
    )
    
    /// Get the service to use for a specific language
    func serviceForLanguage(_ language: TranslationLanguage) -> TranslationServiceType {
        return languageServiceMapping[language] ?? preferredService
    }
    
    /// Get the service to use for a language pair
    func serviceForLanguagePair(from: TranslationLanguage, to: TranslationLanguage) -> TranslationServiceType {
        // Check if either language has a specific service mapping
        if let fromService = languageServiceMapping[from] {
            return fromService
        }
        if let toService = languageServiceMapping[to] {
            return toService
        }
        return preferredService
    }
}

// MARK: - NLLB Language Mapping

/// Maps Google Translate language codes to NLLB language codes
struct NLLBLanguageMapper {
    static let googleToNLLB: [String: String] = [
        "en": "eng_Latn",
        "es": "spa_Latn",
        "fr": "fra_Latn",
        "de": "deu_Latn",
        "pt": "por_Latn",
        "ru": "rus_Cyrl",
        "ja": "jpn_Jpan",
        "ko": "kor_Hang",
        "zh-CN": "zho_Hans",
        "zh-TW": "zho_Hant",
        "yue": "yue_Hant",
        "ar": "arb_Arab",
        "hi": "hin_Deva",
        "it": "ita_Latn",
        "nl": "nld_Latn",
        "vi": "vie_Latn",
        "th": "tha_Thai"
    ]
    
    static let nllbToGoogle: [String: String] = Dictionary(
        uniqueKeysWithValues: googleToNLLB.map { ($1, $0) }
    )
    
    static func toNLLB(_ googleCode: String) -> String {
        return googleToNLLB[googleCode] ?? googleCode
    }
    
    static func toGoogle(_ nllbCode: String) -> String {
        return nllbToGoogle[nllbCode] ?? nllbCode
    }
    
    static func isNLLBSupported(_ googleCode: String) -> Bool {
        return googleToNLLB[googleCode] != nil
    }
}

// MARK: - Predefined Configurations

extension TranslationServiceConfig {

    // MARK: - CONFIGURATION SECTION
    // ========================================
    // EDIT THIS SECTION TO CHANGE TRANSLATION BEHAVIOR
    // ========================================

    /// 🎯 MAIN CONFIGURATION - Change this to modify app behavior
    /// Available options: .googlePrimary, .googleCloudPrimary, .nllbPrimary, .asianNLLB, .optimalHybrid, .googleOnly, .googleCloudOnly, .nllbOnly
    ///
    /// 🚀 COLD LAUNCH RECOMMENDED: Use .optimalHybrid for best balance
    /// ✅ Google Translate implementation fixed - should work properly now
    /// 🔐 API keys protected using iOS security features
    static let appDefault = googleCloudPrimary

    /// 🌐 NLLB SERVER URL - Change this to point to your NLLB server
    private static let nllbServerURL = "http://rockerwww.ddns.net:7860"

    // ========================================
    // PREDEFINED CONFIGURATIONS (Don't modify unless you know what you're doing)
    // ========================================

    /// Google Translate (free) as primary, NLLB as fallback
    static let googlePrimary = TranslationServiceConfig(
        preferredService: .google,
        fallbackService: .nllb,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [:]
    )

    /// Google Cloud Translation as primary, NLLB as fallback
    static let googleCloudPrimary = TranslationServiceConfig(
        preferredService: .googleCloud,
        fallbackService: .google,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [:]
    )

    /// NLLB as primary, Google as fallback
    static let nllbPrimary = TranslationServiceConfig(
        preferredService: .nllb,
        fallbackService: .google,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [:]
    )

    /// Use NLLB for Asian languages, Google for others
    static let asianNLLB = TranslationServiceConfig(
        preferredService: .google,
        fallbackService: .nllb,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [
            .japanese: .nllb,
            .korean: .nllb,
            .chineseSimplified: .nllb,
            .chineseTraditional: .nllb,
            .cantonese: .nllb,
            .thai: .nllb,
            .vietnamese: .nllb,
            .hindi: .nllb,
            .arabic: .nllb
        ]
    )

    /// 🌟 RECOMMENDED: Optimal hybrid configuration
    /// Uses NLLB for languages it handles particularly well, Google for broader language support
    static let optimalHybrid = TranslationServiceConfig(
        preferredService: .google,
        fallbackService: .nllb,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [
            // Use NLLB for languages it handles particularly well
            .japanese: .nllb,
            .korean: .nllb,
            .chineseSimplified: .nllb,
            .chineseTraditional: .nllb,
            .cantonese: .nllb,
            .arabic: .nllb,
            .hindi: .nllb,
            .thai: .nllb,
            .vietnamese: .nllb,

            // Use Google for broader European language support
            .english: .google,
            .spanish: .google,
            .french: .google,
            .german: .google,
            .portuguese: .google,
            .russian: .google,
            .italian: .google,
            .dutch: .google
        ]
    )

    /// Google Translate only (no fallback) - Maximum language coverage
    static let googleOnly = TranslationServiceConfig(
        preferredService: .google,
        fallbackService: nil,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [:]
    )

    /// Google Cloud Translation only (no fallback) - Reliable, paid service
    static let googleCloudOnly = TranslationServiceConfig(
        preferredService: .googleCloud,
        fallbackService: nil,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [:]
    )

    /// NLLB only (no fallback) - High quality for supported languages only
    static let nllbOnly = TranslationServiceConfig(
        preferredService: .nllb,
        fallbackService: nil,
        nllbBaseURL: nllbServerURL,
        languageServiceMapping: [:]
    )
}


