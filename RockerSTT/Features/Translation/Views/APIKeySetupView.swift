//
//  APIKeySetupView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/7/26.
//

import SwiftUI

#if DEBUG

struct APIKeySetupView: View {
    @State private var apiKey: String = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSuccess = false
    @State private var currentKeyStatus = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "key.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.blue)

                        Text("Secure API Key Setup")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Store your Google Cloud Translation API key securely")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()

                    // Current Status
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Status:")
                            .font(.headline)

                        ScrollView {
                            Text(currentKeyStatus)
                                .font(.system(.caption, design: .monospaced))
                                .foregroundColor(currentKeyStatus.contains("✅") ? .green : .orange)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .frame(maxHeight: 120)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .padding(.horizontal)
                
                // API Key Input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Google Cloud API Key:")
                        .font(.headline)
                    
                    SecureField("Enter your API key", text: $apiKey)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(.body, design: .monospaced))
                    
                    Text("Get your API key from Google Cloud Console → APIs & Services → Credentials")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button(action: {
                        storeAPIKey()
                    }) {
                        HStack {
                            Image(systemName: "lock.shield")
                            Text("Store Securely in Keychain")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(apiKey.isEmpty)
                    
                    Button(action: {
                        testAPIKey()
                    }) {
                        HStack {
                            Image(systemName: "checkmark.circle")
                            Text("Test API Key")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(apiKey.isEmpty)
                    
                    Button(action: {
                        deleteAPIKey()
                    }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("Delete Stored Key")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    
                    Button(action: {
                        refreshStatus()
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("Refresh Status")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
                
                // Security Info
                VStack(alignment: .leading, spacing: 8) {
                    Text("🔐 Security Information:")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("✅ Keys stored in iOS Keychain (encrypted)")
                        Text("✅ Protected by device security")
                        Text("✅ Survives app updates")
                        Text("✅ Cannot be extracted from app binary")
                        Text("⚠️ This setup view only appears in DEBUG builds")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                    .padding(.horizontal)

                    // Bottom padding for scroll
                    Color.clear
                        .frame(height: 50)
                }
            }
            .navigationTitle("API Key Setup")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                refreshStatus()
            }
            .alert("API Key Setup", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Methods
    
    private func storeAPIKey() {
        guard !apiKey.isEmpty else { return }
        
        if SecureAPIKeyManager.setGoogleCloudAPIKey(apiKey) {
            alertMessage = "✅ API key stored securely in Keychain!"
            isSuccess = true
            apiKey = "" // Clear the input for security
            refreshStatus()
        } else {
            alertMessage = "❌ Failed to store API key in Keychain"
            isSuccess = false
        }
        showingAlert = true
    }
    
    private func testAPIKey() {
        guard !apiKey.isEmpty else { return }
        
        Task {
            do {
                let service = HybridTranslationService(googleCloudAPIKey: apiKey)
                let result = try await service.translate(
                    text: "Hello",
                    from: .english,
                    to: .spanish
                )
                
                await MainActor.run {
                    alertMessage = "✅ API key works! Translation: '\(result)'"
                    isSuccess = true
                    showingAlert = true
                }
            } catch {
                await MainActor.run {
                    alertMessage = "❌ API key test failed: \(error.localizedDescription)"
                    isSuccess = false
                    showingAlert = true
                }
            }
        }
    }
    
    private func deleteAPIKey() {
        if SecureAPIKeyManager.deleteAPIKey(for: "GoogleCloudTranslation") {
            alertMessage = "✅ API key deleted from Keychain"
            isSuccess = true
            refreshStatus()
        } else {
            alertMessage = "❌ Failed to delete API key"
            isSuccess = false
        }
        showingAlert = true
    }
    
    private func refreshStatus() {
        let storedKey = SecureAPIKeyManager.getAPIKey(for: "GoogleCloudTranslation")
        let envKey = SecureAPIKeyManager.getAPIKeyFromEnvironment("GOOGLE_CLOUD_API_KEY")
        let plistKey = SecureAPIKeyManager.getAPIKeyFromInfoPlist("GoogleCloudAPIKey")
        
        var status = "🔍 API Key Sources:\n"
        
        if let _ = storedKey {
            status += "✅ Keychain: Key found (secure)\n"
        } else {
            status += "❌ Keychain: No key stored\n"
        }
        
        if let _ = envKey {
            status += "✅ Environment: Key found\n"
        } else {
            status += "❌ Environment: No GOOGLE_CLOUD_API_KEY\n"
        }
        
        if let _ = plistKey {
            status += "✅ Info.plist: Key found\n"
        } else {
            status += "❌ Info.plist: No GoogleCloudAPIKey\n"
        }
        
        let finalKey = SecureAPIKeyManager.getGoogleCloudAPIKey()
        if !finalKey.isEmpty {
            status += "\n🎯 Active Key: Found (\(finalKey.count) chars)"
        } else {
            status += "\n⚠️ Active Key: None configured"
        }
        
        currentKeyStatus = status
    }
}

#Preview {
    APIKeySetupView()
}

#endif
