//
//  LanguageSelectionView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI

/// Language selection dropdown component for translation target language
struct LanguageSelectionView: View {
    @ObservedObject var translationService: HybridTranslationService
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Toggle button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    // Flag and language name
                    HStack(spacing: 6) {
                        Text(translationService.selectedTargetLanguage.flag)
                            .font(.system(size: 16))
                        
                        Text(translationService.selectedTargetLanguage.displayName)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.colors.textPrimary)
                    }
                    
                    Spacer()
                    
                    // Chevron icon
                    Image(systemName: "chevron.down")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.colors.textSecondary)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut(duration: 0.2), value: isExpanded)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(DesignSystem.colors.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(DesignSystem.colors.border, lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            // Dropdown menu
            if isExpanded {
                VStack(spacing: 0) {
                    ForEach(TranslationLanguage.allCases) { language in
                        Button(action: {
                            translationService.selectedTargetLanguage = language
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isExpanded = false
                            }
                        }) {
                            HStack(spacing: 8) {
                                Text(language.flag)
                                    .font(.system(size: 16))
                                
                                Text(language.displayName)
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(DesignSystem.colors.textPrimary)
                                
                                Spacer()
                                
                                // Checkmark for selected language
                                if language == translationService.selectedTargetLanguage {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 12, weight: .semibold))
                                        .foregroundColor(DesignSystem.colors.primaryBlue)
                                }
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 10)
                            .background(
                                Rectangle()
                                    .fill(language == translationService.selectedTargetLanguage ? 
                                          DesignSystem.colors.primaryBlue.opacity(0.1) :
                                          Color.clear)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // Divider between items (except last)
                        if language != TranslationLanguage.allCases.last {
                            Divider()
                                .background(DesignSystem.colors.border)
                        }
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(DesignSystem.colors.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(DesignSystem.colors.border, lineWidth: 1)
                        )
                )
                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .scale(scale: 0.95, anchor: .top)),
                    removal: .opacity.combined(with: .scale(scale: 0.95, anchor: .top))
                ))
                .zIndex(1000) // Ensure dropdown appears above other content
            }
        }
        .onTapGesture {
            // Close dropdown when tapping outside
            if isExpanded {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded = false
                }
            }
        }
    }
}

/// Compact language selection view for toolbar/header use
struct CompactLanguageSelectionView: View {
    @ObservedObject var translationService: HybridTranslationService
    
    var body: some View {
        Menu {
            ForEach(TranslationLanguage.allCases) { language in
                Button(action: {
                    translationService.selectedTargetLanguage = language
                    HapticPattern.light.trigger()
                }) {
                    Label {
                        Text(language.displayName)
                            .foregroundColor(.primary)
                    } icon: {
                        HStack {
                            Text(language.flag)
                            if language == translationService.selectedTargetLanguage {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 4) {
                Text(translationService.selectedTargetLanguage.flag)
                    .font(.system(size: 14))
                
                Text(translationService.selectedTargetLanguage.displayName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.colors.textPrimary)
                    .lineLimit(1)
                
                Image(systemName: "chevron.down")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(DesignSystem.colors.textSecondary)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(DesignSystem.colors.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(DesignSystem.colors.border, lineWidth: 0.5)
                    )
            )
        }
        .menuStyle(BorderlessButtonMenuStyle())
    }
}

/// Translation toggle switch with language selection
struct TranslationControlView: View {
    @ObservedObject var translationService: HybridTranslationService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Translation toggle
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Translation")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.colors.textPrimary)
                    
                    Text("Automatically translate transcriptions")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.colors.textSecondary)
                }
                
                Spacer()
                
                Toggle("", isOn: $translationService.isTranslationEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: DesignSystem.colors.primaryBlue))
            }
            
            // Language selection (only shown when translation is enabled)
            if translationService.isTranslationEnabled {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Target Language")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.colors.textPrimary)
                    
                    LanguageSelectionView(translationService: translationService)
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(DesignSystem.colors.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(DesignSystem.colors.border, lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.3), value: translationService.isTranslationEnabled)
    }
}

// MARK: - Previews

#Preview("Language Selection") {
    VStack(spacing: 20) {
        LanguageSelectionView(translationService: HybridTranslationService())

        CompactLanguageSelectionView(translationService: HybridTranslationService())

        TranslationControlView(translationService: HybridTranslationService())
    }
    .padding()
    .background(DesignSystem.colors.adaptiveBackground)
}

// MARK: - Language Selection Sheet

/// Full-screen language selection sheet for settings
struct LanguageSelectionSheet: View {
    @ObservedObject var translationService: HybridTranslationService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                ForEach(TranslationLanguage.allCases) { language in
                    HStack(spacing: 12) {
                        // Flag
                        Text(language.flag)
                            .font(.system(size: 24))
                            .frame(width: 32)

                        // Language name
                        VStack(alignment: .leading, spacing: 2) {
                            Text(language.displayName)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.primary)
                        }

                        Spacer()

                        // Checkmark for selected language
                        if language == translationService.selectedTargetLanguage {
                            Image(systemName: "checkmark")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.vertical, 4)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        translationService.selectedTargetLanguage = language
                        HapticPattern.light.trigger()
                        dismiss()
                    }
                }
            }
            .navigationTitle("Select Language")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
