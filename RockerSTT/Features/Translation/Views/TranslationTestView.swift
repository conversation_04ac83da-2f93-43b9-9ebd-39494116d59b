//
//  TranslationTestView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/7/26.
//

import SwiftUI

#if DEBUG

struct TranslationTestView: View {
    @State private var testResults: [String] = []
    @State private var isRunning = false
    @State private var currentTest = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Translation Service Tests")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Test Google Translate API fix and Cantonese translation")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()

                    // Test Buttons
                    VStack(spacing: 12) {
                    But<PERSON>(action: {
                        runCantoneseTest()
                    }) {
                        HStack {
                            Image(systemName: "globe.asia.australia")
                            Text("Test Cantonese Translation")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunning)
                    
                    But<PERSON>(action: {
                        runGoogleTranslateTest()
                    }) {
                        HStack {
                            Image(systemName: "globe")
                            Text("Test Current Service")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunning)

                    Button(action: {
                        runServiceInfoTest()
                    }) {
                        HStack {
                            Image(systemName: "info.circle")
                            Text("Show Service Configuration")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunning)
                    
                    Button(action: {
                        runAllTests()
                    }) {
                        HStack {
                            Image(systemName: "play.circle")
                            Text("Run All Tests")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunning)
                    
                    Button(action: {
                        clearResults()
                    }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("Clear Results")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isRunning)
                    }
                    .padding(.horizontal)

                    // Current Test Status
                    if isRunning {
                        VStack {
                            ProgressView()
                                .scaleEffect(1.2)
                            Text(currentTest)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.top, 8)
                        }
                        .padding()
                    }

                    // Results
                    if !testResults.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Test Results:")
                                .font(.headline)
                                .padding(.horizontal)

                            LazyVStack(alignment: .leading, spacing: 8) {
                                ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                                    HStack(alignment: .top) {
                                        Text("\(index + 1).")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .frame(width: 20, alignment: .leading)

                                        Text(result)
                                            .font(.system(.caption, design: .monospaced))
                                            .foregroundColor(result.contains("✅") ? .green :
                                                           result.contains("❌") ? .red : .primary)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .fixedSize(horizontal: false, vertical: true)
                                    }
                                    .padding(.horizontal)
                                }
                            }
                            .padding(.vertical, 8)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .padding(.horizontal)
                    }

                    // Bottom padding for scroll
                    Color.clear
                        .frame(height: 50)
                }
            }
            .navigationTitle("Translation Tests")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - Test Methods
    
    private func runCantoneseTest() {
        isRunning = true
        currentTest = "Testing Cantonese translation..."
        testResults.removeAll()
        
        Task {
            await performCantoneseTest()
            await MainActor.run {
                isRunning = false
                currentTest = ""
            }
        }
    }
    
    private func runGoogleTranslateTest() {
        isRunning = true
        currentTest = "Testing Google Translate fix..."
        testResults.removeAll()
        
        Task {
            await performGoogleTranslateTest()
            await MainActor.run {
                isRunning = false
                currentTest = ""
            }
        }
    }
    
    private func runAllTests() {
        isRunning = true
        currentTest = "Running all tests..."
        testResults.removeAll()

        Task {
            await performAllTests()
            await MainActor.run {
                isRunning = false
                currentTest = ""
            }
        }
    }

    private func runServiceInfoTest() {
        isRunning = true
        currentTest = "Checking service configuration..."
        testResults.removeAll()

        Task {
            await performServiceInfoTest()
            await MainActor.run {
                isRunning = false
                currentTest = ""
            }
        }
    }
    
    private func clearResults() {
        testResults.removeAll()
    }
    
    // MARK: - Test Implementations
    
    @MainActor
    private func performCantoneseTest() async {
        addResult("🇭🇰 Testing Cantonese Translation...")
        addResult("=" * 40)
        
        let service = HybridTranslationService()
        let cantoneseText = "粤语写嘢喺明末清初嗰阵已经有一九二零年代到一九三零年代初亦风行一时由二战时期嘅限于传统左派"
        
        addResult("Original: \(cantoneseText)")
        addResult("")
        
        // Test Cantonese to Simplified Chinese
        addResult("🔄 Cantonese → Simplified Chinese...")
        do {
            let result = try await service.translate(
                text: cantoneseText,
                from: .cantonese,
                to: .chineseSimplified
            )
            addResult("✅ SUCCESS: \(result)")
        } catch {
            addResult("❌ FAILED: \(error)")
        }
        
        addResult("")
        
        // Test Cantonese to English
        addResult("🔄 Cantonese → English...")
        do {
            let result = try await service.translate(
                text: cantoneseText,
                from: .cantonese,
                to: .english
            )
            addResult("✅ SUCCESS: \(result)")
        } catch {
            addResult("❌ FAILED: \(error)")
        }
        
        addResult("=" * 40)
    }
    
    @MainActor
    private func performServiceInfoTest() async {
        addResult("ℹ️ Current Service Configuration")
        addResult("=" * 40)

        let config = TranslationServiceConfig.appDefault

        addResult("📋 Configuration Details:")
        addResult("• Primary Service: \(config.preferredService.displayName)")
        addResult("• Description: \(config.preferredService.description)")

        if let fallback = config.fallbackService {
            addResult("• Fallback Service: \(fallback.displayName)")
        } else {
            addResult("• Fallback Service: None")
        }

        addResult("• NLLB Server: \(config.nllbBaseURL)")

        if !config.languageServiceMapping.isEmpty {
            addResult("• Custom Language Mappings: \(config.languageServiceMapping.count)")
            for (language, service) in config.languageServiceMapping {
                addResult("  - \(language.flag) \(language.displayName) → \(service.displayName)")
            }
        } else {
            addResult("• Custom Language Mappings: None")
        }

        addResult("")
        addResult("🧪 Testing Current Service:")

        // Test a simple translation to verify the service is working
        let service = HybridTranslationService()

        do {
            addResult("🔄 Testing 'Hello' → Spanish...")
            let result = try await service.translate(
                text: "Hello",
                from: .english,
                to: .spanish
            )
            addResult("✅ SUCCESS: '\(result)'")
            addResult("🎉 Current service (\(config.preferredService.displayName)) is working!")
        } catch {
            addResult("❌ FAILED: \(error)")
            addResult("⚠️ Current service (\(config.preferredService.displayName)) has issues")
        }

        addResult("=" * 40)
    }

    @MainActor
    private func performGoogleTranslateTest() async {
        addResult("🌐 Testing Current Translation Service...")
        addResult("=" * 40)

        let config = TranslationServiceConfig.appDefault
        addResult("Using: \(config.preferredService.displayName)")
        addResult("")
        
        let service = HybridTranslationService()
        
        let testCases = [
            ("Hello world", TranslationLanguage.english, TranslationLanguage.spanish),
            ("Good morning", TranslationLanguage.english, TranslationLanguage.french),
            ("Thank you", TranslationLanguage.english, TranslationLanguage.german)
        ]
        
        for (text, from, to) in testCases {
            addResult("🔄 '\(text)' (\(from.displayName) → \(to.displayName))")
            do {
                let result = try await service.translate(text: text, from: from, to: to)
                addResult("✅ SUCCESS: '\(result)'")
            } catch {
                addResult("❌ FAILED: \(error)")
            }
            addResult("")
        }
        
        addResult("=" * 40)
    }
    
    @MainActor
    private func performAllTests() async {
        await performGoogleTranslateTest()
        addResult("")
        await performCantoneseTest()
        
        // Test language detection
        addResult("")
        addResult("🔍 Testing Language Detection...")
        addResult("=" * 40)
        
        let service = HybridTranslationService()
        let testTexts = [
            ("Hello world", "English"),
            ("你好世界", "Chinese"),
            ("粤语写嘢", "Cantonese")
        ]
        
        for (text, expected) in testTexts {
            addResult("🔄 Detecting: '\(text)' (expected: \(expected))")
            do {
                let (language, confidence) = try await service.detectLanguage(text: text)
                addResult("✅ Detected: \(language.displayName) (confidence: \(String(format: "%.2f", confidence)))")
            } catch {
                addResult("❌ FAILED: \(error)")
            }
            addResult("")
        }
        
        addResult("=" * 40)
        addResult("🎉 All tests completed!")
    }
    
    private func addResult(_ text: String) {
        testResults.append(text)
    }
}

// MARK: - String Extension

extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// MARK: - Preview

#Preview {
    TranslationTestView()
}

#endif
