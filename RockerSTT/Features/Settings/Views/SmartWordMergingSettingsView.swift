//
//  SmartWordMergingSettingsView.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//

import SwiftUI

/// Settings view for Smart Word Merging configuration
/// Requirements: 5.4, 2.4
struct SmartWordMergingSettingsView: View {
    @StateObject private var preferences = SmartWordMergingPreferences.shared
    @State private var showingPresetInfo = false
    @State private var showingAdvancedSettings = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Clean Alabaster background with French Lilac gradient
                DesignSystem.brandColors.adaptiveBackgroundGradient
                    .ignoresSafeArea()
                
                Form {
                    // Configuration Preset Section
                    Section(header: sectionHeader("Configuration Preset")) {
                    Picker("Preset", selection: $preferences.currentPreset) {
                        ForEach(SmartWordMergingPreferences.ConfigurationPreset.allCases, id: \.self) { preset in
                            VStack(alignment: .leading) {
                                Text(preset.displayName)
                                    .font(.headline)
                                Text(preset.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .tag(preset)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Button(action: { showingPresetInfo = true }) {
                        HStack {
                            Image(systemName: "info.circle")
                            Text("About Presets")
                        }
                    }
                    .foregroundColor(DesignSystem.brandColors.orchid)
                }
                
                // Core Settings Section
                Section(header: sectionHeader("Core Settings")) {
                    BrandToggle(
                        "Enable Word Merging",
                        isOn: $preferences.enableWordMerging,
                        description: "Enable or disable the smart word merging feature entirely"
                    )
                    
                    BrandToggle(
                        "Enable Caching",
                        isOn: $preferences.enableCaching,
                        description: "Cache dictionary lookup results for better performance",
                        isEnabled: preferences.enableWordMerging
                    )
                    
                    BrandToggle(
                        "Enable Language Detection",
                        isOn: $preferences.enableLanguageDetection,
                        description: "Automatically detect language for better merging accuracy",
                        isEnabled: preferences.enableWordMerging
                    )
                }
                
                // Performance Settings Section
                Section(header: sectionHeader("Performance")) {
                    BrandToggle(
                        "Show Performance Warnings",
                        isOn: $preferences.showPerformanceWarnings,
                        description: "Display warnings when performance thresholds are exceeded"
                    )
                    
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Cache Size")
                                .font(DesignSystem.typography.body)
                                .foregroundColor(DesignSystem.colors.textPrimary)
                            Text("Maximum number of cached dictionary results")
                                .font(DesignSystem.typography.caption)
                                .foregroundColor(DesignSystem.colors.textSecondary)
                        }
                        Spacer()
                        Stepper(value: $preferences.maxCacheSize, in: 100...5000, step: 100) {
                            Text("\(preferences.maxCacheSize)")
                                .foregroundColor(DesignSystem.brandColors.orchid)
                        }
                        .accentColor(DesignSystem.brandColors.orchid)
                    }
                    .disabled(!preferences.enableCaching)
                    .opacity(preferences.enableCaching ? 1.0 : 0.6)
                    .accessibilityElement(children: .combine)
                    .accessibilityLabel("Cache Size: \(preferences.maxCacheSize)")
                    .accessibilityHint("Use stepper to adjust cache size")
                    
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Dictionary Timeout")
                                .font(DesignSystem.typography.body)
                                .foregroundColor(DesignSystem.colors.textPrimary)
                            Text("Maximum time to wait for dictionary lookup")
                                .font(DesignSystem.typography.caption)
                                .foregroundColor(DesignSystem.colors.textSecondary)
                        }
                        Spacer()
                        VStack(alignment: .trailing) {
                            Text("\(Int(preferences.dictionaryTimeout * 1000))ms")
                                .foregroundColor(DesignSystem.brandColors.orchid)
                            Slider(value: $preferences.dictionaryTimeout, in: 0.005...0.050, step: 0.001)
                                .frame(width: 100)
                                .accentColor(DesignSystem.brandColors.orchid)
                        }
                    }
                    .disabled(!preferences.enableWordMerging)
                    .opacity(preferences.enableWordMerging ? 1.0 : 0.6)
                    .accessibilityElement(children: .combine)
                    .accessibilityLabel("Dictionary Timeout: \(Int(preferences.dictionaryTimeout * 1000)) milliseconds")
                    .accessibilityHint("Use slider to adjust timeout duration")
                }
                
                // Language Settings Section
                Section(header: sectionHeader("Languages")) {
                    ForEach([RecognizedLanguage.english, .chinese, .japanese], id: \.self) { language in
                        BrandToggle(
                            language.displayName,
                            isOn: Binding(
                                get: { preferences.enabledLanguages.contains(language) },
                                set: { isEnabled in
                                    if isEnabled {
                                        preferences.enabledLanguages.insert(language)
                                    } else {
                                        preferences.enabledLanguages.remove(language)
                                    }
                                }
                            ),
                            isEnabled: preferences.enableWordMerging
                        )
                    }
                }
                
                // Advanced Settings Section
                Section(header: sectionHeader("Advanced")) {
                    BrandToggle(
                        "Aggressive Merging",
                        isOn: $preferences.aggressiveMerging,
                        description: "More aggressive word merging for better performance",
                        isEnabled: preferences.enableWordMerging
                    )
                    
                    BrandToggle(
                        "Preserve Original Spacing",
                        isOn: $preferences.preserveOriginalSpacing,
                        description: "Maintain original spacing when merging fails",
                        isEnabled: preferences.enableWordMerging
                    )
                    
                    BrandToggle(
                        "Debug Logging",
                        isOn: $preferences.enableDebugLogging,
                        description: "Enable detailed debug logging for troubleshooting"
                    )
                    
                    Button("Show Advanced Settings") {
                        showingAdvancedSettings = true
                    }
                    .foregroundColor(DesignSystem.brandColors.orchid)
                }
                
                // Actions Section
                Section(header: sectionHeader("Actions")) {
                    Button("Reset to Defaults") {
                        preferences.resetToDefaults()
                    }
                    .foregroundColor(.red) // Maintain system red for destructive actions
                    
                    Button("Export Settings") {
                        exportSettings()
                    }
                    .foregroundColor(DesignSystem.brandColors.orchid)
                }
                
                // Status Section
                Section(header: sectionHeader("Status")) {
                    HStack {
                        Text("Current Preset")
                        Spacer()
                        Text(preferences.currentPreset.displayName)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Word Merging")
                        Spacer()
                        Text(preferences.enableWordMerging ? "Enabled" : "Disabled")
                            .foregroundColor(preferences.enableWordMerging ? .green : .red)
                    }
                    
                    HStack {
                        Text("Enabled Languages")
                        Spacer()
                        Text("\(preferences.enabledLanguages.count)")
                            .foregroundColor(.secondary)
                    }
                }
                }
                .scrollContentBackground(.hidden) // Hide default form background
            }
            .navigationTitle("Smart Word Merging")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingPresetInfo) {
                PresetInfoView()
            }
            .sheet(isPresented: $showingAdvancedSettings) {
                AdvancedSettingsView(preferences: preferences)
            }
        }
    }
    
    // MARK: - Helper Functions
    
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(DesignSystem.typography.settingsHeader)
            .fontWeight(.medium)
            .foregroundColor(DesignSystem.brandColors.persianPurple)
    }
    
    private func exportSettings() {
        let settings = preferences.exportSettings()
        let jsonData = try? JSONSerialization.data(withJSONObject: settings, options: .prettyPrinted)
        
        if let jsonData = jsonData,
           let jsonString = String(data: jsonData, encoding: .utf8) {
            UIPasteboard.general.string = jsonString
            // Could show a toast or alert here
            print("Settings exported to clipboard")
        }
    }
}

struct PresetInfoView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    ForEach(SmartWordMergingPreferences.ConfigurationPreset.allCases, id: \.self) { preset in
                        VStack(alignment: .leading, spacing: 8) {
                            Text(preset.displayName)
                                .font(.headline)
                            Text(preset.description)
                                .font(.body)
                                .foregroundColor(.secondary)
                            
                            // Show key settings for each preset
                            let config = preset.configuration
                            VStack(alignment: .leading, spacing: 4) {
                                Text("• Caching: \(config.enableCaching ? "Enabled" : "Disabled")")
                                Text("• Language Detection: \(config.enableLanguageDetection ? "Enabled" : "Disabled")")
                                // Performance monitoring removed
                                Text("• Cache Size: \(config.maxCacheSize)")
                                Text("• Timeout: \(Int(config.dictionaryTimeout * 1000))ms")
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
                .padding()
            }
            .navigationTitle("Configuration Presets")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct AdvancedSettingsView: View {
    @ObservedObject var preferences: SmartWordMergingPreferences
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ZStack {
                // Clean Alabaster background with French Lilac gradient
                DesignSystem.brandColors.adaptiveBackgroundGradient
                    .ignoresSafeArea()
                
                Form {
                    // Performance monitoring section removed
                    
                    Section(header: sectionHeader("Debug Options")) {
                        BrandToggle(
                            "Debug Logging",
                            isOn: $preferences.enableDebugLogging
                        )
                    }
                    
                    Section(header: sectionHeader("Behavior")) {
                        BrandToggle(
                            "Aggressive Merging",
                            isOn: $preferences.aggressiveMerging
                        )
                        BrandToggle(
                            "Preserve Original Spacing",
                            isOn: $preferences.preserveOriginalSpacing
                        )
                    }
                
                Section(header: sectionHeader("Current Configuration")) {
                    let config = preferences.getCurrentConfiguration()
                    Text("Word Merging: \(config.enableWordMerging ? "Enabled" : "Disabled")")
                    Text("Caching: \(config.enableCaching ? "Enabled" : "Disabled")")
                    Text("Language Detection: \(config.enableLanguageDetection ? "Enabled" : "Disabled")")
                    // Performance monitoring status removed
                    Text("Cache Size: \(config.maxCacheSize)")
                    Text("Dictionary Timeout: \(Int(config.dictionaryTimeout * 1000))ms")
                    Text("Enabled Languages: \(config.enabledLanguages.map { $0.displayName }.joined(separator: ", "))")
                }
                }
                .scrollContentBackground(.hidden) // Hide default form background
            }
            .navigationTitle("Advanced Settings")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
    
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(DesignSystem.typography.settingsHeader)
            .fontWeight(.medium)
            .foregroundColor(DesignSystem.brandColors.persianPurple)
    }
}

struct SmartWordMergingSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SmartWordMergingSettingsView()
    }
}
