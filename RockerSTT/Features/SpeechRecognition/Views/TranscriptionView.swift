//
//  TranscriptionView.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import AlertToast
import SwiftUI

struct TranscriptionView: View {
    @StateObject private var viewModel = SpeechRecognitionViewModel()
    @State private var showingSettings = false
    @State private var showingDeveloperDebug = false
    @State private var scrollDebounceTimer: Timer?

    var body: some View {
        VStack(spacing: 0) {
            // Main transcription area - continuous scrollable text
            transcriptionTextArea

            // Controls
            controlsView
        }
        .brandRecordingGradientBackground()
        .task {
            await viewModel.requestPermissions()
        }
        .toast(isPresenting: .constant(viewModel.errorMessage != nil), duration: 3, tapToDismiss: true){
            // Enhanced toast with brand colors
            AlertToast(
                displayMode: .hud,
                type: .error(.brandAmber),
                title: viewModel.errorMessage ?? "",
                style: .style(
                    backgroundColor: .brandAmber.opacity(0.1),
                    titleColor: .brandPersianPurple,
                    subTitleColor: .brandOrchid
                )
            )
        }
    }



    private var transcriptionTextArea: some View {
        ScrollViewReader { proxy in
            if viewModel.transcriptionEntries.isEmpty {
                // Enhanced empty state
                EmptyStateView(type: determineEmptyStateType())
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // List view with transcription entries using TranscriptionCard
                List {
                    ForEach(Array(viewModel.transcriptionEntries.enumerated()), id: \.element.id) { index, entry in
                        TranscriptionCard(
                            entry: entry,
                            meetingStartTime: viewModel.meetingStartTimeForRelativeTimestamp,
                            connectionState: viewModel.connectionState,
                            viewModel: viewModel
                        )
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                            .listRowInsets(EdgeInsets(top: DesignSystem.spacing.cardSpacing/2, leading: DesignSystem.spacing.medium, bottom: DesignSystem.spacing.cardSpacing/2, trailing: DesignSystem.spacing.medium))
                            .id(entry.id)
                            .transition(.asymmetric(
                                insertion: AnimationManager.isReducedMotionEnabled 
                                    ? .opacity 
                                    : .scale(scale: 0.8)
                                        .combined(with: .opacity)
                                        .combined(with: .move(edge: .bottom))
                                        .combined(with: .offset(y: 20)),
                                removal: AnimationManager.isReducedMotionEnabled 
                                    ? .opacity 
                                    : .scale(scale: 0.8)
                                        .combined(with: .opacity)
                                        .combined(with: .move(edge: .top))
                            ))
                            .animation(
                                AnimationManager.cardEntranceStaggered(index: index % 3), // Staggered animation for multiple entries
                                value: viewModel.transcriptionEntries.count
                            )
                    }
                }
                .listStyle(.plain)
                .background(Color.clear)
                .animation(AnimationManager.accessibleCardEntrance, value: viewModel.transcriptionEntries.count)
                .onChange(of: viewModel.transcriptionEntries.count) {
                    // Auto-scroll to the last entry when new entries are added
                    scrollDebounceTimer?.invalidate()
                    scrollDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                        if let lastEntry = viewModel.transcriptionEntries.last {
                            DispatchQueue.main.async {
                                withAnimation(AnimationManager.accessibleButtonPress) {
                                    proxy.scrollTo(lastEntry.id, anchor: .bottom)
                                }
                            }
                        }
                    }
                }
                .onReceive(viewModel.textUpdatePublisher) { _ in
                    // Auto-scroll when any entry's text is updated (more reliable than onChange)
                    scrollDebounceTimer?.invalidate()
                    scrollDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                        if let lastEntry = viewModel.transcriptionEntries.last {
                            DispatchQueue.main.async {
                                withAnimation(AnimationManager.accessibleButtonPress) {
                                    proxy.scrollTo(lastEntry.id, anchor: .bottom)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    private var controlsView: some View {
        VStack(spacing: DesignSystem.spacing.small) {
            HStack {
                Spacer()

                // Enhanced Record button with brand styling and improved animations
                Button(action: recordingButtonAction) {
                    recordingButtonContent
                }
                .buttonStyle(RecordingButtonStyle())
                .disabled(!viewModel.permissionGranted || viewModel.connectionState == .connecting)



                Spacer()
            }
        }
        .padding(DesignSystem.spacing.xSmall)
    }

    // MARK: - Recording Button Components

    private func recordingButtonAction() {
        // Enhanced haptic feedback
        if viewModel.isRecording {
            HapticFeedbackManager.shared.recordingEnded()
            viewModel.stopRecording()
        } else {
            HapticFeedbackManager.shared.recordingStarted()
            viewModel.startRecording()
        }
    }

    private var recordingButtonContent: some View {
        ZStack {
            // Outer animated visualization rings (always present but hidden when not recording)
            // Outer ring with enhanced animation
            Circle()
                .stroke(DesignSystem.brandColors.orchid.opacity(0.3), lineWidth: 2)
                .frame(width: 100, height: 100)
                .scaleEffect(viewModel.isRecording ? 1.3 : 1.0)
                .opacity(viewModel.isRecording ? 0.8 : 0.0)
                .animation(
                    AnimationManager.isReducedMotionEnabled
                        ? .none
                        : AnimationManager.recordingRings,
                    value: viewModel.isRecording
                )

            // Middle ring with staggered animation
            Circle()
                .stroke(DesignSystem.brandColors.orchid.opacity(0.5), lineWidth: 3)
                .frame(width: 88, height: 88)
                .scaleEffect(viewModel.isRecording ? 1.15 : 1.0)
                .opacity(viewModel.isRecording ? 0.9 : 0.0)
                .animation(
                    AnimationManager.isReducedMotionEnabled
                        ? .none
                        : AnimationManager.recordingRings.delay(0.2),
                    value: viewModel.isRecording
                )

            // Inner ring with final staggered animation
            Circle()
                .stroke(DesignSystem.brandColors.orchid.opacity(0.7), lineWidth: 2)
                .frame(width: 82, height: 82)
                .scaleEffect(viewModel.isRecording ? 1.08 : 1.0)
                .opacity(viewModel.isRecording ? 1.0 : 0.0)
                .animation(
                    AnimationManager.isReducedMotionEnabled
                        ? .none
                        : AnimationManager.recordingRings.delay(0.4),
                    value: viewModel.isRecording
                )

            // Main button circle with Persian Purple gradient and shadow
            Circle()
                .fill(
                    viewModel.isRecording
                        ? AnyShapeStyle(DesignSystem.brandColors.amber.opacity(0.9))
                        : AnyShapeStyle(DesignSystem.brandColors.primaryGradient)
                )
                .frame(width: 76, height: 76) // iPhone 16 optimized size (minimum 44pt touch target)
                .shadow(
                    color: DesignSystem.brandColors.persianPurple.opacity(0.4),
                    radius: 12,
                    x: 0,
                    y: 6
                )
                .overlay(
                    // Subtle inner glow effect
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    (viewModel.isRecording
                                        ? DesignSystem.brandColors.amber
                                        : DesignSystem.brandColors.persianPurple).opacity(0.2),
                                    Color.clear
                                ]),
                                center: .center,
                                startRadius: 38,
                                endRadius: 55
                            )
                        )
                        .frame(width: 110, height: 110)
                        .opacity(viewModel.isRecording ? 1.0 : 0.7)
                        .animation(AnimationManager.recordingStateChange, value: viewModel.isRecording)
                )

            // Button content with proper visual feedback states
            Group {
                if viewModel.connectionState == .connecting {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                } else {
                    Image(systemName: viewModel.isRecording ? "stop.fill" : "mic.fill")
                        .font(.system(size: 28, weight: .medium)) // iPhone 16 optimized icon size
                        .foregroundColor(.white)
                        .scaleEffect(viewModel.isRecording ? 0.9 : 1.0)
                        .animation(AnimationManager.accessibleButtonPress, value: viewModel.isRecording)
                }
            }
        }
        .frame(width: 100, height: 100) // Fixed frame size for consistent positioning
        .accessibilityLabel(recordingButtonAccessibilityLabel)
        .accessibilityHint(recordingButtonAccessibilityHint)
        .accessibilityValue(recordingButtonAccessibilityValue)
        .accessibilityAddTraits(.isButton)
        .accessibilityIdentifier("main_recording_button")
        .scaleEffect(viewModel.isRecording ? 1.05 : 1.0)
        .animation(AnimationManager.recordingStateChange, value: viewModel.isRecording)
    }

    // MARK: - Helper Methods
    
    private func determineEmptyStateType() -> EmptyStateType {
        if !viewModel.permissionGranted {
            return .noPermission
        } else if viewModel.connectionState == .connecting {
            return .connecting
        } else if viewModel.isRecording {
            return .listening
        } else if let errorMessage = viewModel.errorMessage {
            return .error(errorMessage)
        } else {
            return .ready
        }
    }
    
    // MARK: - Accessibility Helpers
    
    private var recordingButtonAccessibilityLabel: String {
        if viewModel.connectionState == .connecting {
            return "Connecting to speech recognition service"
        } else if viewModel.isRecording {
            return "Stop recording"
        } else {
            return "Start recording"
        }
    }
    
    private var recordingButtonAccessibilityHint: String {
        if !viewModel.permissionGranted {
            return "Microphone permission required"
        } else if viewModel.connectionState == .connecting {
            return "Please wait while connecting"
        } else if viewModel.isRecording {
            return "Double tap to stop recording your speech"
        } else {
            return "Double tap to start recording your speech"
        }
    }
    
    private var recordingButtonAccessibilityValue: String {
        switch viewModel.connectionState {
        case .connecting:
            return "Connecting"
        case .connected:
            return viewModel.isRecording ? "Recording" : "Ready"
        case .disconnected:
            return "Disconnected"
        case .reconnecting:
            return "Reconnecting"
        }
    }


}

// MARK: - Custom Button Style for Recording Button

struct RecordingButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    TranscriptionView()
}

#Preview("TranscriptionCard Sample") {
    TranscriptionCard(
        entry: TranscriptionEntry(
            timestamp: Date(timeIntervalSince1970: 1616116620.4),
            text: "测测测测测测试，测测测测测测试，测测测测测测试，测测测测测测试，测测测测测测试，测测测测测测试，测测测测测测试，测测测测测测试，测测测测测测试，",
            parsedContent: ParsedContent(
                language: .cantonese,
                emotion: .happy,
                audioType: .speech,
                cleanText: "测测测测测测试"
            )
        ),
        meetingStartTime: Date(timeIntervalSince1970: 1616116616.4),
        connectionState: .connected,
        viewModel: nil
    )
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackground)
}
