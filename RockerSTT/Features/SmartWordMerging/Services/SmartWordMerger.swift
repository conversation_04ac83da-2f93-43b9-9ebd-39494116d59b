//
//  SmartWordMerger.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/7/18.
//

import Foundation
import UIKit

// MARK: - Error Types

/// Errors that can occur during word merging operations
enum WordMergingError: Error, LocalizedError {
    case dictionaryUnavailable
    case processingTimeout
    case invalidInput(String)
    case cacheError(String)
    case tokenTooLong(Int)
    
    var errorDescription: String? {
        switch self {
        case .dictionaryUnavailable:
            return "Dictionary service is unavailable"
        case .processingTimeout:
            return "Dictionary lookup timed out"
        case .invalidInput(let details):
            return "Invalid input: \(details)"
        case .cacheError(let details):
            return "Cache error: \(details)"
        case .tokenTooLong(let length):
            return "Token too long for processing: \(length) characters"
        }
    }
}

/// Configuration for word merging behavior and error handling
/// Requirements: 5.4, 2.4
struct MergingConfiguration {
    // Core feature toggles
    let enableWordMerging: Bool
    let enableCaching: Bool
    let enableLanguageDetection: Bool
    let enablePerformanceLogging: Bool
    let enablePerformanceMonitoring: Bool

    // Performance settings
    let maxCacheSize: Int
    let dictionaryTimeout: TimeInterval
    let maxTokenLength: Int
    let performanceReportingInterval: TimeInterval

    // Behavior settings
    let fallbackToSpacing: Bool
    let aggressiveMerging: Bool
    let preserveOriginalSpacing: Bool

    // Language-specific settings
    let enabledLanguages: Set<RecognizedLanguage>
    let chineseWordSegmentation: Bool
    let japaneseWordSegmentation: Bool

    // User experience settings
    let showPerformanceWarnings: Bool
    let enableDebugLogging: Bool
    
    // MARK: - Preset Configurations

    /// Default configuration for production use
    static let `default` = MergingConfiguration(
        enableWordMerging: true,
        enableCaching: true,
        enableLanguageDetection: true,
        enablePerformanceLogging: false,
        enablePerformanceMonitoring: false,
        maxCacheSize: 1000,
        dictionaryTimeout: 0.01, // 10ms
        maxTokenLength: 50,
        performanceReportingInterval: 300.0, // 5 minutes
        fallbackToSpacing: true,
        aggressiveMerging: false,
        preserveOriginalSpacing: false,
        enabledLanguages: [.english, .chinese, .japanese],
        chineseWordSegmentation: true,
        japaneseWordSegmentation: true,
        showPerformanceWarnings: false,
        enableDebugLogging: false
    )

    /// Debug configuration with enhanced logging and monitoring
    static let debug = MergingConfiguration(
        enableWordMerging: true,
        enableCaching: true,
        enableLanguageDetection: true,
        enablePerformanceLogging: true,
        enablePerformanceMonitoring: true,
        maxCacheSize: 1000,
        dictionaryTimeout: 0.01, // 10ms
        maxTokenLength: 50,
        performanceReportingInterval: 60.0, // 1 minute
        fallbackToSpacing: true,
        aggressiveMerging: false,
        preserveOriginalSpacing: false,
        enabledLanguages: [.english, .chinese, .japanese],
        chineseWordSegmentation: true,
        japaneseWordSegmentation: true,
        showPerformanceWarnings: true,
        enableDebugLogging: true
    )

    /// Performance-optimized configuration for high-volume usage
    static let performance = MergingConfiguration(
        enableWordMerging: true,
        enableCaching: true,
        enableLanguageDetection: false, // Disabled for performance
        enablePerformanceLogging: false,
        enablePerformanceMonitoring: true,
        maxCacheSize: 2000, // Larger cache
        dictionaryTimeout: 0.005, // 5ms - faster timeout
        maxTokenLength: 30, // Shorter tokens for speed
        performanceReportingInterval: 600.0, // 10 minutes
        fallbackToSpacing: true,
        aggressiveMerging: true, // More aggressive for speed
        preserveOriginalSpacing: false,
        enabledLanguages: [.english], // English only for performance
        chineseWordSegmentation: false,
        japaneseWordSegmentation: false,
        showPerformanceWarnings: true,
        enableDebugLogging: false
    )

    /// Conservative configuration with minimal processing
    static let conservative = MergingConfiguration(
        enableWordMerging: true,
        enableCaching: true,
        enableLanguageDetection: true,
        enablePerformanceLogging: false,
        enablePerformanceMonitoring: false,
        maxCacheSize: 500, // Smaller cache
        dictionaryTimeout: 0.02, // 20ms - longer timeout for accuracy
        maxTokenLength: 100, // Longer tokens allowed
        performanceReportingInterval: 900.0, // 15 minutes
        fallbackToSpacing: true,
        aggressiveMerging: false,
        preserveOriginalSpacing: true, // Preserve original spacing
        enabledLanguages: [.english, .chinese, .japanese],
        chineseWordSegmentation: true,
        japaneseWordSegmentation: true,
        showPerformanceWarnings: false,
        enableDebugLogging: false
    )

    /// Disabled configuration - word merging turned off
    static let disabled = MergingConfiguration(
        enableWordMerging: false,
        enableCaching: false,
        enableLanguageDetection: false,
        enablePerformanceLogging: false,
        enablePerformanceMonitoring: false,
        maxCacheSize: 0,
        dictionaryTimeout: 0.0,
        maxTokenLength: 0,
        performanceReportingInterval: 0.0,
        fallbackToSpacing: true,
        aggressiveMerging: false,
        preserveOriginalSpacing: true,
        enabledLanguages: [],
        chineseWordSegmentation: false,
        japaneseWordSegmentation: false,
        showPerformanceWarnings: false,
        enableDebugLogging: false
    )
}

/// Core component responsible for intelligent word merging in ASR partial results
/// Analyzes consecutive partial results and merges split words when appropriate
class SmartWordMerger {
    
    // MARK: - Private Properties

    private let textChecker = UITextChecker()
    private let cache: WordMergingCache
    private let configuration: MergingConfiguration
    
    // MARK: - Initialization
    
    /// Initialize SmartWordMerger with custom configuration
    /// - Parameter configuration: Configuration for merging behavior (defaults to .default)
    init(configuration: MergingConfiguration = .default) {
        self.configuration = configuration
        self.cache = WordMergingCache(maxCacheSize: configuration.maxCacheSize)
    }
    
    // MARK: - Public Methods

    /// Resets the internal state
    /// Call this when starting a new transcription session
    func resetState() {
        // Clear cache if needed
        cache.clearCache()
        if configuration.enablePerformanceLogging {
            print("🔄 SmartWordMerger: State reset")
        }
    }

    /// Determines if two tokens should be merged based on dictionary validation
    /// - Parameters:
    ///   - previousToken: The last token from the previous partial result
    ///   - currentToken: The first token from the current partial result
    ///   - language: The detected language for language-aware processing
    /// - Returns: True if the tokens should be merged, false otherwise
    func shouldMergeWords(previousToken: String, currentToken: String, language: RecognizedLanguage) -> Bool {
        do {
            // Skip merging for non-space-based languages
            guard shouldApplyMergingForLanguage(language) else {
                return false
            }
            
            // Validate input tokens with error handling
            try validateTokens(previousToken: previousToken, currentToken: currentToken)
            
            // Clean tokens (remove punctuation and whitespace)
            let cleanPreviousToken = cleanToken(previousToken)
            let cleanCurrentToken = cleanToken(currentToken)
            
            // Skip if tokens are empty after cleaning
            guard !cleanPreviousToken.isEmpty && !cleanCurrentToken.isEmpty else {
                if configuration.enablePerformanceLogging {
                    print("⚠️ SmartWordMerger: Tokens empty after cleaning")
                }
                return false
            }
            
            // First check enhanced dictionary for known patterns
            if let mergedWord = EnhancedWordDictionary.getMergedWord(prefix: cleanPreviousToken, suffix: cleanCurrentToken) {
                if configuration.enablePerformanceLogging {
                    print("🔍 SmartWordMerger: Found pattern '\(cleanPreviousToken)' + '\(cleanCurrentToken)' = '\(mergedWord)' in enhanced dictionary")
                }
                return true
            }

            // Create combined word for system dictionary check
            let combinedWord = cleanPreviousToken + cleanCurrentToken

            // Check enhanced dictionary first (faster than system dictionary)
            if EnhancedWordDictionary.isValidCompoundWord(combinedWord) {
                if configuration.enablePerformanceLogging {
                    print("🔍 SmartWordMerger: Found '\(combinedWord)' in enhanced compound words")
                }
                return true
            }

            // Fall back to system dictionary check
            let isValid = try isValidEnglishWordWithErrorHandling(combinedWord)

            if configuration.enablePerformanceLogging {
                print("🔍 SmartWordMerger: System dictionary check '\(cleanPreviousToken)' + '\(cleanCurrentToken)' = '\(combinedWord)': \(isValid ? "valid" : "invalid")")
            }

            return isValid
            
        } catch {
            // Handle all errors with graceful fallback
            return handleMergingError(error, fallbackBehavior: .separateWithSpace)
        }
    }
    
    /// Main entry point for merging partial results
    /// - Parameters:
    ///   - previousPartial: The previous partial result (optional)
    ///   - currentPartial: The current partial result
    ///   - language: The detected language
    /// - Returns: The merged result text
    func mergePartialResults(previousPartial: String?, currentPartial: String, language: RecognizedLanguage) -> String {
        // Check if word merging is enabled
        if !configuration.enableWordMerging {
            return appendPartials(previous: previousPartial, current: currentPartial)
        }

        // For non-English languages (Cantonese/Mandarin), no merging needed
        guard language == .english else {
            return appendPartials(previous: previousPartial, current: currentPartial)
        }

        // Handle edge cases
        guard let previousPartial = previousPartial, !previousPartial.isEmpty else {
            return currentPartial
        }

        guard !currentPartial.isEmpty else {
            return previousPartial
        }

        // Extract last token from previous and first token from current
        let previousTokens = previousPartial.split(separator: " ")
        let currentTokens = currentPartial.split(separator: " ")

        guard let lastToken = previousTokens.last,
              let firstToken = currentTokens.first else {
            // Fallback to simple append if we can't extract tokens
            return appendPartials(previous: previousPartial, current: currentPartial)
        }

        let lastTokenStr = String(lastToken)
        let firstTokenStr = String(firstToken)
        let combinedToken = lastTokenStr + firstTokenStr

        if configuration.enablePerformanceLogging {
            print("🔍 SmartWordMerger: Checking merge: '\(lastTokenStr)' + '\(firstTokenStr)' = '\(combinedToken)'")
        }

        if isValidEnglishWord(combinedToken) {
            // Merge: combine the tokens and reconstruct the full sentence
            let previousWithoutLast = previousTokens.dropLast().joined(separator: " ")
            let currentWithoutFirst = currentTokens.dropFirst().joined(separator: " ")

            var result = ""
            if !previousWithoutLast.isEmpty {
                result += previousWithoutLast + " "
            }
            result += combinedToken
            if !currentWithoutFirst.isEmpty {
                result += " " + currentWithoutFirst
            }

            if configuration.enablePerformanceLogging {
                print("🔍 SmartWordMerger: Merged successfully → '\(result)'")
            }

            return result
        } else {
            // No merge: add space between previous and current
            let result = previousPartial + " " + currentPartial

            if configuration.enablePerformanceLogging {
                print("🔍 SmartWordMerger: No merge, added space → '\(result)'")
            }

            return result
        }
    }



    /// Checks if a word is valid in English using UITextChecker
    /// - Parameter word: The word to validate
    /// - Returns: True if the word is valid, false otherwise
    private func isValidEnglishWord(_ word: String) -> Bool {
        do {
            return try isValidEnglishWordWithErrorHandling(word)
        } catch {
            // Graceful fallback for new cross-partial merging
            if configuration.enablePerformanceLogging {
                print("⚠️ SmartWordMerger: Word validation error: \(error.localizedDescription)")
            }
            return false // Conservative fallback - don't merge if validation fails
        }
    }

    /// Appends current partial to previous partial without spacing
    /// Used primarily for non-English languages (Cantonese/Mandarin) that don't require spaces
    /// - Parameters:
    ///   - previous: The previous partial result (optional)
    ///   - current: The current partial result
    /// - Returns: The combined text without additional spacing
    private func appendPartials(previous: String?, current: String) -> String {
        guard let previous = previous, !previous.isEmpty else {
            return current
        }

        guard !current.isEmpty else {
            return previous
        }

        // For Cantonese/Mandarin and other non-space-based languages,
        // simply concatenate without adding spaces
        return previous + current
    }

    /// Merges words within a text by checking adjacent word pairs
    /// - Parameters:
    ///   - text: The text to process
    ///   - language: The detected language
    /// - Returns: The text with merged words
    private func mergeWordsInText(_ text: String, language: RecognizedLanguage) -> String {
        // Split text into tokens
        let tokens = text.components(separatedBy: .whitespaces).filter { !$0.isEmpty }

        // Need at least 2 tokens to merge
        guard tokens.count >= 2 else {
            return text
        }

        var resultTokens: [String] = []
        var i = 0

        while i < tokens.count {
            // If we're at the last token, just add it
            if i == tokens.count - 1 {
                resultTokens.append(tokens[i])
                i += 1
                continue
            }

            // Check if current token and next token should be merged
            let currentToken = tokens[i]
            let nextToken = tokens[i + 1]

            if shouldMergeWords(previousToken: currentToken, currentToken: nextToken, language: language) {
                // Create merged token
                let cleanCurrentToken = cleanToken(currentToken)
                let cleanNextToken = cleanToken(nextToken)

                let mergedToken: String
                if let enhancedResult = EnhancedWordDictionary.getMergedWord(prefix: cleanCurrentToken, suffix: cleanNextToken) {
                    mergedToken = enhancedResult
                    if configuration.enablePerformanceLogging {
                        print("🔍 SmartWordMerger: Using enhanced dictionary result: '\(cleanCurrentToken)' + '\(cleanNextToken)' = '\(enhancedResult)'")
                    }
                } else {
                    mergedToken = cleanCurrentToken + cleanNextToken
                    if configuration.enablePerformanceLogging {
                        print("🔍 SmartWordMerger: Using direct concatenation: '\(cleanCurrentToken)' + '\(cleanNextToken)' = '\(mergedToken)'")
                    }
                }

                resultTokens.append(mergedToken)
                i += 2 // Skip both tokens since we merged them
            } else {
                // No merge, just add current token
                resultTokens.append(currentToken)
                i += 1
            }
        }

        return resultTokens.joined(separator: " ")
    }

    // MARK: - Private Methods
    
    /// Validates if a word is a valid English word using UITextChecker with comprehensive error handling
    /// - Parameter word: The word to validate
    /// - Returns: True if the word is valid, false otherwise
    /// - Throws: WordMergingError for various error conditions
    internal func isValidEnglishWordWithErrorHandling(_ word: String) throws -> Bool {
        // Check cache first if caching is enabled
        if configuration.enableCaching {
            do {
                if let cachedResult = try cache.getCachedResult(for: word) {
                    return cachedResult
                }
            } catch {
                // Cache error - continue with dictionary lookup but log the issue
                if configuration.enablePerformanceLogging {
                    print("⚠️ SmartWordMerger: Cache error for word '\(word)': \(error.localizedDescription)")
                }
            }
        }
        
        // Validate word length to prevent performance issues
        guard word.count <= configuration.maxTokenLength else {
            throw WordMergingError.tokenTooLong(word.count)
        }
        
        // Perform dictionary lookup with timeout protection
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let range = NSRange(location: 0, length: word.utf16.count)
        let misspelledRange: NSRange
        
        do {
            // Wrap dictionary call to catch potential system errors
            misspelledRange = textChecker.rangeOfMisspelledWord(
                in: word,
                range: range,
                startingAt: 0,
                wrap: false,
                language: "en"
            )
        } catch {
            // Dictionary service unavailable
            throw WordMergingError.dictionaryUnavailable
        }
        
        let elapsedTime = CFAbsoluteTimeGetCurrent() - startTime

        // Check for timeout
        if elapsedTime > configuration.dictionaryTimeout {
            throw WordMergingError.processingTimeout
        }
        
        // Word is valid if no misspelled range was found
        let isValid = misspelledRange.location == NSNotFound
        
        // Cache the result if caching is enabled
        if configuration.enableCaching {
            do {
                try cache.setCachedResult(for: word, isValid: isValid)
            } catch {
                // Cache error - log but don't fail the operation
                if configuration.enablePerformanceLogging {
                    print("⚠️ SmartWordMerger: Failed to cache result for '\(word)': \(error.localizedDescription)")
                }
            }
        }
        
        if configuration.enablePerformanceLogging {
            print("📊 SmartWordMerger: Dictionary lookup for '\(word)': \(isValid ? "valid" : "invalid") in \(String(format: "%.2f", elapsedTime * 1000))ms")
        }
        
        return isValid
    }
    

    
    /// Extracts the first token from a text string
    /// - Parameter text: The text to extract from
    /// - Returns: The first token
    private func extractFirstToken(from text: String) -> String {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        let components = trimmedText.components(separatedBy: .whitespacesAndNewlines)
        return components.first ?? ""
    }
    
    /// Extracts the last token from a text string
    /// - Parameter text: The text to extract from
    /// - Returns: The last token
    internal func extractLastToken(from text: String) -> String {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        let components = trimmedText.components(separatedBy: .whitespacesAndNewlines)
        return components.last ?? ""
    }
    
    /// Cleans a token by removing punctuation and extra whitespace
    /// - Parameter token: The token to clean
    /// - Returns: The cleaned token
    private func cleanToken(_ token: String) -> String {
        return token.trimmingCharacters(in: .whitespacesAndNewlines)
            .trimmingCharacters(in: .punctuationCharacters)
            .lowercased()
    }
    
    /// Determines if word merging should be applied for the given language
    /// - Parameter language: The detected language
    /// - Returns: True if merging should be applied, false otherwise
    private func shouldApplyMergingForLanguage(_ language: RecognizedLanguage) -> Bool {
        switch language {
        case .english, .japanese:
            return true // Space-based languages where splitting can occur
        case .chinese, .cantonese:
            return false // No spaces, no splitting issues
        case .unknown:
            return true // Default to applying merging for safety
        }
    }
    
    // MARK: - Input Validation Methods
    
    /// Validates input tokens for merging operations
    /// - Parameters:
    ///   - previousToken: The previous token to validate
    ///   - currentToken: The current token to validate
    /// - Throws: WordMergingError for invalid inputs
    private func validateTokens(previousToken: String, currentToken: String) throws {
        // Check for empty tokens
        guard !previousToken.isEmpty else {
            throw WordMergingError.invalidInput("Previous token is empty")
        }
        
        guard !currentToken.isEmpty else {
            throw WordMergingError.invalidInput("Current token is empty")
        }
        
        // Check token length limits
        guard previousToken.count <= configuration.maxTokenLength else {
            throw WordMergingError.tokenTooLong(previousToken.count)
        }
        
        guard currentToken.count <= configuration.maxTokenLength else {
            throw WordMergingError.tokenTooLong(currentToken.count)
        }
        
        // Check for tokens that are only punctuation or whitespace
        let cleanPrevious = previousToken.trimmingCharacters(in: .whitespacesAndNewlines.union(.punctuationCharacters))
        let cleanCurrent = currentToken.trimmingCharacters(in: .whitespacesAndNewlines.union(.punctuationCharacters))
        
        guard !cleanPrevious.isEmpty else {
            throw WordMergingError.invalidInput("Previous token contains only punctuation/whitespace")
        }
        
        guard !cleanCurrent.isEmpty else {
            throw WordMergingError.invalidInput("Current token contains only punctuation/whitespace")
        }
    }
    
    /// Validates a partial result string
    /// - Parameter partialResult: The partial result to validate
    /// - Throws: WordMergingError for invalid inputs
    private func validatePartialResult(_ partialResult: String) throws {
        // Check for extremely long inputs that could cause performance issues
        guard partialResult.count <= configuration.maxTokenLength * 10 else {
            throw WordMergingError.invalidInput("Partial result too long: \(partialResult.count) characters")
        }
    }
    
    // MARK: - Error Handling Methods
    
    /// Fallback behavior options for error handling
    private enum FallbackBehavior {
        case separateWithSpace
        case returnCurrentPartial(String)
        case returnAppendedPartials(String?, String)
        case returnEmpty
    }
    
    /// Handles merging errors with appropriate fallback behavior
    /// - Parameters:
    ///   - error: The error that occurred
    ///   - fallbackBehavior: The fallback behavior to apply
    /// - Returns: Appropriate fallback result based on the behavior
    private func handleMergingError(_ error: Error, fallbackBehavior: FallbackBehavior) -> Bool {
        if configuration.enablePerformanceLogging {
            print("⚠️ SmartWordMerger: Handling error - \(error.localizedDescription)")
        }
        
        // Log specific error types for debugging
        if let mergingError = error as? WordMergingError {
            switch mergingError {
            case .dictionaryUnavailable:
                if configuration.enablePerformanceLogging {
                    print("📚 SmartWordMerger: Dictionary unavailable - falling back to space separation")
                }
            case .processingTimeout:
                if configuration.enablePerformanceLogging {
                    print("⏱️ SmartWordMerger: Dictionary lookup timeout - falling back to space separation")
                }
            case .tokenTooLong(let length):
                if configuration.enablePerformanceLogging {
                    print("📏 SmartWordMerger: Token too long (\(length) chars) - falling back to space separation")
                }
            case .invalidInput(let details):
                if configuration.enablePerformanceLogging {
                    print("❌ SmartWordMerger: Invalid input (\(details)) - falling back to space separation")
                }
            case .cacheError(let details):
                if configuration.enablePerformanceLogging {
                    print("💾 SmartWordMerger: Cache error (\(details)) - continuing without cache")
                }
            }
        }
        
        // Apply fallback behavior based on configuration
        if configuration.fallbackToSpacing {
            return false // Don't merge - use space separation
        } else {
            return false // Conservative fallback
        }
    }
    
    /// Handles merging errors that return strings
    /// - Parameters:
    ///   - error: The error that occurred
    ///   - fallbackBehavior: The fallback behavior to apply
    /// - Returns: Appropriate fallback string based on the behavior
    private func handleMergingError(_ error: Error, fallbackBehavior: FallbackBehavior) -> String {
        if configuration.enablePerformanceLogging {
            print("⚠️ SmartWordMerger: Handling error with string fallback - \(error.localizedDescription)")
        }
        
        switch fallbackBehavior {
        case .separateWithSpace:
            return "" // This shouldn't be used for string returns
        case .returnCurrentPartial(let partial):
            return partial
        case .returnAppendedPartials(let previous, let current):
            return appendPartials(previous: previous, current: current)
        case .returnEmpty:
            return ""
        }
    }
}

/// A thread-safe LRU-style cache for storing word merging validation results
/// to avoid redundant dictionary lookups and improve performance.
class WordMergingCache {
    private let maxCacheSize: Int
    private var cache: [String: CacheEntry] = [:]
    private var accessOrder: [String] = []
    private let queue = DispatchQueue(label: "com.rockerstt.wordmergingcache", attributes: .concurrent)
    
    // Error tracking for cache operations
    private var errorCount: Int = 0
    private let maxErrorCount: Int = 10
    
    /// Cache entry containing the validation result and access timestamp
    private struct CacheEntry {
        let isValid: Bool
        let timestamp: Date
        
        init(isValid: Bool) {
            self.isValid = isValid
            self.timestamp = Date()
        }
    }
    
    /// Initialize cache with specified maximum size
    /// - Parameter maxCacheSize: Maximum number of entries to store (default: 1000)
    init(maxCacheSize: Int = 1000) {
        self.maxCacheSize = maxCacheSize
    }
    
    /// Retrieve cached validation result for a combined word
    /// - Parameter combinedWord: The word combination to look up
    /// - Returns: Cached validation result if available, nil otherwise
    /// - Throws: WordMergingError.cacheError if cache operations fail
    func getCachedResult(for combinedWord: String) throws -> Bool? {
        do {
            return try queue.sync {
                // Check if cache is in error state
                guard errorCount < maxErrorCount else {
                    throw WordMergingError.cacheError("Cache disabled due to too many errors")
                }
                
                // Validate input
                guard !combinedWord.isEmpty else {
                    throw WordMergingError.cacheError("Empty cache key")
                }
                
                guard let entry = cache[combinedWord] else {
                    return nil
                }
                
                // Update access order for LRU
                updateAccessOrder(for: combinedWord)
                return entry.isValid
            }
        } catch {
            errorCount += 1
            if let mergingError = error as? WordMergingError {
                throw mergingError
            } else {
                throw WordMergingError.cacheError("Cache read failed: \(error.localizedDescription)")
            }
        }
    }
    
    /// Store validation result in cache
    /// - Parameters:
    ///   - combinedWord: The word combination to cache
    ///   - isValid: Whether the combination is a valid word
    /// - Throws: WordMergingError.cacheError if cache operations fail
    func setCachedResult(for combinedWord: String, isValid: Bool) throws {
        try queue.sync(flags: .barrier) {
            // Check if cache is in error state
            guard errorCount < maxErrorCount else {
                throw WordMergingError.cacheError("Cache disabled due to too many errors")
            }
            
            // Validate input
            guard !combinedWord.isEmpty else {
                throw WordMergingError.cacheError("Empty cache key")
            }
            
            do {
                // Check if we need to evict entries before adding
                if self.cache.count >= self.maxCacheSize && self.cache[combinedWord] == nil {
                    try self.evictOldestEntries()
                }
                
                // Add or update entry
                self.cache[combinedWord] = CacheEntry(isValid: isValid)
                self.updateAccessOrder(for: combinedWord)
                
            } catch {
                self.errorCount += 1
                throw WordMergingError.cacheError("Cache write failed: \(error.localizedDescription)")
            }
        }
    }
    
    /// Clear all cached entries and reset error state
    func clearCache() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
            self.accessOrder.removeAll()
            self.errorCount = 0 // Reset error count when clearing cache
        }
    }
    
    /// Get current cache statistics
    /// - Returns: Dictionary containing cache size and hit rate information
    func getCacheStats() -> [String: Any] {
        return queue.sync {
            return [
                "size": cache.count,
                "maxSize": maxCacheSize,
                "utilizationPercent": Double(cache.count) / Double(maxCacheSize) * 100
            ]
        }
    }
    
    // MARK: - Private Methods
    
    /// Update access order for LRU eviction policy
    /// - Parameter key: The cache key that was accessed
    private func updateAccessOrder(for key: String) {
        // Remove existing entry from access order
        if let index = accessOrder.firstIndex(of: key) {
            accessOrder.remove(at: index)
        }
        
        // Add to end (most recently used)
        accessOrder.append(key)
    }
    
    /// Evict oldest entries when cache is full
    /// - Throws: WordMergingError.cacheError if eviction fails
    private func evictOldestEntries() throws {
        let entriesToEvict = max(1, maxCacheSize / 10) // Evict 10% or at least 1 entry
        
        for _ in 0..<entriesToEvict {
            guard !accessOrder.isEmpty else { break }
            
            let oldestKey = accessOrder.removeFirst()
            cache.removeValue(forKey: oldestKey)
        }
        
        // Verify eviction was successful
        guard cache.count < maxCacheSize else {
            throw WordMergingError.cacheError("Failed to evict entries - cache still full")
        }
    }
}