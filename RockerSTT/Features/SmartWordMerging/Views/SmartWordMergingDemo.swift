//
//  SmartWordMergingDemo.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/7/19.
//  Demo view to showcase smart word merging functionality
//

import SwiftUI

/// Demo view that showcases the smart word merging functionality
/// This can be used for manual testing and demonstration purposes
struct SmartWordMergingDemo: View {
    @State private var previousPartial = ""
    @State private var currentPartial = ""
    @State private var mergedResult = ""
    @State private var isEnabled = true
    @State private var selectedLanguage = RecognizedLanguage.english
    
    private let smartWordMerger = SmartWordMerger()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Text("Smart Word Merging Demo")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Test the word merging functionality with sample inputs")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top)
                
                // Configuration
                VStack(alignment: .leading, spacing: 12) {
                    Text("Configuration")
                        .font(.headline)
                    
                    Toggle("Enable Word Merging", isOn: $isEnabled)
                    
                    HStack {
                        Text("Language:")
                        Picker("Language", selection: $selectedLanguage) {
                            Text("English").tag(RecognizedLanguage.english)
                            Text("Chinese").tag(RecognizedLanguage.chinese)
                            Text("Mixed").tag(RecognizedLanguage.unknown)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // Input Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Input")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Previous Partial:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        TextField("e.g., some", text: $previousPartial)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Partial:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        TextField("e.g., some thing", text: $currentPartial)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // Test Button
                Button(action: performMerging) {
                    Text("Test Word Merging")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                }
                
                // Result Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Result")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Merged Output:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(mergedResult.isEmpty ? "No result yet" : mergedResult)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(Color(.systemGray5))
                            .cornerRadius(8)
                            .font(.system(.body, design: .monospaced))
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // Quick Test Examples
                VStack(alignment: .leading, spacing: 12) {
                    Text("Quick Test Examples")
                        .font(.headline)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(quickTestExamples, id: \.0) { example in
                                Button(action: {
                                    previousPartial = example.0
                                    currentPartial = example.1
                                    performMerging()
                                }) {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("'\(example.0)' + '\(example.1)'")
                                            .font(.caption)
                                            .fontWeight(.medium)
                                        Text("→ '\(example.2)'")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    }
                                    .padding(8)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(8)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
    
    private func performMerging() {
        // Update configuration
//        smartWordMerger.updateConfiguration(
//            SmartWordMergerConfiguration(
//                isEnabled: isEnabled,
//                enablePerformanceLogging: true
//            )
//        )
        
        // Perform merging
        let result = smartWordMerger.mergePartialResults(
            previousPartial: previousPartial,
            currentPartial: currentPartial,
            language: selectedLanguage
        )
        
        mergedResult = result
    }
    
    private let quickTestExamples: [(String, String, String)] = [
        ("some", "some thing", "something"),
        ("any", "any where", "anywhere"),
        ("every", "every one", "everyone"),
        ("no", "no body", "nobody"),
        ("in", "in to", "into"),
        ("up", "up stairs", "upstairs"),
        ("side", "side walk", "sidewalk"),
        ("high", "high way", "highway"),
        ("back", "back ground", "background"),
        ("door", "door way", "doorway")
    ]
}

#if DEBUG
struct SmartWordMergingDemo_Previews: PreviewProvider {
    static var previews: some View {
        SmartWordMergingDemo()
    }
}
#endif
