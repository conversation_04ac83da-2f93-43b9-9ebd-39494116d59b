//
//  FloatingHistoryViewModel.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI
import Combine

/// Enhanced view model for floating history view with unified filtering logic
/// Extends HistoryListViewModel to provide tab-based filtering and smooth animations
class FloatingHistoryViewModel: HistoryListViewModel {

    // MARK: - Published Properties

    /// Tab bar visibility manager
    let visibilityManager = TabBarVisibilityManager()

    /// Animation manager for smooth transitions
    let animationManager = FloatingHistoryAnimationManager()

    /// Performance optimization has been removed for simplicity

    /// Search integration
    @Published var searchResults: [HistorySession] = []
    @Published var isSearchActive: Bool = false

    /// Empty state management
    @Published var isShowingEmptyState: Bool = false
    @Published var emptyStateTransition: Bool = false

    /// Lazy loading state
    @Published var visibleRange: Range<Int> = 0..<0
    @Published var isLoadingMore: Bool = false

    // MARK: - Private Properties

    private var filteringCancellable: AnyCancellable?
    private var isFilteringInProgress: Bool = false
    private var searchCancellable: AnyCancellable?
    private var performanceCancellables = Set<AnyCancellable>()

    // Performance optimization constants
    private let lazyLoadingPageSize: Int = 20
    private let preloadThreshold: Int = 5

    // MARK: - Initialization

    override init(historyStorageService: HistoryStorageService = HistoryStorageService()) {
        super.init(historyStorageService: historyStorageService)
        setupFilteringObserver()
        setupPerformanceOptimization()
    }
    
    // MARK: - Setup

    /// Sets up observer for automatic filtering when sessions or tab selection changes
    private func setupFilteringObserver() {
        filteringCancellable = Publishers.CombineLatest(
            $allSessions,
            $selectedTab
        )
        .debounce(for: .milliseconds(100), scheduler: DispatchQueue.main)
        .sink { [weak self] sessions, selectedTab in
            self?.filterSessions(sessions: sessions, for: selectedTab)
        }
    }
    
    // MARK: - Public Methods
    
    /// Handles tab selection with smooth animation
    /// - Parameter tab: The selected history tab
    override func selectTab(_ tab: HistoryTab) {
        let previousTab = selectedTab
        guard previousTab != tab else { return }

        // Start filtering animation
        isFilteringInProgress = true
        animationManager.startFilteringAnimation(sessionCount: filteredSessions.count)

        withAnimation(DesignSystem.animations.standard) {
            selectedTab = tab
        }

        // Provide haptic feedback
        HapticFeedbackManager.shared.selectionChanged()

        // Announce change to VoiceOver
        AccessibilityHelper.announce("Switched to \(tab.displayName) tab")

        // Reset filtering state after animation completes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.isFilteringInProgress = false
        }
    }
    
    /// Forces refresh of filtered sessions
    func refreshFilteredSessions() {
        filterSessions(sessions: allSessions, for: selectedTab)
    }
    
    /// Handles scroll offset changes for tab bar visibility
    /// - Parameter scrollData: Current scroll data including offset and safe area
    func handleScrollOffsetChange(_ scrollData: ScrollOffsetData) {
        visibilityManager.handleScrollChange(scrollData)
    }

    /// Legacy method for backward compatibility
    /// - Parameter offset: Current scroll offset
    func handleScrollOffsetChange(_ offset: CGFloat) {
        visibilityManager.handleScrollChange(offset)
    }
    
    /// Handles tap gesture to show hidden tab bar
    func handleTapGesture() {
        visibilityManager.handleTapGesture()
    }

    // MARK: - Search Integration

    /// Integrates search results with current tab filtering
    /// - Parameter results: Search results from HistorySearchViewModel
    func updateSearchResults(_ results: [HistorySession]) {
        searchResults = results
        isSearchActive = !results.isEmpty

        // Filter search results based on current tab if needed
        if isSearchActive {
            filterSearchResultsForCurrentTab()
        }
    }

    /// Clears search state and returns to normal filtering
    func clearSearch() {
        searchResults = []
        isSearchActive = false

        // Return to normal tab-based filtering
        filterSessions()
    }

    /// Filters search results based on current tab selection
    private func filterSearchResultsForCurrentTab() {
        guard isSearchActive else { return }

        let filteredResults = searchResults.filter { session in
            switch selectedTab {
            case .recents:
                return true // All search results are valid for recents
            case .favorites:
                return session.isFavourite
            case .saved:
                return session.isSaved
            }
        }

        // Update filtered sessions with search results
        withAnimation(DesignSystem.animations.standard) {
            filteredSessions = filteredResults
        }
    }

    // MARK: - Performance Optimization

    /// Performance optimization setup removed for simplicity
    private func setupPerformanceOptimization() {
        // Performance monitoring removed
    }

    /// Updates visible range for lazy loading optimization
    /// - Parameters:
    ///   - startIndex: First visible item index
    ///   - endIndex: Last visible item index
    func updateVisibleRange(startIndex: Int, endIndex: Int) {
        // Ensure valid range parameters
        guard startIndex <= endIndex else {
            print("⚠️ FloatingHistoryViewModel: Invalid range - startIndex (\(startIndex)) > endIndex (\(endIndex))")
            return
        }

        // Clamp indices to valid bounds
        let clampedStartIndex = max(0, min(startIndex, filteredSessions.count))
        let clampedEndIndex = max(clampedStartIndex, min(endIndex, filteredSessions.count))

        let newRange = clampedStartIndex..<clampedEndIndex
        guard newRange != visibleRange else { return }

        visibleRange = newRange
        // Performance optimizer removed

        // Check if we need to load more sessions
        if clampedEndIndex >= filteredSessions.count - preloadThreshold && !isLoadingMore {
            loadMoreSessionsIfNeeded()
        }
    }

    /// Determines if a session should be loaded based on performance optimization
    /// - Parameter index: Session index
    /// - Returns: True if session should be loaded
    func shouldLoadSession(at index: Int) -> Bool {
        // Always load sessions since performance optimization was removed
        return true
    }

    /// Loads more sessions for lazy loading
    private func loadMoreSessionsIfNeeded() {
        guard !isLoadingMore else { return }

        isLoadingMore = true

        // Simulate loading delay for performance testing
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.isLoadingMore = false
        }
    }

    // Performance metrics handling removed for simplicity

    /// Gets optimized animation for current performance level
    /// - Parameter context: Animation context
    /// - Returns: Optimized animation
    func getOptimizedAnimation(for context: AnimationContext) -> Animation {
        // Return standard animation since performance optimization was removed
        return .easeInOut(duration: 0.3)
    }

    // MARK: - Private Methods

    /// Filters sessions based on current state (search or normal)
    private func filterSessions() {
        // If search is active, filter search results instead of all sessions
        if isSearchActive {
            filterSearchResultsForCurrentTab()
        } else {
            filterSessions(sessions: allSessions, for: selectedTab)
        }
    }

    /// Filters sessions based on the selected tab with smooth animation
    /// - Parameters:
    ///   - sessions: All available sessions
    ///   - tab: The tab to filter for
    private func filterSessions(sessions: [HistorySession], for tab: HistoryTab) {
        let filtered = filterSessions(for: tab, from: sessions)

        // Update empty state with smooth transition
        let willBeEmpty = filtered.isEmpty

        withAnimation(DesignSystem.animations.standard) {
            filteredSessions = filtered
            updateEmptyState(isEmpty: willBeEmpty)
        }
    }

    /// Updates empty state with smooth transitions
    /// - Parameter isEmpty: Whether the current filter results in empty state
    private func updateEmptyState(isEmpty: Bool) {
        if isEmpty != isShowingEmptyState {
            // Trigger transition animation
            emptyStateTransition = true

            // Update state
            isShowingEmptyState = isEmpty

            // Announce state change for accessibility
            announceEmptyStateChange(isEmpty: isEmpty)

            // Reset transition flag after animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.emptyStateTransition = false
            }
        }
    }

    /// Announces empty state changes for accessibility
    /// - Parameter isEmpty: Whether transitioning to empty state
    private func announceEmptyStateChange(isEmpty: Bool) {
        let announcement: String

        if isEmpty {
            switch selectedTab {
            case .recents:
                announcement = "No recent transcriptions found"
            case .favorites:
                announcement = "No favorite transcriptions found"
            case .saved:
                announcement = "No saved transcriptions found"
            }
        } else {
            announcement = "Transcriptions loaded for \(selectedTab.displayName.lowercased()) tab"
        }

        // Post accessibility announcement
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            UIAccessibility.post(notification: .announcement, argument: announcement)
        }
    }
    
    /// Core filtering logic for sessions based on tab type
    /// - Parameters:
    ///   - tab: The history tab to filter for
    ///   - sessions: The sessions to filter from
    /// - Returns: Filtered and sorted sessions
    private func filterSessions(for tab: HistoryTab, from sessions: [HistorySession]) -> [HistorySession] {
        let filtered: [HistorySession]
        
        switch tab {
        case .recents:
            // Show all sessions for recents
            filtered = sessions
            
        case .favorites:
            // Show only favorited sessions
            filtered = sessions.filter { $0.isFavourite }

        case .saved:
            // Show only saved sessions
            filtered = sessions.filter { $0.isSaved }
        }
        
        // Sort by most recent first
        return filtered.sorted { session1, session2 in
            guard let date1 = session1.createdAt,
                  let date2 = session2.createdAt else {
                return false
            }
            return date1 > date2
        }
    }
    
    // MARK: - Override Methods
    
    /// Override to handle favorite toggle with filtering update
    override func toggleFavorite(for session: HistorySession) {
        super.toggleFavorite(for: session)
        
        // Refresh filtered sessions after favorite status change
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.refreshFilteredSessions()
        }
    }
    
    /// Override to handle save toggle with filtering update
    override func toggleSaved(for session: HistorySession) {
        super.toggleSaved(for: session)

        // Refresh filtered sessions after save status change
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.refreshFilteredSessions()
        }
    }
    
    /// Override to handle deletion with filtering update
    override func deleteSession(_ session: HistorySession) {
        super.deleteSession(session)
        
        // Refresh filtered sessions after deletion
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.refreshFilteredSessions()
        }
    }
    
    // MARK: - Cleanup
    
    deinit {
        filteringCancellable?.cancel()
    }
}

// MARK: - Tab Statistics

extension FloatingHistoryViewModel {
    
    /// Returns the count of sessions for each tab
    var tabCounts: [HistoryTab: Int] {
        return [
            .recents: allSessions.count,
            .favorites: allSessions.filter { $0.isFavourite }.count,
            .saved: allSessions.filter { $0.isSaved }.count
        ]
    }
    
    /// Returns the count for the currently selected tab
    var currentTabCount: Int {
        return tabCounts[selectedTab] ?? 0
    }
    
    /// Returns whether the current tab has any sessions
    var hasSessionsForCurrentTab: Bool {
        return currentTabCount > 0
    }
}
