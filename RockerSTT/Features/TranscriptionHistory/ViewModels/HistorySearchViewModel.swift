//
//  HistorySearchViewModel.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import SwiftUI
import Combine
import CoreData

// MARK: - Search Filter Enum

enum SearchFilter: String, CaseIterable, Hashable {
    case recent = "recent"
    case favorites = "favorites"
    case saved = "saved"
    case translated = "translated"
    case longSessions = "longSessions"
    
    var displayName: String {
        switch self {
        case .recent:
            return "Recent"
        case .favorites:
            return "Favorites"
        case .saved:
            return "Saved"
        case .translated:
            return "Translated"
        case .longSessions:
            return "Long Sessions"
        }
    }
    
    var iconName: String {
        switch self {
        case .recent:
            return "clock"
        case .favorites:
            return "heart"
        case .saved:
            return "bookmark"
        case .translated:
            return "globe"
        case .longSessions:
            return "timer"
        }
    }
}

/// ViewModel for managing search functionality in transcription history
class HistorySearchViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// Current search query
    @Published var searchQuery: String = ""
    
    /// Search results
    @Published var searchResults: [HistorySession] = []
    
    /// Loading state for search operations
    @Published var isSearching: Bool = false
    
    /// Whether search is active (has query)
    @Published var hasActiveSearch: Bool = false
    
    /// Recent search queries
    @Published var recentSearches: [String] = []
    
    /// Search suggestions based on content
    @Published var searchSuggestions: [String] = []
    
    /// Error message for search operations
    @Published var searchError: String?
    
    /// Active search filters
    @Published var activeFilters: Set<SearchFilter> = []
    
    // MARK: - Private Properties
    
    private let historyStorageService: HistoryStorageService
    private var cancellables = Set<AnyCancellable>()
    private let maxRecentSearches = 10
    private let maxSuggestions = 5
    
    // MARK: - Initialization
    
    init(historyStorageService: HistoryStorageService = HistoryStorageService()) {
        self.historyStorageService = historyStorageService
        setupBindings()
        loadRecentSearches()
    }
    
    // MARK: - Setup
    
    private func setupBindings() {
        // Debounce search query changes
        $searchQuery
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] query in
                self?.performSearch(query: query)
            }
            .store(in: &cancellables)
        
        // Update active search state
        $searchQuery
            .map { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            .assign(to: \.hasActiveSearch, on: self)
            .store(in: &cancellables)
    }
    
    // MARK: - Search Operations
    
    /// Performs search with the given query
    private func performSearch(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedQuery.isEmpty else {
            searchResults = []
            isSearching = false
            return
        }
        
        isSearching = true
        searchError = nil
        
        // Perform search using Core Data
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            do {
                let results = try self.searchSessions(query: trimmedQuery)
                
                DispatchQueue.main.async {
                    self.searchResults = results
                    self.isSearching = false
                    
                    // Add to recent searches if we got results
                    if !results.isEmpty {
                        self.addToRecentSearches(trimmedQuery)
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.searchError = "Search failed: \(error.localizedDescription)"
                    self.isSearching = false
                    self.searchResults = []
                }
            }
        }
    }
    
    /// Searches sessions using Core Data predicates with filters
    private func searchSessions(query: String) throws -> [HistorySession] {
        let context = historyStorageService.mainContext
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        
        // Create search predicate for title and content
        let titlePredicate = NSPredicate(format: "title CONTAINS[cd] %@", query)
        let contentPredicate = NSPredicate(format: "contentPreview CONTAINS[cd] %@", query)
        
        // Combine title and content predicates with OR
        var searchPredicate = NSCompoundPredicate(orPredicateWithSubpredicates: [
            titlePredicate,
            contentPredicate
        ])
        
        // Apply filters if any are active
        if !activeFilters.isEmpty {
            var filterPredicates: [NSPredicate] = []
            
            for filter in activeFilters {
                switch filter {
                case .recent:
                    // Sessions from last 7 days
                    let sevenDaysAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
                    filterPredicates.append(NSPredicate(format: "updatedAt >= %@", sevenDaysAgo as NSDate))
                    
                case .favorites:
                    filterPredicates.append(NSPredicate(format: "isFavourite == YES"))
                    
                case .saved:
                    filterPredicates.append(NSPredicate(format: "isSaved == YES"))
                    
                case .translated:
                    // Sessions that have translated content
                    filterPredicates.append(NSPredicate(format: "ANY entries.translatedText != nil AND ANY entries.translatedText != ''"))
                    
                case .longSessions:
                    // Sessions with more than 10 entries or duration > 5 minutes
                    let longSessionPredicate1 = NSPredicate(format: "entryCount > 10")
                    let longSessionPredicate2 = NSPredicate(format: "duration > 300") // 5 minutes in seconds
                    filterPredicates.append(NSCompoundPredicate(orPredicateWithSubpredicates: [longSessionPredicate1, longSessionPredicate2]))
                }
            }
            
            // Combine filter predicates with OR (any filter can match)
            let combinedFilterPredicate = NSCompoundPredicate(orPredicateWithSubpredicates: filterPredicates)
            
            // Combine search predicate with filter predicate using AND
            searchPredicate = NSCompoundPredicate(andPredicateWithSubpredicates: [
                searchPredicate,
                combinedFilterPredicate
            ])
        }
        
        request.predicate = searchPredicate
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
        ]
        request.fetchLimit = 50 // Limit results for performance
        
        return try context.fetch(request)
    }
    
    /// Searches within session entries for detailed results
    func searchInSession(_ session: HistorySession, query: String) -> [HistoryEntry] {
        let entries = session.sortedEntries
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedQuery.isEmpty else { return entries }
        
        return entries.filter { entry in
            let originalTextMatch = entry.originalText?.localizedCaseInsensitiveContains(trimmedQuery) ?? false
            let translatedTextMatch = entry.translatedText?.localizedCaseInsensitiveContains(trimmedQuery) ?? false
            return originalTextMatch || translatedTextMatch
        }
    }
    
    // MARK: - Search Suggestions
    
    /// Generates search suggestions based on existing content
    func generateSuggestions() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }
            
            let sessions = self.historyStorageService.fetchSessions(limit: 100)
            var suggestions = Set<String>()
            
            // Extract common words from titles and content
            for session in sessions {
                if let title = session.title {
                    let words = self.extractMeaningfulWords(from: title)
                    suggestions.formUnion(words)
                }
                
                if let content = session.contentPreview {
                    let words = self.extractMeaningfulWords(from: content)
                    suggestions.formUnion(words)
                }
                
                if suggestions.count >= self.maxSuggestions * 3 {
                    break // Enough data to work with
                }
            }
            
            // Sort by length and take the most meaningful ones
            let sortedSuggestions = Array(suggestions)
                .filter { $0.count >= 3 } // Minimum 3 characters
                .sorted { $0.count > $1.count }
                .prefix(self.maxSuggestions)
            
            DispatchQueue.main.async {
                self.searchSuggestions = Array(sortedSuggestions)
            }
        }
    }
    
    /// Extracts meaningful words from text (excluding common stop words)
    private func extractMeaningfulWords(from text: String) -> Set<String> {
        let stopWords = Set(["the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "a", "an", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "this", "that", "these", "those"])
        
        let words = text.components(separatedBy: .whitespacesAndNewlines)
            .map { $0.trimmingCharacters(in: .punctuationCharacters).lowercased() }
            .filter { word in
                word.count >= 3 && !stopWords.contains(word) && word.allSatisfy { $0.isLetter }
            }
        
        return Set(words)
    }
    
    // MARK: - Recent Searches
    
    /// Adds a search query to recent searches
    private func addToRecentSearches(_ query: String) {
        // Remove if already exists
        recentSearches.removeAll { $0.lowercased() == query.lowercased() }
        
        // Add to beginning
        recentSearches.insert(query, at: 0)
        
        // Limit to max count
        if recentSearches.count > maxRecentSearches {
            recentSearches = Array(recentSearches.prefix(maxRecentSearches))
        }
        
        saveRecentSearches()
    }
    
    /// Loads recent searches from UserDefaults
    private func loadRecentSearches() {
        recentSearches = UserDefaults.standard.stringArray(forKey: "RecentHistorySearches") ?? []
    }
    
    /// Saves recent searches to UserDefaults
    private func saveRecentSearches() {
        UserDefaults.standard.set(recentSearches, forKey: "RecentHistorySearches")
    }
    
    /// Clears all recent searches
    func clearRecentSearches() {
        recentSearches = []
        saveRecentSearches()
    }
    
    /// Uses a recent search query
    func useRecentSearch(_ query: String) {
        searchQuery = query
    }
    
    // MARK: - Search Management
    
    /// Clears current search
    func clearSearch() {
        searchQuery = ""
        searchResults = []
        searchError = nil
    }
    
    /// Clears search error
    func clearError() {
        searchError = nil
    }
    
    // MARK: - Filter Management
    
    /// Toggles a search filter on/off
    func toggleFilter(_ filter: SearchFilter) {
        if activeFilters.contains(filter) {
            activeFilters.remove(filter)
        } else {
            activeFilters.insert(filter)
        }
        
        // Re-perform search with new filters if we have an active search
        if hasActiveSearch {
            performSearch(query: searchQuery)
        }
    }
    
    /// Clears all active filters
    func clearFilters() {
        activeFilters.removeAll()
        
        // Re-perform search without filters if we have an active search
        if hasActiveSearch {
            performSearch(query: searchQuery)
        }
    }
}

// MARK: - Search Result Highlighting

extension HistorySearchViewModel {
    
    /// Returns highlighted text for search results using brand colors
    func highlightedText(for text: String, query: String) -> AttributedString {
        guard !query.isEmpty else {
            return AttributedString(text)
        }
        
        var attributedString = AttributedString(text)
        let searchTerms = query.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        for term in searchTerms {
            let ranges = text.ranges(of: term, options: .caseInsensitive)
            
            for range in ranges {
                if let attributedRange = Range(range, in: attributedString) {
                    // Use brand Amber color for highlighting
                    attributedString[attributedRange].backgroundColor = DesignSystem.brandColors.amber.opacity(0.3)
                    attributedString[attributedRange].foregroundColor = DesignSystem.brandColors.persianPurple
                    attributedString[attributedRange].font = .system(.body, weight: .semibold)
                }
            }
        }
        
        return attributedString
    }
}

// MARK: - String Extension for Search Highlighting

extension String {
    func ranges(of searchString: String, options: String.CompareOptions = []) -> [Range<String.Index>] {
        var ranges: [Range<String.Index>] = []
        var searchStartIndex = self.startIndex
        
        while searchStartIndex < self.endIndex,
              let range = self.range(of: searchString, options: options, range: searchStartIndex..<self.endIndex) {
            ranges.append(range)
            searchStartIndex = range.upperBound
        }
        
        return ranges
    }
}
