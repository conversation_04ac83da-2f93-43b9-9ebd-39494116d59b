//
//  HistoryTaggingService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import CoreData

/// Service for managing tags and categories for transcription sessions
class HistoryTaggingService: ObservableObject {
    
    // MARK: - Properties
    
    private let storageService: HistoryStorageService
    @Published var availableTags: [String] = []
    @Published var availableCategories: [String] = []

    // MARK: - Predefined Categories

    static let defaultCategories = [
        "Meeting",
        "Interview",
        "Lecture",
        "Personal Note",
        "Phone Call",
        "Presentation",
        "Research",
        "Other"
    ]

    // MARK: - Initialization

    init(storageService: HistoryStorageService = HistoryStorageService()) {
        self.storageService = storageService
        loadAvailableTags()
        loadAvailableCategories()
    }
    
    // MARK: - Tag Management
    
    /// Add a tag to a session
    func addTag(_ tag: String, to session: HistorySession) {
        guard !tag.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        
        // Get current tags or create empty set
        var currentTags = Set(session.tags?.components(separatedBy: ",") ?? [])
        currentTags.insert(trimmedTag)
        
        // Update session tags
        session.tags = Array(currentTags).joined(separator: ",")
        session.updatedAt = Date()
        
        // Save context
        saveContext()
        
        // Update available tags
        loadAvailableTags()
    }
    
    /// Remove a tag from a session
    func removeTag(_ tag: String, from session: HistorySession) {
        guard let currentTagsString = session.tags else { return }
        
        var currentTags = Set(currentTagsString.components(separatedBy: ","))
        currentTags.remove(tag.lowercased())
        
        session.tags = currentTags.isEmpty ? nil : Array(currentTags).joined(separator: ",")
        session.updatedAt = Date()
        
        saveContext()
        loadAvailableTags()
    }
    
    /// Get tags for a session
    func getTags(for session: HistorySession) -> [String] {
        guard let tagsString = session.tags else { return [] }
        return tagsString.components(separatedBy: ",").filter { !$0.isEmpty }
    }
    
    /// Set category for a session
    func setCategory(_ category: String, for session: HistorySession) {
        session.category = category.isEmpty ? nil : category
        session.updatedAt = Date()

        saveContext()
        loadAvailableCategories()
    }

    /// Get category for a session
    func getCategory(for session: HistorySession) -> String? {
        return session.category
    }
    
    // MARK: - Bulk Operations
    
    /// Add tag to multiple sessions
    func addTag(_ tag: String, to sessions: [HistorySession]) {
        for session in sessions {
            addTag(tag, to: session)
        }
    }
    
    /// Set category for multiple sessions
    func setCategory(_ category: String, for sessions: [HistorySession]) {
        for session in sessions {
            setCategory(category, for: session)
        }
    }
    
    // MARK: - Search and Filter
    
    /// Get sessions with specific tag
    func getSessions(withTag tag: String) -> [HistorySession] {
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "tags CONTAINS[cd] %@", tag)
        request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
        
        do {
            return try storageService.mainContext.fetch(request)
        } catch {
            print("Error fetching sessions with tag: \(error)")
            return []
        }
    }
    
    /// Get sessions with specific category
    func getSessions(withCategory category: String) -> [HistorySession] {
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "category ==[cd] %@", category)
        request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
        
        do {
            return try storageService.mainContext.fetch(request)
        } catch {
            print("Error fetching sessions with category: \(error)")
            return []
        }
    }
    
    /// Get sessions with any of the specified tags
    func getSessions(withAnyTags tags: [String]) -> [HistorySession] {
        guard !tags.isEmpty else { return [] }
        
        let predicates = tags.map { NSPredicate(format: "tags CONTAINS[cd] %@", $0) }
        let compoundPredicate = NSCompoundPredicate(orPredicateWithSubpredicates: predicates)
        
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = compoundPredicate
        request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
        
        do {
            return try storageService.mainContext.fetch(request)
        } catch {
            print("Error fetching sessions with tags: \(error)")
            return []
        }
    }
    
    // MARK: - Statistics
    
    /// Get tag usage statistics
    func getTagStatistics() -> [String: Int] {
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "tags != nil")
        
        do {
            let sessions = try storageService.mainContext.fetch(request)
            var tagCounts: [String: Int] = [:]

            for session in sessions {
                let tags = getTags(for: session)
                for tag in tags {
                    tagCounts[tag, default: 0] += 1
                }
            }

            return tagCounts
        } catch {
            print("Error fetching tag statistics: \(error)")
            return [:]
        }
    }
    
    /// Get category statistics
    func getCategoryStatistics() -> [String: Int] {
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "category != nil")
        
        do {
            let sessions = try storageService.mainContext.fetch(request)
            var categoryCounts: [String: Int] = [:]

            for session in sessions {
                if let category = session.category {
                    categoryCounts[category, default: 0] += 1
                }
            }

            return categoryCounts
        } catch {
            print("Error fetching category statistics: \(error)")
            return [:]
        }
    }
    
    // MARK: - Auto-tagging
    
    /// Suggest tags based on content analysis
    func suggestTags(for session: HistorySession) -> [String] {
        guard let content = session.fullTranscriptionText?.lowercased() else { return [] }
        
        var suggestions: [String] = []
        
        // Meeting-related keywords
        if content.contains("meeting") || content.contains("agenda") || content.contains("action item") {
            suggestions.append("meeting")
        }
        
        // Interview-related keywords
        if content.contains("interview") || content.contains("candidate") || content.contains("position") {
            suggestions.append("interview")
        }
        
        // Lecture/Education keywords
        if content.contains("lecture") || content.contains("professor") || content.contains("student") {
            suggestions.append("lecture")
        }
        
        // Phone call keywords
        if content.contains("call") || content.contains("phone") || content.contains("hello") {
            suggestions.append("phone-call")
        }
        
        // Research keywords
        if content.contains("research") || content.contains("study") || content.contains("analysis") {
            suggestions.append("research")
        }
        
        return suggestions
    }
    
    /// Auto-categorize based on content
    func suggestCategory(for session: HistorySession) -> String? {
        let suggestions = suggestTags(for: session)
        
        if suggestions.contains("meeting") {
            return "Meeting"
        } else if suggestions.contains("interview") {
            return "Interview"
        } else if suggestions.contains("lecture") {
            return "Lecture"
        } else if suggestions.contains("phone-call") {
            return "Phone Call"
        } else if suggestions.contains("research") {
            return "Research"
        }
        
        return nil
    }
    
    // MARK: - Private Methods
    
    private func loadAvailableTags() {
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "tags != nil")
        
        do {
            let sessions = try storageService.mainContext.fetch(request)
            var allTags = Set<String>()

            for session in sessions {
                let tags = getTags(for: session)
                allTags.formUnion(tags)
            }

            DispatchQueue.main.async {
                self.availableTags = Array(allTags).sorted()
            }
        } catch {
            print("Error loading available tags: \(error)")
        }
    }
    
    private func loadAvailableCategories() {
        let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
        request.predicate = NSPredicate(format: "category != nil")
        
        do {
            let sessions = try storageService.mainContext.fetch(request)
            var usedCategories = Set<String>()

            for session in sessions {
                if let category = session.category {
                    usedCategories.insert(category)
                }
            }

            // Combine default categories with used categories
            let allCategories = Set(Self.defaultCategories).union(usedCategories)

            DispatchQueue.main.async {
                self.availableCategories = Array(allCategories).sorted()
            }
        } catch {
            print("Error loading available categories: \(error)")
        }
    }
    
    private func saveContext() {
        do {
            try storageService.mainContext.save()
        } catch {
            print("Error saving context: \(error)")
        }
    }
}

// MARK: - Tag Filtering Options

enum TagFilterMode {
    case any      // Sessions with any of the selected tags
    case all      // Sessions with all of the selected tags
    case exact    // Sessions with exactly the selected tags
}

// MARK: - Category Filter

struct CategoryFilter {
    let categories: [String]
    let includeUncategorized: Bool
    
    static let all = CategoryFilter(categories: [], includeUncategorized: true)
}
