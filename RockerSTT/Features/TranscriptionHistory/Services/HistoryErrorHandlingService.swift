//
//  HistoryErrorHandlingService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import SwiftUI
import AlertToast
import CoreData

/// Service for handling errors and displaying appropriate user feedback
class HistoryErrorHandlingService: ObservableObject {
    
    // MARK: - Properties
    
    @Published var currentError: HistoryError?
    @Published var showingErrorAlert = false
    @Published var showingErrorToast = false
    @Published var isRetrying = false
    
    // MARK: - Error Handling
    
    /// Handle an error with appropriate user feedback
    func handleError(_ error: Error, context: ErrorContext = .general) {
        let historyError = mapToHistoryError(error, context: context)
        
        DispatchQueue.main.async {
            self.currentError = historyError
            
            // Determine how to show the error based on severity
            switch historyError.severity {
            case .low:
                self.showErrorToast(historyError)
            case .medium:
                self.showErrorAlert(historyError)
            case .high:
                self.showErrorAlert(historyError)
            }
        }
        
        // Log error for debugging
        logError(historyError, originalError: error, context: context)
    }
    
    /// Show error as a toast notification
    private func showErrorToast(_ error: HistoryError) {
        showingErrorToast = true
        
        // Auto-hide toast after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.showingErrorToast = false
        }
    }
    
    /// Show error as an alert dialog
    private func showErrorAlert(_ error: HistoryError) {
        showingErrorAlert = true
    }
    
    /// Retry the last failed operation
    func retry(operation: @escaping () async throws -> Void) {
        guard !isRetrying else { return }
        
        isRetrying = true
        
        Task {
            do {
                try await operation()
                
                DispatchQueue.main.async {
                    self.isRetrying = false
                    self.currentError = nil
                    self.showingErrorAlert = false
                    self.showingErrorToast = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.isRetrying = false
                    self.handleError(error)
                }
            }
        }
    }
    
    /// Clear current error state
    func clearError() {
        currentError = nil
        showingErrorAlert = false
        showingErrorToast = false
    }
    
    // MARK: - Error Mapping
    
    private func mapToHistoryError(_ error: Error, context: ErrorContext) -> HistoryError {
        // Check if it's already a HistoryError
        if let historyError = error as? HistoryError {
            return historyError
        }
        
        // Map common system errors
        if let nsError = error as NSError? {
            switch nsError.domain {
            case NSURLErrorDomain:
                return mapNetworkError(nsError)
            case NSCocoaErrorDomain:
                return mapCoreDataError(nsError, context: context)
            default:
                return .unknown(nsError.localizedDescription)
            }
        }
        
        // Default mapping
        return .unknown(error.localizedDescription)
    }
    
    private func mapNetworkError(_ error: NSError) -> HistoryError {
        switch error.code {
        case NSURLErrorNotConnectedToInternet,
             NSURLErrorNetworkConnectionLost:
            return .networkUnavailable
        case NSURLErrorTimedOut:
            return .networkTimeout
        case NSURLErrorCannotFindHost,
             NSURLErrorCannotConnectToHost:
            return .serverUnavailable
        default:
            return .networkUnavailable
        }
    }
    
    private func mapCoreDataError(_ error: NSError, context: ErrorContext) -> HistoryError {
        switch error.code {
        case NSPersistentStoreIncompatibleVersionHashError,
             NSMigrationMissingSourceModelError:
            return .dataCorruption
        case NSValidationMissingMandatoryPropertyError,
             NSValidationRelationshipLacksMinimumCountError:
            return .dataValidationFailed
        case NSPersistentStoreCoordinatorLockingError:
            return .storageFailure("Storage operation failed")
        default:
            return .storageFailure("Unknown error occurred")
        }
    }
    
    // MARK: - Logging
    
    private func logError(_ historyError: HistoryError, originalError: Error, context: ErrorContext) {
        let errorInfo = [
            "HistoryError": String(describing: historyError),
            "OriginalError": originalError.localizedDescription,
            "Context": String(describing: context),
            "Timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        
        print("🚨 HistoryError: \(errorInfo)")
        
        // In a production app, you might want to send this to a crash reporting service
        // CrashReporter.log(error: historyError, context: errorInfo)
    }
}

// MARK: - Error Context

enum ErrorContext {
    case general
    case loading
    case saving
    case deleting
    case searching
    case exporting
    case importing
    case tagging
    case favoriting
    case sharing
}

// MARK: - Enhanced History Error

extension HistoryError {
    
    var severity: ErrorSeverity {
        switch self {
        case .networkTimeout, .serverUnavailable:
            return .low
        case .networkUnavailable, .storageFailure:
            return .medium
        case .permissionDenied, .dataCorruption, .dataValidationFailed:
            return .high
        case .unknown, .exportFailure:
            return .medium
        }
    }
    
    var isRecoverable: Bool {
        switch self {
        case .networkTimeout, .networkUnavailable, .serverUnavailable, .storageFailure:
            return true
        case .permissionDenied, .dataCorruption, .dataValidationFailed:
            return false
        case .unknown, .exportFailure:
            return true
        }
    }
    
    var userAction: String? {
        switch self {
        case .networkTimeout, .networkUnavailable, .serverUnavailable:
            return "Check your internet connection and try again"
        case .storageFailure:
            return "Try restarting the app"
        case .permissionDenied:
            return "Grant necessary permissions in Settings"
        case .dataCorruption:
            return "Contact support if this continues"
        case .dataValidationFailed:
            return "Check your input and try again"
        case .unknown:
            return "Try again or contact support"
        case .exportFailure:
            return "Check file permissions and try again"
        }
    }
}

// MARK: - Additional Error Types

extension HistoryError {
    static var networkTimeout: HistoryError { .unknown("Network request timed out") }
    static var serverUnavailable: HistoryError { .unknown("Server is temporarily unavailable") }
    static var dataValidationFailed: HistoryError { .unknown("Data validation failed") }
}

// MARK: - Error Severity

enum ErrorSeverity {
    case low    // Toast notification
    case medium // Alert dialog
    case high   // Alert dialog with emphasis
}

// MARK: - Error Recovery Strategies

struct ErrorRecoveryStrategy {
    let canRetry: Bool
    let retryDelay: TimeInterval
    let maxRetries: Int
    let fallbackAction: (() -> Void)?
    
    static let networkRetry = ErrorRecoveryStrategy(
        canRetry: true,
        retryDelay: 2.0,
        maxRetries: 3,
        fallbackAction: nil
    )
    
    static let storageRetry = ErrorRecoveryStrategy(
        canRetry: true,
        retryDelay: 1.0,
        maxRetries: 2,
        fallbackAction: nil
    )
    
    static let noRetry = ErrorRecoveryStrategy(
        canRetry: false,
        retryDelay: 0,
        maxRetries: 0,
        fallbackAction: nil
    )
}

// MARK: - Error Handling Extensions

extension View {
    
    /// Add error handling to a view
    func withErrorHandling(_ errorHandler: HistoryErrorHandlingService) -> some View {
        self
            .alert("Error", isPresented: .constant(errorHandler.showingErrorAlert && errorHandler.currentError != nil)) {
                if let error = errorHandler.currentError {
                    if error.isRecoverable {
                        Button("Try Again") {
                            // Retry action would be handled by the calling view
                        }
                    }
                    
                    Button("OK") {
                        errorHandler.clearError()
                    }
                }
            } message: {
                if let error = errorHandler.currentError {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(error.description)
                        
                        if let userAction = error.userAction {
                            Text(userAction)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .toast(isPresenting: .constant(errorHandler.showingErrorToast && errorHandler.currentError != nil)) {
                if let error = errorHandler.currentError {
                    return AlertToast(
                        displayMode: .hud,
                        type: .error(.red),
                        title: error.title,
                        subTitle: error.description
                    )
                } else {
                    return AlertToast(displayMode: .hud, type: .error(.red), title: "Error")
                }
            }
    }
}

// MARK: - Preview

#Preview {
    struct ErrorHandlingPreview: View {
        @StateObject private var errorHandler = HistoryErrorHandlingService()
        
        var body: some View {
            VStack(spacing: 20) {
                Button("Trigger Network Error") {
                    let error = NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet)
                    errorHandler.handleError(error, context: .loading)
                }
                
                Button("Trigger Storage Error") {
                    let error = NSError(domain: NSCocoaErrorDomain, code: NSPersistentStoreCoordinatorLockingError)
                    errorHandler.handleError(error, context: .saving)
                }
                
                Button("Clear Error") {
                    errorHandler.clearError()
                }
            }
            .withErrorHandling(errorHandler)
        }
    }
    
    return ErrorHandlingPreview()
}
