//
//  HistoryExportService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import UIKit

/// Service for exporting transcription history in various formats
class HistoryExportService: ObservableObject {
    
    // MARK: - Export Formats
    
    enum ExportFormat: String, CaseIterable {
        case plainText = "txt"
        case markdown = "md"
        case json = "json"
        case csv = "csv"
        
        var displayName: String {
            switch self {
            case .plainText: return "Plain Text"
            case .markdown: return "Markdown"
            case .json: return "JSON"
            case .csv: return "CSV"
            }
        }
        
        var mimeType: String {
            switch self {
            case .plainText: return "text/plain"
            case .markdown: return "text/markdown"
            case .json: return "application/json"
            case .csv: return "text/csv"
            }
        }
        
        var fileExtension: String {
            return rawValue
        }
    }
    
    // MARK: - Export Options
    
    struct ExportOptions {
        let includeTranslations: Bool
        let includeTimestamps: Bool
        let includeMetadata: Bool
        let includeEmotions: Bool
        let separateLanguages: Bool
        
        static let `default` = ExportOptions(
            includeTranslations: true,
            includeTimestamps: true,
            includeMetadata: true,
            includeEmotions: false,
            separateLanguages: false
        )
    }
    
    // MARK: - Properties
    
    @Published var isExporting = false
    @Published var exportProgress: Double = 0.0
    @Published var lastExportError: Error?
    
    // MARK: - Public Methods
    
    /// Export a single session to the specified format
    func exportSession(
        _ session: HistorySession,
        format: ExportFormat,
        options: ExportOptions = .default
    ) throws -> Data {
        isExporting = true
        exportProgress = 0.0
        defer {
            isExporting = false
            exportProgress = 0.0
        }
        
        do {
            let data = try generateExportData(for: session, format: format, options: options)
            exportProgress = 1.0
            return data
        } catch {
            lastExportError = error
            throw error
        }
    }
    
    /// Export multiple sessions to the specified format
    func exportSessions(
        _ sessions: [HistorySession],
        format: ExportFormat,
        options: ExportOptions = .default
    ) throws -> Data {
        isExporting = true
        exportProgress = 0.0
        defer {
            isExporting = false
            exportProgress = 0.0
        }
        
        do {
            let data = try generateExportData(for: sessions, format: format, options: options)
            exportProgress = 1.0
            return data
        } catch {
            lastExportError = error
            throw error
        }
    }
    
    /// Generate filename for export
    func generateFilename(
        for sessions: [HistorySession],
        format: ExportFormat
    ) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = dateFormatter.string(from: Date())
        
        if sessions.count == 1, let session = sessions.first {
            let title = session.title?.replacingOccurrences(of: " ", with: "_") ?? "session"
            return "\(title)_\(timestamp).\(format.fileExtension)"
        } else {
            return "transcription_history_\(timestamp).\(format.fileExtension)"
        }
    }
    
    // MARK: - Private Methods
    
    private func generateExportData(
        for session: HistorySession,
        format: ExportFormat,
        options: ExportOptions
    ) throws -> Data {
        return try generateExportData(for: [session], format: format, options: options)
    }
    
    private func generateExportData(
        for sessions: [HistorySession],
        format: ExportFormat,
        options: ExportOptions
    ) throws -> Data {
        let content: String
        
        switch format {
        case .plainText:
            content = generatePlainTextContent(for: sessions, options: options)
        case .markdown:
            content = generateMarkdownContent(for: sessions, options: options)
        case .json:
            content = try generateJSONContent(for: sessions, options: options)
        case .csv:
            content = generateCSVContent(for: sessions, options: options)
        }
        
        guard let data = content.data(using: .utf8) else {
            throw ExportError.encodingFailed
        }
        
        return data
    }
    
    private func generatePlainTextContent(
        for sessions: [HistorySession],
        options: ExportOptions
    ) -> String {
        var content = ""
        
        for (sessionIndex, session) in sessions.enumerated() {
            if sessionIndex > 0 {
                content += "\n\n" + String(repeating: "=", count: 50) + "\n\n"
            }
            
            // Session header
            content += "Session: \(session.displayTitle)\n"
            if options.includeMetadata {
                content += "Date: \(session.formattedDate)\n"
                if let duration = session.formattedDuration {
                    content += "Duration: \(duration)\n"
                }
                content += "Entries: \(session.entryCount)\n"
                content += "Word Count: \(session.wordCount)\n"
            }
            content += "\n"
            
            // Entries
            for entry in session.sortedEntries {
                if options.includeTimestamps, let timestamp = entry.timestamp {
                    let formatter = DateFormatter()
                    formatter.timeStyle = .medium
                    content += "[\(formatter.string(from: timestamp))] "
                }
                
                if let originalText = entry.originalText {
                    content += originalText
                    
                    if options.includeTranslations,
                       let translatedText = entry.translatedText,
                       !translatedText.isEmpty {
                        content += "\n  → \(translatedText)"
                    }
                }
                
                if options.includeEmotions,
                   let emotion = entry.emotion,
                   !emotion.isEmpty {
                    content += " [\(emotion)]"
                }
                
                content += "\n"
            }
        }
        
        return content
    }
    
    private func generateMarkdownContent(
        for sessions: [HistorySession],
        options: ExportOptions
    ) -> String {
        var content = "# Transcription History Export\n\n"
        
        for session in sessions {
            content += "## \(session.displayTitle)\n\n"
            
            if options.includeMetadata {
                content += "**Date:** \(session.formattedDate)  \n"
                if let duration = session.formattedDuration {
                    content += "**Duration:** \(duration)  \n"
                }
                content += "**Entries:** \(session.entryCount)  \n"
                content += "**Word Count:** \(session.wordCount)  \n\n"
            }
            
            for entry in session.sortedEntries {
                var entryContent = ""
                
                if options.includeTimestamps, let timestamp = entry.timestamp {
                    let formatter = DateFormatter()
                    formatter.timeStyle = .medium
                    entryContent += "`\(formatter.string(from: timestamp))` "
                }
                
                if let originalText = entry.originalText {
                    entryContent += originalText
                    
                    if options.includeTranslations,
                       let translatedText = entry.translatedText,
                       !translatedText.isEmpty {
                        entryContent += "\n\n> **Translation:** \(translatedText)"
                    }
                }
                
                if options.includeEmotions,
                   let emotion = entry.emotion,
                   !emotion.isEmpty {
                    entryContent += " *[\(emotion)]*"
                }
                
                content += entryContent + "\n\n"
            }
            
            content += "---\n\n"
        }
        
        return content
    }
    
    private func generateJSONContent(
        for sessions: [HistorySession],
        options: ExportOptions
    ) throws -> String {
        var exportData: [String: Any] = [
            "export_date": ISO8601DateFormatter().string(from: Date()),
            "export_options": [
                "include_translations": options.includeTranslations,
                "include_timestamps": options.includeTimestamps,
                "include_metadata": options.includeMetadata,
                "include_emotions": options.includeEmotions,
                "separate_languages": options.separateLanguages
            ],
            "sessions": []
        ]
        
        var sessionsData: [[String: Any]] = []
        
        for session in sessions {
            var sessionData: [String: Any] = [
                "id": session.id?.uuidString ?? "",
                "title": session.title ?? "",
                "created_at": ISO8601DateFormatter().string(from: session.createdAt ?? Date()),
                "entries": []
            ]
            
            if options.includeMetadata {
                sessionData["duration"] = session.duration
                sessionData["entry_count"] = session.entryCount
                sessionData["word_count"] = session.wordCount
                sessionData["is_favourite"] = session.isFavourite
                sessionData["is_saved"] = session.isSaved
                sessionData["has_translations"] = session.hasTranslations
            }
            
            var entriesData: [[String: Any]] = []
            
            for entry in session.sortedEntries {
                var entryData: [String: Any] = [:]
                
                if let originalText = entry.originalText {
                    entryData["original_text"] = originalText
                }
                
                if options.includeTranslations,
                   let translatedText = entry.translatedText {
                    entryData["translated_text"] = translatedText
                    entryData["target_language"] = entry.targetLanguage
                }
                
                if options.includeTimestamps,
                   let timestamp = entry.timestamp {
                    entryData["timestamp"] = ISO8601DateFormatter().string(from: timestamp)
                }
                
                if let detectedLanguage = entry.detectedLanguage {
                    entryData["detected_language"] = detectedLanguage
                }
                
                if options.includeEmotions,
                   let emotion = entry.emotion {
                    entryData["emotion"] = emotion
                }
                
                entryData["is_final"] = entry.isFinal
                
                entriesData.append(entryData)
            }
            
            sessionData["entries"] = entriesData
            sessionsData.append(sessionData)
        }
        
        exportData["sessions"] = sessionsData
        
        let jsonData = try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)
        return String(data: jsonData, encoding: .utf8) ?? ""
    }
    
    private func generateCSVContent(
        for sessions: [HistorySession],
        options: ExportOptions
    ) -> String {
        var content = ""
        
        // CSV Header
        var headers = ["Session Title", "Session Date", "Entry Timestamp", "Original Text"]
        
        if options.includeTranslations {
            headers.append(contentsOf: ["Translated Text", "Target Language"])
        }
        
        headers.append("Detected Language")
        
        if options.includeEmotions {
            headers.append("Emotion")
        }
        
        headers.append("Is Final")
        
        content += headers.map { escapeCSVField($0) }.joined(separator: ",") + "\n"
        
        // CSV Data
        for session in sessions {
            for entry in session.sortedEntries {
                var row: [String] = []
                
                row.append(escapeCSVField(session.displayTitle))
                row.append(escapeCSVField(session.formattedDate))
                
                if options.includeTimestamps, let timestamp = entry.timestamp {
                    let formatter = DateFormatter()
                    formatter.timeStyle = .medium
                    row.append(escapeCSVField(formatter.string(from: timestamp)))
                } else {
                    row.append("")
                }
                
                row.append(escapeCSVField(entry.originalText ?? ""))
                
                if options.includeTranslations {
                    row.append(escapeCSVField(entry.translatedText ?? ""))
                    row.append(escapeCSVField(entry.targetLanguage ?? ""))
                }
                
                row.append(escapeCSVField(entry.detectedLanguage ?? ""))
                
                if options.includeEmotions {
                    row.append(escapeCSVField(entry.emotion ?? ""))
                }
                
                row.append(entry.isFinal ? "true" : "false")
                
                content += row.joined(separator: ",") + "\n"
            }
        }
        
        return content
    }
    
    private func escapeCSVField(_ field: String) -> String {
        let escaped = field.replacingOccurrences(of: "\"", with: "\"\"")
        if escaped.contains(",") || escaped.contains("\"") || escaped.contains("\n") {
            return "\"\(escaped)\""
        }
        return escaped
    }
}

// MARK: - Export Error

enum ExportError: LocalizedError {
    case encodingFailed
    case invalidData
    case fileWriteFailed
    
    var errorDescription: String? {
        switch self {
        case .encodingFailed:
            return "Failed to encode export data"
        case .invalidData:
            return "Invalid data for export"
        case .fileWriteFailed:
            return "Failed to write export file"
        }
    }
}
