//
//  FloatingHistoryAnimationManager.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI
import Combine

/// Animation manager specifically designed for floating history interface
/// Handles smooth transitions, staggered animations, and performance optimization
class FloatingHistoryAnimationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isFilteringInProgress: Bool = false
    @Published var currentAnimationPhase: AnimationPhase = .idle
    @Published var staggeredAnimationStates: [String: AnimationState] = [:]
    @Published var optimizationLevel: OptimizationLevel = .balanced
    
    // MARK: - Animation Configuration
    
    struct AnimationConfig {
        // Tab switching animations - Enhanced for better feel
        static let tabSwitchDuration: Double = 0.35
        static let tabSwitchEasing: Animation = .interpolatingSpring(
            mass: 1.0,
            stiffness: 170,
            damping: 26,
            initialVelocity: 0
        )

        // Micro-interaction animations
        static let hoverFeedbackDuration: Double = 0.15
        static let hoverFeedbackEasing: Animation = .interpolatingSpring(
            mass: 0.8,
            stiffness: 200,
            damping: 20,
            initialVelocity: 0
        )

        static let selectionFeedbackDuration: Double = 0.2
        static let selectionFeedbackEasing: Animation = .interpolatingSpring(
            mass: 0.9,
            stiffness: 180,
            damping: 22,
            initialVelocity: 0
        )

        // Grid item animations - Refined timing
        static let itemInsertionDuration: Double = 0.45
        static let itemRemovalDuration: Double = 0.28
        static let itemInsertionEasing: Animation = .interpolatingSpring(
            mass: 1.2,
            stiffness: 150,
            damping: 28,
            initialVelocity: 0
        )
        static let itemRemovalEasing: Animation = .easeOut(duration: itemRemovalDuration)

        // Staggered animation timing - Optimized for smoothness
        static let staggerDelay: Double = 0.04
        static let maxStaggerDelay: Double = 0.25

        // Loading state animations - More polished
        static let loadingPulseDuration: Double = 1.0
        static let loadingPulseEasing: Animation = .easeInOut(duration: loadingPulseDuration).repeatForever(autoreverses: true)

        // Empty state transitions
        static let emptyStateTransitionDuration: Double = 0.5
        static let emptyStateTransitionEasing: Animation = .interpolatingSpring(
            mass: 1.1,
            stiffness: 140,
            damping: 30,
            initialVelocity: 0
        )

        // Performance thresholds
        static let maxConcurrentAnimations: Int = 20
        static let reducedMotionFallbackDuration: Double = 0.15
    }
    
    // MARK: - Animation State Types
    
    enum AnimationPhase {
        case idle
        case filtering
        case tabSwitching
        case loading
    }
    
    struct AnimationState {
        var scale: CGFloat = 1.0
        var opacity: Double = 1.0
        var offset: CGSize = .zero
        var rotation: Angle = .zero
        var isVisible: Bool = true
        var animationDelay: Double = 0.0
    }

    enum OptimizationLevel {
        case performance
        case balanced
        case quality

        var animationSpeedMultiplier: Double {
            switch self {
            case .performance: return 1.5
            case .balanced: return 1.0
            case .quality: return 0.8
            }
        }

        var maxConcurrentAnimations: Int {
            switch self {
            case .performance: return 10
            case .balanced: return 20
            case .quality: return 30
            }
        }

        var staggerDelayMultiplier: Double {
            switch self {
            case .performance: return 0.5
            case .balanced: return 1.0
            case .quality: return 1.2
            }
        }
    }
    
    // MARK: - Private Properties
    
    private var animationCancellables = Set<AnyCancellable>()
    private var activeAnimations: Set<String> = []
    private let hapticManager = HapticFeedbackManager.shared
    private var isReducedMotionEnabled: Bool {
        UIAccessibility.isReduceMotionEnabled
    }
    
    // MARK: - Initialization
    
    init() {
        setupAccessibilityObserver()
    }
    
    // MARK: - Public Animation Methods
    
    /// Animates tab switching with smooth transitions
    /// - Parameters:
    ///   - fromTab: Previous tab
    ///   - toTab: New tab
    ///   - sessionCount: Number of sessions to animate
    func animateTabSwitch(from fromTab: HistoryTab, to toTab: HistoryTab, sessionCount: Int) {
        guard !isReducedMotionEnabled else {
            // Immediate transition for reduced motion
            currentAnimationPhase = .idle
            return
        }
        
        currentAnimationPhase = .tabSwitching
        
        // Haptic feedback for tab switch
        hapticManager.selectionChanged()
        
        // Animate out current content
        withAnimation(AnimationConfig.tabSwitchEasing) {
            // Fade out current items
            for i in 0..<min(sessionCount, AnimationConfig.maxConcurrentAnimations) {
                let key = "tab_switch_out_\(i)"
                staggeredAnimationStates[key] = AnimationState(
                    scale: 0.95,
                    opacity: 0.0,
                    animationDelay: Double(i) * AnimationConfig.staggerDelay
                )
            }
        }
        
        // Animate in new content after a brief delay
        DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConfig.tabSwitchDuration * 0.5) {
            self.animateContentEntrance(sessionCount: sessionCount, animationKey: "tab_switch_in")
        }
        
        // Reset animation phase
        DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConfig.tabSwitchDuration) {
            self.currentAnimationPhase = .idle
            self.clearAnimationStates()
        }
    }
    
    /// Animates content entrance with staggered timing
    /// - Parameters:
    ///   - sessionCount: Number of sessions to animate
    ///   - animationKey: Unique key for this animation sequence
    func animateContentEntrance(sessionCount: Int, animationKey: String = "entrance") {
        guard !isReducedMotionEnabled else { return }
        
        let itemsToAnimate = min(sessionCount, AnimationConfig.maxConcurrentAnimations)
        
        for i in 0..<itemsToAnimate {
            let key = "\(animationKey)_\(i)"
            let delay = min(Double(i) * AnimationConfig.staggerDelay, AnimationConfig.maxStaggerDelay)
            
            // Initial state (hidden)
            staggeredAnimationStates[key] = AnimationState(
                scale: 0.8,
                opacity: 0.0,
                offset: CGSize(width: 0, height: 20),
                animationDelay: delay
            )
            
            // Animate to visible state
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                withAnimation(AnimationConfig.itemInsertionEasing) {
                    self.staggeredAnimationStates[key] = AnimationState(
                        scale: 1.0,
                        opacity: 1.0,
                        offset: .zero,
                        animationDelay: delay
                    )
                }
            }
        }
    }
    
    /// Animates content removal with smooth exit
    /// - Parameters:
    ///   - sessionCount: Number of sessions to animate
    ///   - animationKey: Unique key for this animation sequence
    func animateContentRemoval(sessionCount: Int, animationKey: String = "removal") {
        guard !isReducedMotionEnabled else { return }
        
        let itemsToAnimate = min(sessionCount, AnimationConfig.maxConcurrentAnimations)
        
        for i in 0..<itemsToAnimate {
            let key = "\(animationKey)_\(i)"
            let delay = Double(i) * (AnimationConfig.staggerDelay * 0.5) // Faster removal
            
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                withAnimation(AnimationConfig.itemRemovalEasing) {
                    self.staggeredAnimationStates[key] = AnimationState(
                        scale: 0.9,
                        opacity: 0.0,
                        offset: CGSize(width: 0, height: -10),
                        animationDelay: delay
                    )
                }
            }
        }
    }
    
    /// Starts filtering animation sequence
    /// - Parameter sessionCount: Number of sessions being filtered
    func startFilteringAnimation(sessionCount: Int) {
        isFilteringInProgress = true
        currentAnimationPhase = .filtering
        
        // Light haptic feedback
        hapticManager.buttonPressed()
        
        // Animate out old content
        animateContentRemoval(sessionCount: sessionCount, animationKey: "filter_out")
        
        // Brief pause before showing new content
        DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConfig.itemRemovalDuration) {
            self.animateContentEntrance(sessionCount: sessionCount, animationKey: "filter_in")
            
            // End filtering state
            DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConfig.itemInsertionDuration) {
                self.isFilteringInProgress = false
                self.currentAnimationPhase = .idle
            }
        }
    }
    
    /// Shows loading animation for async operations
    func showLoadingAnimation() {
        guard !isReducedMotionEnabled else { return }
        
        currentAnimationPhase = .loading
        
        withAnimation(AnimationConfig.loadingPulseEasing) {
            staggeredAnimationStates["loading"] = AnimationState(
                scale: 1.05,
                opacity: 0.7
            )
        }
    }
    
    /// Hides loading animation
    func hideLoadingAnimation() {
        currentAnimationPhase = .idle
        
        withAnimation(.easeOut(duration: 0.2)) {
            staggeredAnimationStates["loading"] = AnimationState()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.staggeredAnimationStates.removeValue(forKey: "loading")
        }
    }
    
    /// Gets animation state for a specific item
    /// - Parameter key: Animation key for the item
    /// - Returns: Current animation state
    func animationState(for key: String) -> AnimationState {
        return staggeredAnimationStates[key] ?? AnimationState()
    }
    
    /// Clears all animation states
    func clearAnimationStates() {
        staggeredAnimationStates.removeAll()
        activeAnimations.removeAll()
    }

    // MARK: - Performance Optimization

    /// Sets the optimization level for animations
    /// - Parameter level: New optimization level
    func setOptimizationLevel(_ level: OptimizationLevel) {
        guard level != optimizationLevel else { return }

        optimizationLevel = level

        // Clear existing animation states to apply new optimization
        clearAnimationStates()

        print("🎨 FloatingHistoryAnimationManager: Optimization level set to \(level)")
    }

    /// Gets optimized animation based on current optimization level
    /// - Parameter baseAnimation: Base animation to optimize
    /// - Returns: Optimized animation
    func getOptimizedAnimation(_ baseAnimation: Animation) -> Animation {
        let speedMultiplier = optimizationLevel.animationSpeedMultiplier
        return baseAnimation.speed(speedMultiplier)
    }

    /// Checks if animation should be throttled based on optimization level
    /// - Returns: True if animation should proceed
    func shouldAllowAnimation() -> Bool {
        return activeAnimations.count < optimizationLevel.maxConcurrentAnimations
    }

    // MARK: - Private Methods
    
    private func setupAccessibilityObserver() {
        NotificationCenter.default.publisher(for: UIAccessibility.reduceMotionStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.clearAnimationStates()
            }
            .store(in: &animationCancellables)
    }

    // MARK: - Micro-Interaction Animation Methods

    /// Returns animation for hover feedback
    func getHoverFeedbackAnimation() -> Animation {
        return isReducedMotionEnabled ?
            .linear(duration: AnimationConfig.reducedMotionFallbackDuration) :
            AnimationConfig.hoverFeedbackEasing
    }

    /// Returns animation for selection feedback
    func getSelectionFeedbackAnimation() -> Animation {
        return isReducedMotionEnabled ?
            .linear(duration: AnimationConfig.reducedMotionFallbackDuration) :
            AnimationConfig.selectionFeedbackEasing
    }

    /// Returns animation for empty state transitions
    func getEmptyStateTransitionAnimation() -> Animation {
        return isReducedMotionEnabled ?
            .linear(duration: AnimationConfig.reducedMotionFallbackDuration) :
            AnimationConfig.emptyStateTransitionEasing
    }

    /// Starts loading state animation
    func startLoadingStateAnimation() {
        guard !isReducedMotionEnabled else {
            currentAnimationPhase = .loading
            return
        }

        withAnimation(AnimationConfig.loadingPulseEasing) {
            currentAnimationPhase = .loading
        }
    }

    /// Stops loading state animation
    func stopLoadingStateAnimation() {
        withAnimation(getSelectionFeedbackAnimation()) {
            currentAnimationPhase = .idle
        }
    }

    /// Animates tab button hover state
    /// - Parameter isHovered: Whether the button is being hovered
    func animateTabButtonHover(_ isHovered: Bool) -> Animation {
        return getHoverFeedbackAnimation()
    }

    /// Animates tab button selection
    /// - Parameter isSelected: Whether the button is selected
    func animateTabButtonSelection(_ isSelected: Bool) -> Animation {
        hapticManager.selectionChanged()
        return getSelectionFeedbackAnimation()
    }
}

// MARK: - Animation View Modifiers

extension View {
    /// Applies floating history item animation
    /// - Parameters:
    ///   - animationManager: Animation manager instance
    ///   - animationKey: Unique key for this item
    /// - Returns: Animated view
    func floatingHistoryItemAnimation(
        animationManager: FloatingHistoryAnimationManager,
        animationKey: String
    ) -> some View {
        let state = animationManager.animationState(for: animationKey)
        
        return self
            .scaleEffect(state.scale)
            .opacity(state.opacity)
            .offset(state.offset)
            .rotationEffect(state.rotation)
    }
    
    /// Applies loading animation for floating history
    /// - Parameter animationManager: Animation manager instance
    /// - Returns: Animated view
    func floatingHistoryLoadingAnimation(
        animationManager: FloatingHistoryAnimationManager
    ) -> some View {
        let isLoading = animationManager.currentAnimationPhase == .loading
        let state = animationManager.animationState(for: "loading")
        
        return self
            .scaleEffect(isLoading ? state.scale : 1.0)
            .opacity(isLoading ? state.opacity : 1.0)
    }
}
