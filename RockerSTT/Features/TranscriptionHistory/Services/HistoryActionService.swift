//
//  HistoryActionService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import AVFoundation
import UIKit

/// Service for handling history session actions like copy, delete, and speak
@MainActor
class HistoryActionService: ObservableObject {
    
    // MARK: - Properties
    
    private let historyStorageService: HistoryStorageService
    private let speechSynthesizer = AVSpeechSynthesizer()
    private var speechDelegate: SpeechSynthesizerDelegate?

    @Published var isSpeaking = false
    @Published var currentSpeakingSessionId: UUID?

    // MARK: - Initialization

    init(historyStorageService: HistoryStorageService = HistoryStorageService()) {
        self.historyStorageService = historyStorageService
        setupSpeechSynthesizer()
    }

    // MARK: - Setup

    private func setupSpeechSynthesizer() {
        speechDelegate = SpeechSynthesizerDelegate(actionService: self)
        speechSynthesizer.delegate = speechDelegate
    }
    
    // MARK: - Copy Action
    
    /// Copies the session's transcription text to the clipboard
    func copyToClipboard(session: HistorySession) {
        let textToCopy = session.fullTranscriptionText ?? session.contentPreview ?? ""
        
        guard !textToCopy.isEmpty else {
            print("No text to copy for session: \(session.displayTitle)")
            return
        }
        
        UIPasteboard.general.string = textToCopy
        
        // Provide haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("Copied to clipboard: \(textToCopy.prefix(50))...")
    }
    
    // MARK: - Delete Action
    
    /// Deletes a session from Core Data
    func deleteSession(_ session: HistorySession) throws {
        historyStorageService.deleteSession(session)

        // Provide haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        print("Deleted session: \(session.displayTitle)")
    }
    
    // MARK: - Speak Action
    
    /// Speaks the session's transcription text using AVSpeechSynthesizer
    func speakSession(_ session: HistorySession) {
        let textToSpeak = session.fullTranscriptionText ?? session.contentPreview ?? ""
        
        guard !textToSpeak.isEmpty else {
            print("No text to speak for session: \(session.displayTitle)")
            return
        }
        
        // Stop current speech if speaking
        if isSpeaking {
            stopSpeaking()
        }
        
        // Create speech utterance
        let utterance = AVSpeechUtterance(string: textToSpeak)
        
        // Configure speech parameters
        utterance.rate = AVSpeechUtteranceDefaultSpeechRate
        utterance.pitchMultiplier = 1.0
        utterance.volume = 1.0
        
        // Set language to default (can be enhanced later with language detection)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        
        // Update state
        isSpeaking = true
        currentSpeakingSessionId = session.id
        
        // Start speaking
        speechSynthesizer.speak(utterance)
        
        print("Started speaking session: \(session.displayTitle)")
    }
    
    /// Stops the current speech synthesis
    func stopSpeaking() {
        guard isSpeaking else { return }
        
        speechSynthesizer.stopSpeaking(at: .immediate)
        isSpeaking = false
        currentSpeakingSessionId = nil
        
        print("Stopped speaking")
    }
    
    /// Pauses the current speech synthesis
    func pauseSpeaking() {
        guard isSpeaking else { return }
        speechSynthesizer.pauseSpeaking(at: .immediate)
    }
    
    /// Resumes the paused speech synthesis
    func resumeSpeaking() {
        speechSynthesizer.continueSpeaking()
    }
    
    // MARK: - Speech Delegate Callbacks
    
    fileprivate func speechDidFinish() {
        isSpeaking = false
        currentSpeakingSessionId = nil
    }
    
    fileprivate func speechDidCancel() {
        isSpeaking = false
        currentSpeakingSessionId = nil
    }
}

// MARK: - Speech Synthesizer Delegate

private class SpeechSynthesizerDelegate: NSObject, AVSpeechSynthesizerDelegate {
    weak var actionService: HistoryActionService?
    
    init(actionService: HistoryActionService) {
        self.actionService = actionService
        super.init()
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        Task { @MainActor in
            actionService?.speechDidFinish()
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        Task { @MainActor in
            actionService?.speechDidCancel()
        }
    }
}

// MARK: - HistorySession Extension

extension HistorySession {
    /// Returns the full transcription text by combining all entries
    var fullTranscriptionText: String? {
        let entries = self.sortedEntries

        if entries.isEmpty {
            return contentPreview
        }

        let transcriptionTexts = entries.compactMap { $0.originalText }

        if transcriptionTexts.isEmpty {
            return contentPreview
        }

        return transcriptionTexts.joined(separator: " ")
    }
}
