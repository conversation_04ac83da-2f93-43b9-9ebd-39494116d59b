//
//  FloatingHistoryNavigationService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI
import Combine

/// Navigation service specifically for floating history interface
/// Handles deep linking, search integration, and navigation coordination
@MainActor
class FloatingHistoryNavigationService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentNavigationState: NavigationState = .list
    @Published var selectedSession: HistorySession?
    @Published var isSearchPresented: Bool = false
    @Published var isExportPresented: Bool = false
    @Published var navigationPath = NavigationPath()
    
    // MARK: - Navigation State

    enum NavigationState: Equatable {
        case list
        case sessionDetail(HistorySession)
        case search
        case export

        static func == (lhs: NavigationState, rhs: NavigationState) -> Bool {
            switch (lhs, rhs) {
            case (.list, .list), (.search, .search), (.export, .export):
                return true
            case (.sessionDetail(let session1), .sessionDetail(let session2)):
                return session1.id == session2.id
            default:
                return false
            }
        }
    }
    
    // MARK: - Dependencies
    
    private let navigationCoordinator: NavigationCoordinator
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(navigationCoordinator: NavigationCoordinator) {
        self.navigationCoordinator = navigationCoordinator
        setupNavigationObservers()
    }
    
    // MARK: - Navigation Methods
    
    /// Navigate to session detail
    /// - Parameter session: The session to navigate to
    func navigateToSession(_ session: HistorySession) {
        selectedSession = session
        currentNavigationState = .sessionDetail(session)
        
        // Update navigation coordinator
        navigationCoordinator.navigateToSession(session)
        
        // Add to navigation path for proper back navigation
        navigationPath.append(NavigationDestination.sessionDetail(session))
        
        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()
        
        // Log navigation
        print("FloatingHistory: Navigated to session \(session.title ?? "Untitled")")
    }
    
    /// Present search interface
    func presentSearch() {
        isSearchPresented = true
        currentNavigationState = .search
        
        // Update navigation coordinator
        navigationCoordinator.showingSearch = true
        
        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()
        
        print("FloatingHistory: Presented search")
    }
    
    /// Present export interface
    func presentExport() {
        isExportPresented = true
        currentNavigationState = .export
        
        // Update navigation coordinator
        navigationCoordinator.showingExport = true
        
        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()
        
        print("FloatingHistory: Presented export")
    }
    
    /// Navigate back to list
    func navigateBackToList() {
        selectedSession = nil
        currentNavigationState = .list
        isSearchPresented = false
        isExportPresented = false
        
        // Clear navigation path
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
        
        // Update navigation coordinator
        navigationCoordinator.currentDetailSession = nil
        navigationCoordinator.showingSearch = false
        navigationCoordinator.showingExport = false
        
        print("FloatingHistory: Navigated back to list")
    }
    
    /// Handle deep link navigation
    /// - Parameter url: The deep link URL
    func handleDeepLink(_ url: URL) {
        guard url.scheme == "rockerstt" else { return }
        
        switch url.host {
        case "session":
            handleSessionDeepLink(url)
        case "search":
            handleSearchDeepLink(url)
        default:
            print("FloatingHistory: Unknown deep link: \(url)")
        }
    }
    
    /// Handle back navigation
    func handleBackNavigation() -> Bool {
        switch currentNavigationState {
        case .list:
            return false // Can't go back from list
        case .sessionDetail, .search, .export:
            navigateBackToList()
            return true
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNavigationObservers() {
        // Listen for navigation coordinator changes
        navigationCoordinator.$currentDetailSession
            .sink { [weak self] session in
                if let session = session {
                    self?.selectedSession = session
                    self?.currentNavigationState = .sessionDetail(session)
                }
            }
            .store(in: &cancellables)
        
        navigationCoordinator.$showingSearch
            .sink { [weak self] isShowing in
                self?.isSearchPresented = isShowing
                if isShowing {
                    self?.currentNavigationState = .search
                }
            }
            .store(in: &cancellables)
        
        navigationCoordinator.$showingExport
            .sink { [weak self] isShowing in
                self?.isExportPresented = isShowing
                if isShowing {
                    self?.currentNavigationState = .export
                }
            }
            .store(in: &cancellables)
        
        // Listen for notification-based navigation
        NotificationCenter.default.publisher(for: .navigateToSession)
            .compactMap { $0.object as? HistorySession }
            .sink { [weak self] session in
                self?.navigateToSession(session)
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: .navigateToHistorySearch)
            .sink { [weak self] _ in
                self?.presentSearch()
            }
            .store(in: &cancellables)
    }
    
    private func handleSessionDeepLink(_ url: URL) {
        // Extract session ID from URL path
        let pathComponents = url.pathComponents
        guard pathComponents.count > 1,
              let sessionIdString = pathComponents.last,
              let sessionId = UUID(uuidString: sessionIdString) else {
            print("FloatingHistory: Invalid session deep link: \(url)")
            return
        }
        
        // Find session and navigate to it
        // This would typically involve fetching the session from Core Data
        print("FloatingHistory: Deep link to session ID: \(sessionId)")
        
        // For now, just navigate to search to find the session
        presentSearch()
    }
    
    private func handleSearchDeepLink(_ url: URL) {
        // Extract search query from URL parameters
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        let query = components?.queryItems?.first(where: { $0.name == "q" })?.value
        
        presentSearch()
        
        if let searchQuery = query {
            print("FloatingHistory: Deep link search query: \(searchQuery)")
            // This would be passed to the search view when it's presented
        }
    }
}

// MARK: - Navigation State Helpers

extension FloatingHistoryNavigationService {
    
    /// Check if currently viewing a specific session
    func isViewingSession(_ session: HistorySession) -> Bool {
        if case .sessionDetail(let currentSession) = currentNavigationState {
            return currentSession.id == session.id
        }
        return false
    }
    
    /// Check if currently in search mode
    var isInSearchMode: Bool {
        return currentNavigationState == .search
    }
    
    /// Check if currently in export mode
    var isInExportMode: Bool {
        return currentNavigationState == .export
    }
    
    /// Get navigation depth
    var navigationDepth: Int {
        return navigationPath.count
    }
    
    /// Check if can navigate back
    var canNavigateBack: Bool {
        return currentNavigationState != .list
    }
}

// MARK: - Navigation Analytics

extension FloatingHistoryNavigationService {
    
    /// Track navigation events for analytics
    private func trackNavigationEvent(_ event: String, parameters: [String: Any] = [:]) {
        var eventParameters = parameters
        eventParameters["navigation_state"] = String(describing: currentNavigationState)
        eventParameters["navigation_depth"] = navigationDepth
        
        print("FloatingHistory Navigation: \(event) - \(eventParameters)")
        
        // This would integrate with your analytics service
        // AnalyticsService.shared.track(event: event, parameters: eventParameters)
    }
}
