//
//  FloatingHistoryEmptyStateView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Enhanced empty state view specifically designed for floating history interface
/// Provides tab-specific messaging, animations, and call-to-action elements
struct FloatingHistoryEmptyStateView: View {
    
    // MARK: - Properties
    
    let selectedTab: HistoryTab
    let onAction: (() -> Void)?
    
    // MARK: - Animation State
    
    @State private var iconScale: CGFloat = 0.8
    @State private var contentOpacity: Double = 0.0
    @State private var buttonScale: CGFloat = 0.9
    @State private var pulseAnimation: Bool = false
    
    // MARK: - Design Constants
    
    private let iconSize: CGFloat = 72
    private let animationDuration: Double = 0.6
    private let staggerDelay: Double = 0.15
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.large) {
            Spacer()
            
            // Animated icon with pulse effect
            animatedIcon
            
            // Title and description with staggered animation
            textContent
            
            // Call-to-action button (if available)
            if hasCallToAction {
                callToActionButton
            }
            
            // Helpful tips section
            tipsSection
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, DesignSystem.spacing.large)
        .padding(.bottom, 120) // Account for floating tab bar
//        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
        .onAppear {
            startEntranceAnimation()
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityDescription)
    }
    
    // MARK: - View Components
    
    private var animatedIcon: some View {
        ZStack {
            // Background circle with pulse effect
            Circle()
                .fill(tabConfig.iconBackgroundColor.opacity(0.1))
                .frame(width: iconSize + 32, height: iconSize + 32)
                .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                .animation(
                    .easeInOut(duration: 2.0).repeatForever(autoreverses: true),
                    value: pulseAnimation
                )
            
            // Main icon
            Image(systemName: tabConfig.iconName)
                .font(.system(size: iconSize, weight: .light))
                .foregroundColor(tabConfig.iconColor)
                .scaleEffect(iconScale)
                .animation(
                    .spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0),
                    value: iconScale
                )
        }
    }
    
    private var textContent: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Title
            Text(tabConfig.title)
                .font(DesignSystem.typography.title2)
                .fontWeight(.semibold)
                .foregroundColor(DesignSystem.brandColors.adaptiveTextPrimary)
                .multilineTextAlignment(.center)
                .opacity(contentOpacity)
                .animation(
                    .easeOut(duration: animationDuration).delay(staggerDelay),
                    value: contentOpacity
                )
            
            // Description
            Text(tabConfig.description)
                .font(DesignSystem.typography.body)
                .foregroundColor(DesignSystem.brandColors.adaptiveTextSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .opacity(contentOpacity)
                .animation(
                    .easeOut(duration: animationDuration).delay(staggerDelay * 2),
                    value: contentOpacity
                )
        }
    }
    
    @ViewBuilder
    private var callToActionButton: some View {
        if let actionConfig = tabConfig.actionConfig {
            BrandButton(
                actionConfig.title,
                style: .primary,
                size: .small,
                isEnabled: true,
                action: {
                    onAction?()
                    HapticFeedbackManager.shared.buttonPressed()
                }
            )
            .scaleEffect(buttonScale)
            .opacity(contentOpacity)
            .animation(
                .spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0)
                .delay(staggerDelay * 3),
                value: buttonScale
            )
            .animation(
                .easeOut(duration: animationDuration).delay(staggerDelay * 3),
                value: contentOpacity
            )
            .accessibilityLabel(actionConfig.accessibilityLabel)
            .accessibilityHint(actionConfig.accessibilityHint)
        }
    }
    
    private var tipsSection: some View {
        VStack(spacing: DesignSystem.spacing.small) {
            ForEach(Array(tabConfig.tips.enumerated()), id: \.offset) { index, tip in
                HStack(spacing: DesignSystem.spacing.small) {
                    Image(systemName: "lightbulb.fill")
                        .font(.caption)
                        .foregroundColor(DesignSystem.brandColors.orchid)
                    
                    Text(tip)
                        .font(DesignSystem.typography.caption)
                        .foregroundColor(DesignSystem.brandColors.adaptiveTextSecondary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                .opacity(contentOpacity)
                .animation(
                    .easeOut(duration: animationDuration)
                    .delay(staggerDelay * (4 + Double(index))),
                    value: contentOpacity
                )
            }
        }
        .padding(.horizontal, DesignSystem.spacing.medium)
    }
    
    // MARK: - Configuration
    
    private var tabConfig: TabEmptyStateConfig {
        TabEmptyStateConfig.config(for: selectedTab)
    }
    
    private var hasCallToAction: Bool {
        tabConfig.actionConfig != nil && onAction != nil
    }
    
    private var accessibilityDescription: String {
        let baseDescription = "\(tabConfig.title). \(tabConfig.description)"
        if hasCallToAction, let actionConfig = tabConfig.actionConfig {
            return "\(baseDescription). \(actionConfig.accessibilityLabel) available."
        }
        return baseDescription
    }
    
    // MARK: - Animation Methods
    
    private func startEntranceAnimation() {
        // Start pulse animation immediately
        pulseAnimation = true
        
        // Staggered entrance animations
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            iconScale = 1.0
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: animationDuration)) {
                contentOpacity = 1.0
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + staggerDelay * 3) {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0)) {
                buttonScale = 1.0
            }
        }
    }
}

// MARK: - Tab Configuration

private struct TabEmptyStateConfig {
    let iconName: String
    let iconColor: Color
    let iconBackgroundColor: Color
    let title: String
    let description: String
    let tips: [String]
    let actionConfig: ActionConfig?
    
    struct ActionConfig {
        let title: String
        let accessibilityLabel: String
        let accessibilityHint: String
    }
    
    static func config(for tab: HistoryTab) -> TabEmptyStateConfig {
        switch tab {
        case .recents:
            return TabEmptyStateConfig(
                iconName: "waveform.circle",
                iconColor: DesignSystem.brandColors.orchid,
                iconBackgroundColor: DesignSystem.brandColors.orchid,
                title: "No Recent Notes",
                description: "Start recording to see your notes appear here. Your most recent notes will be displayed first.",
                tips: [
                    "Tap the record button to start your first note",
                    "All your recordings will appear in this tab automatically"
                ],
                actionConfig: ActionConfig(
                    title: "Start Recording",
                    accessibilityLabel: "Start Recording",
                    accessibilityHint: "Navigate to recording view to begin transcription"
                )
            )
            
        case .favorites:
            return TabEmptyStateConfig(
                iconName: "heart.circle",
                iconColor: DesignSystem.brandColors.orchid,
                iconBackgroundColor: DesignSystem.brandColors.orchid,
                title: "No Favorite Notes",
                description: "Mark important notes as favorites to find them quickly. Tap the heart icon on any note card.",
                tips: [
                    "Tap the ♥ icon on note cards to add favorites",
                    "Favorites help you quickly find important recordings"
                ],
                actionConfig: nil
            )
            
        case .saved:
            return TabEmptyStateConfig(
                iconName: "bookmark.circle",
                iconColor: DesignSystem.brandColors.orchid,
                iconBackgroundColor: DesignSystem.brandColors.orchid,
                title: "No Saved Notes",
                description: "Save notes for later reference. Tap the bookmark icon on any note card to save it.",
                tips: [
                    "Tap the save icon on note cards to save them",
                    "Saved items are perfect for important meetings or notes"
                ],
                actionConfig: nil
            )
        }
    }
}

// MARK: - Preview
#if DEBUG
struct FloatingHistoryEmptyStateViewPreview: View {
    @State private var selectedTab: HistoryTab = .recents
    @State private var actionFun = {}

    var body: some View {
        VStack {
            FloatingHistoryEmptyStateView(selectedTab: selectedTab, onAction: actionFun)
            
            // State switcher for preview
            HStack {
                Button("Ready") { selectedTab = .recents }
                Button("Listening") { selectedTab = .favorites }
                Button("Connecting") { selectedTab = .saved }
            }
            .padding()
        }
        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
    }
}

#Preview {
    FloatingHistoryEmptyStateViewPreview()
}
#endif
