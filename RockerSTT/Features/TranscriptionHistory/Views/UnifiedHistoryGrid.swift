//
//  UnifiedHistoryGrid.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Unified grid view for all tab categories (Recent, Favourite, Saved)
/// Provides consistent 2-column layout with smooth filtering transitions
struct UnifiedHistoryGrid: View {
    
    // MARK: - Properties
    
    let sessions: [HistorySession]
    let selectedTab: HistoryTab
    let onSessionTap: (HistorySession) -> Void
    let onFavoriteToggle: (HistorySession) -> Void
    let onSaveToggle: (HistorySession) -> Void
    let onDelete: (HistorySession) -> Void

    // MARK: - Animation Properties

    @StateObject private var animationManager = FloatingHistoryAnimationManager()
    @State private var previousSessionCount: Int = 0
    @State private var previousTab: HistoryTab = .recents
    
    // MARK: - Design Constants
    
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.spacing.historyGridSpacing),
        GridItem(.flexible(), spacing: DesignSystem.spacing.historyGridSpacing)
    ]
    
    private let horizontalPadding: CGFloat = DesignSystem.spacing.screenPadding
    private let verticalSpacing: CGFloat = DesignSystem.spacing.historyGridSpacing
    private let topPadding: CGFloat = DesignSystem.spacing.xSmall
    private let bottomPadding: CGFloat = 120 // Extra space for floating tab bar
    private let staggerDelay: Double = 0.05 // Delay between card animations
    
    // MARK: - Body
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: verticalSpacing) {
                ForEach(Array(sessions.enumerated()), id: \.element.id) { index, session in
                    HistoryGridCard(
                        session: session,
                        onTap: {
                            onSessionTap(session)
                        },
                        onFavoriteToggle: {
                            onFavoriteToggle(session)
                        },
                        onSaveToggle: {
                            onSaveToggle(session)
                        },
                        onDelete: {
                            onDelete(session)
                        }
                    )
                    .floatingHistoryItemAnimation(
                        animationManager: animationManager,
                        animationKey: "grid_item_\(session.id?.uuidString ?? "\(index)")"
                    )
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                    .animation(
                        DesignSystem.animations.cardEntrance.delay(Double(index) * staggerDelay),
                        value: selectedTab
                    )
                }
            }
            .padding(.horizontal, horizontalPadding)
            .padding(.top, topPadding)
            .padding(.bottom, bottomPadding)
        }
        .floatingHistoryLoadingAnimation(animationManager: animationManager)
        .coordinateSpace(name: "scroll") // For scroll offset detection
        .accessibilityIdentifier(AccessibilityHelper.Identifier.historyList)
        .onChange(of: selectedTab) { oldTab, newTab in
            handleTabChange(from: oldTab, to: newTab)
        }
        .onChange(of: sessions.count) { oldCount, newCount in
            handleSessionCountChange(from: oldCount, to: newCount)
        }
        .onAppear {
            setupInitialAnimation()
        }
    }

    // MARK: - Animation Methods

    /// Handles tab change animations
    private func handleTabChange(from oldTab: HistoryTab, to newTab: HistoryTab) {
        guard oldTab != newTab else { return }

        previousTab = oldTab
        animationManager.animateTabSwitch(
            from: oldTab,
            to: newTab,
            sessionCount: sessions.count
        )
    }

    /// Handles session count changes for filtering animations
    private func handleSessionCountChange(from oldCount: Int, to newCount: Int) {
        guard oldCount != newCount else { return }

        if newCount > oldCount {
            // New items added - animate entrance
            animationManager.animateContentEntrance(
                sessionCount: newCount - oldCount,
                animationKey: "new_items"
            )
        } else if newCount < oldCount {
            // Items removed - animate exit
            animationManager.animateContentRemoval(
                sessionCount: oldCount - newCount,
                animationKey: "removed_items"
            )
        }

        previousSessionCount = newCount
    }

    /// Sets up initial animation state
    private func setupInitialAnimation() {
        previousSessionCount = sessions.count
        previousTab = selectedTab

        // Animate initial content entrance
        animationManager.animateContentEntrance(
            sessionCount: sessions.count,
            animationKey: "initial"
        )
    }
}

// MARK: - Empty State View

/// Empty state view for when no sessions match the current filter
struct UnifiedHistoryGridEmptyState: View {
    let selectedTab: HistoryTab
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            Image(systemName: selectedTab.systemImage)
                .font(.system(size: 48, weight: .light))
                .foregroundColor(DesignSystem.brandColors.orchid.opacity(0.6))
            
            Text(emptyStateTitle)
                .font(DesignSystem.typography.headline)
                .foregroundColor(DesignSystem.brandColors.adaptiveTextPrimary)
                .multilineTextAlignment(.center)
            
            Text(emptyStateMessage)
                .font(DesignSystem.typography.body)
                .foregroundColor(DesignSystem.brandColors.adaptiveTextSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, DesignSystem.spacing.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.bottom, 120) // Account for floating tab bar
    }
    
    private var emptyStateTitle: String {
        switch selectedTab {
        case .recents:
            return "No Recent Notes"
        case .favorites:
            return "No Favorite Notes"
        case .saved:
            return "No Saved Notes"
        }
    }

    private var emptyStateMessage: String {
        switch selectedTab {
        case .recents:
            return "Start recording to see your notes here"
        case .favorites:
            return "Tap the heart icon on notes to add them to your favorites"
        case .saved:
            return "Tap the bookmark icon on notes to save them for later"
        }
    }
}

// MARK: - Container View with Empty State

/// Container that shows either the grid or empty state based on content
struct UnifiedHistoryGridContainer: View {
    let sessions: [HistorySession]
    let selectedTab: HistoryTab
    let onSessionTap: (HistorySession) -> Void
    let onFavoriteToggle: (HistorySession) -> Void
    let onSaveToggle: (HistorySession) -> Void
    let onDelete: (HistorySession) -> Void
    
    var body: some View {
        if sessions.isEmpty {
            UnifiedHistoryGridEmptyState(selectedTab: selectedTab)
        } else {
            UnifiedHistoryGrid(
                sessions: sessions,
                selectedTab: selectedTab,
                onSessionTap: onSessionTap,
                onFavoriteToggle: onFavoriteToggle,
                onSaveToggle: onSaveToggle,
                onDelete: onDelete
            )
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Grid with content
        UnifiedHistoryGrid(
            sessions: HistorySession.mockSessions,
            selectedTab: .recents,
            onSessionTap: { _ in },
            onFavoriteToggle: { _ in },
            onSaveToggle: { _ in },
            onDelete: { _ in }
        )
        .frame(height: 300)
        
        // Empty state
        UnifiedHistoryGridEmptyState(selectedTab: .favorites)
            .frame(height: 200)
    }
    .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
}

// MARK: - Mock Data Extension

extension HistorySession {
    static var mockSessions: [HistorySession] {
        // This would normally come from Core Data
        // For preview purposes, we'll return an empty array
        // In the actual implementation, this would be populated with real data
        return []
    }
}
