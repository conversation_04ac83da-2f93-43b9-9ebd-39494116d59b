//
//  TaggingView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// View for managing tags and categories for transcription sessions
struct TaggingView: View {
    
    // MARK: - Properties
    
    let session: HistorySession
    @StateObject private var taggingService = HistoryTaggingService()
    @Environment(\.dismiss) private var dismiss
    
    @State private var newTag = ""
    @State private var selectedCategory: String = ""
    @State private var showingSuggestions = false
    @State private var suggestedTags: [String] = []
    @State private var suggestedCategory: String?
    
    // Design constants
    private let sectionSpacing: CGFloat = 24
    private let tagSpacing: CGFloat = 8
    
    var currentTags: [String] {
        taggingService.getTags(for: session)
    }
    
    var currentCategory: String {
        taggingService.getCategory(for: session) ?? ""
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: sectionSpacing) {
                    // Session Info
                    SessionInfoCard(session: session)
                    
                    // Category Selection
                    CategorySelectionSection(
                        selectedCategory: $selectedCategory,
                        availableCategories: taggingService.availableCategories,
                        suggestedCategory: suggestedCategory,
                        onCategorySelected: { category in
                            taggingService.setCategory(category, for: session)
                        }
                    )
                    
                    // Current Tags
                    CurrentTagsSection(
                        tags: currentTags,
                        onTagRemoved: { tag in
                            taggingService.removeTag(tag, from: session)
                        }
                    )
                    
                    // Add New Tag
                    AddTagSection(
                        newTag: $newTag,
                        availableTags: taggingService.availableTags,
                        onTagAdded: { tag in
                            taggingService.addTag(tag, to: session)
                            newTag = ""
                        }
                    )
                    
                    // Suggested Tags
                    if !suggestedTags.isEmpty {
                        SuggestedTagsSection(
                            suggestedTags: suggestedTags,
                            currentTags: currentTags,
                            onTagAdded: { tag in
                                taggingService.addTag(tag, to: session)
                            }
                        )
                    }
                    
                    // Tag Statistics
                    TagStatisticsSection(taggingService: taggingService)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Tags & Categories")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Suggest") {
                        loadSuggestions()
                    }
                    .foregroundColor(.blue)
                }
            }
            .onAppear {
                selectedCategory = currentCategory
                loadSuggestions()
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadSuggestions() {
        suggestedTags = taggingService.suggestTags(for: session)
        suggestedCategory = taggingService.suggestCategory(for: session)
    }
}

// MARK: - Session Info Card

private struct SessionInfoCard: View {
    let session: HistorySession
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Session Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                Text(session.displayTitle)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(session.formattedDate)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if let preview = session.contentPreview {
                    Text(preview)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .padding(.top, 4)
                }
            }
        }
        .padding(16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Category Selection Section

private struct CategorySelectionSection: View {
    @Binding var selectedCategory: String
    let availableCategories: [String]
    let suggestedCategory: String?
    let onCategorySelected: (String) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Category")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                if let suggested = suggestedCategory, suggested != selectedCategory {
                    Button("Use Suggested: \(suggested)") {
                        selectedCategory = suggested
                        onCategorySelected(suggested)
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(availableCategories, id: \.self) { category in
                    CategoryCard(
                        category: category,
                        isSelected: selectedCategory == category,
                        onTap: {
                            selectedCategory = category
                            onCategorySelected(category)
                        }
                    )
                }
            }
            
            if !selectedCategory.isEmpty {
                Button("Clear Category") {
                    selectedCategory = ""
                    onCategorySelected("")
                }
                .font(.caption)
                .foregroundColor(.red)
            }
        }
    }
}

// MARK: - Category Card

private struct CategoryCard: View {
    let category: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(category)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(isSelected ? Color.blue : Color(.systemGray6))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Current Tags Section

private struct CurrentTagsSection: View {
    let tags: [String]
    let onTagRemoved: (String) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Current Tags")
                .font(.headline)
                .fontWeight(.semibold)
            
            if tags.isEmpty {
                Text("No tags added yet")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                TagCloudView(
                    tags: tags,
                    style: .removable,
                    onTagTapped: onTagRemoved
                )
            }
        }
    }
}

// MARK: - Add Tag Section

private struct AddTagSection: View {
    @Binding var newTag: String
    let availableTags: [String]
    let onTagAdded: (String) -> Void
    
    @State private var showingSuggestions = false
    
    var filteredSuggestions: [String] {
        guard !newTag.isEmpty else { return [] }
        return availableTags.filter { tag in
            tag.lowercased().contains(newTag.lowercased())
        }.prefix(5).map { $0 }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Add New Tag")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                TextField("Enter tag name", text: $newTag)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .onSubmit {
                        addTag()
                    }
                    .onChange(of: newTag) { _, _ in
                        showingSuggestions = !newTag.isEmpty && !filteredSuggestions.isEmpty
                    }
                
                Button("Add") {
                    addTag()
                }
                .disabled(newTag.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            
            if showingSuggestions && !filteredSuggestions.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Suggestions:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TagCloudView(
                        tags: filteredSuggestions,
                        style: .selectable,
                        onTagTapped: { tag in
                            newTag = tag
                            addTag()
                        }
                    )
                }
            }
        }
    }
    
    private func addTag() {
        let trimmedTag = newTag.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty else { return }
        
        onTagAdded(trimmedTag)
        showingSuggestions = false
    }
}

// MARK: - Suggested Tags Section

private struct SuggestedTagsSection: View {
    let suggestedTags: [String]
    let currentTags: [String]
    let onTagAdded: (String) -> Void
    
    var availableSuggestions: [String] {
        suggestedTags.filter { !currentTags.contains($0) }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Suggested Tags")
                .font(.headline)
                .fontWeight(.semibold)
            
            if availableSuggestions.isEmpty {
                Text("No new suggestions available")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                TagCloudView(
                    tags: availableSuggestions,
                    style: .addable,
                    onTagTapped: onTagAdded
                )
            }
        }
    }
}

// MARK: - Tag Statistics Section

private struct TagStatisticsSection: View {
    let taggingService: HistoryTaggingService
    
    @State private var tagStats: [String: Int] = [:]
    @State private var categoryStats: [String: Int] = [:]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Usage Statistics")
                .font(.headline)
                .fontWeight(.semibold)
            
            if !tagStats.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Most Used Tags:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(Array(tagStats.sorted { $0.value > $1.value }.prefix(5)), id: \.key) { tag, count in
                        HStack {
                            Text(tag)
                                .font(.body)
                            
                            Spacer()
                            
                            Text("\(count)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            if !categoryStats.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Category Distribution:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(Array(categoryStats.sorted { $0.value > $1.value }), id: \.key) { category, count in
                        HStack {
                            Text(category)
                                .font(.body)
                            
                            Spacer()
                            
                            Text("\(count)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .onAppear {
            loadStatistics()
        }
    }
    
    private func loadStatistics() {
        tagStats = taggingService.getTagStatistics()
        categoryStats = taggingService.getCategoryStatistics()
    }
}

// MARK: - Tag Cloud View

struct TagCloudView: View {
    let tags: [String]
    let style: TagStyle
    let onTagTapped: (String) -> Void
    
    enum TagStyle {
        case removable
        case selectable
        case addable
        
        var backgroundColor: Color {
            switch self {
            case .removable: return .red.opacity(0.1)
            case .selectable: return .blue.opacity(0.1)
            case .addable: return .green.opacity(0.1)
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .removable: return .red
            case .selectable: return .blue
            case .addable: return .green
            }
        }
        
        var icon: String {
            switch self {
            case .removable: return "xmark"
            case .selectable: return "hand.tap"
            case .addable: return "plus"
            }
        }
    }
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 80))
        ], spacing: 8) {
            ForEach(tags, id: \.self) { tag in
                TagChip(
                    tag: tag,
                    style: style,
                    onTapped: {
                        onTagTapped(tag)
                    }
                )
            }
        }
    }
}

// MARK: - Tag Chip

private struct TagChip: View {
    let tag: String
    let style: TagCloudView.TagStyle
    let onTapped: () -> Void
    
    var body: some View {
        Button(action: onTapped) {
            HStack(spacing: 4) {
                Text(tag)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Image(systemName: style.icon)
                    .font(.caption2)
            }
            .foregroundColor(style.foregroundColor)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(style.backgroundColor)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(style.foregroundColor.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        @State private var sampleSession: HistorySession = {
            let session = HistorySession()
            session.id = UUID()
            session.title = "Sample Meeting"
            session.contentPreview = "This is a sample meeting about project planning and team coordination."
            session.createdAt = Date()
            session.tags = "meeting,planning,team"
            session.category = "Meeting"
            return session
        }()
        
        var body: some View {
            TaggingView(session: sampleSession)
        }
    }
    
    return PreviewWrapper()
}
