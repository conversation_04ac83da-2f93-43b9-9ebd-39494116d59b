//
//  HistoryTabBar.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Custom tab bar component for history navigation with highlight animations
struct HistoryTabBar: View {
    
    // MARK: - Properties
    
    @Binding var selectedTab: HistoryTab
    let tabs: [HistoryTab] = HistoryTab.allCases
    
    // Animation properties
    @State private var highlightOffset: CGFloat = 0
    @State private var tabWidths: [CGFloat] = []
    
    // Design constants - Updated with brand colors
    private let highlightColor = DesignSystem.brandColors.persianPurple
    private let selectedTextColor = DesignSystem.brandColors.persianPurple
    private let unselectedTextColor = DesignSystem.brandColors.orchid.opacity(0.7)
    private let backgroundColor = DesignSystem.brandColors.alabaster
    private let highlightHeight: CGFloat = 3
    private let tabHeight: CGFloat = DesignSystem.spacing.tabBarHeight
    private let horizontalPadding: CGFloat = DesignSystem.spacing.medium
    
    var body: some View {
        VStack(spacing: 0) {
            // Tab buttons
            HStack(spacing: 0) {
                ForEach(Array(tabs.enumerated()), id: \.element) { index, tab in
                    TabButton(
                        tab: tab,
                        isSelected: selectedTab == tab,
                        action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedTab = tab
                                updateHighlightPosition(for: index)
                            }
                        }
                    )
                    .background(
                        GeometryReader { geometry in
                            Color.clear
                                .onAppear {
                                    updateTabWidth(for: index, width: geometry.size.width)
                                }
                                .onChange(of: geometry.size.width) { _, newWidth in
                                    updateTabWidth(for: index, width: newWidth)
                                }
                        }
                    )
                }
            }
            .frame(height: tabHeight)
            .background(backgroundColor)
            
            // Highlight indicator
            ZStack(alignment: .leading) {
                // Background line
                Rectangle()
                    .fill(DesignSystem.brandColors.frenchLilac.opacity(0.5))
                    .frame(height: 1)
                
                // Animated highlight
                Rectangle()
                    .fill(highlightColor)
                    .frame(width: currentTabWidth, height: highlightHeight)
                    .offset(x: highlightOffset)
                    .animation(.easeInOut(duration: 0.3), value: highlightOffset)
            }
        }
        .onAppear {
            // Initialize highlight position
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if let selectedIndex = tabs.firstIndex(of: selectedTab) {
                    updateHighlightPosition(for: selectedIndex)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private var currentTabWidth: CGFloat {
        guard let selectedIndex = tabs.firstIndex(of: selectedTab),
              selectedIndex < tabWidths.count else {
            return 0
        }
        return tabWidths[selectedIndex]
    }
    
    private func updateTabWidth(for index: Int, width: CGFloat) {
        // Ensure array is large enough
        while tabWidths.count <= index {
            tabWidths.append(0)
        }
        tabWidths[index] = width
        
        // Update highlight position if this is the selected tab
        if let selectedIndex = tabs.firstIndex(of: selectedTab), selectedIndex == index {
            updateHighlightPosition(for: selectedIndex)
        }
    }
    
    private func updateHighlightPosition(for index: Int) {
        guard index < tabWidths.count else { return }
        
        let offset = tabWidths.prefix(index).reduce(0, +)
        highlightOffset = offset
    }
}

// MARK: - Tab Button Component

private struct TabButton: View {
    let tab: HistoryTab
    let isSelected: Bool
    let action: () -> Void
    
    private let selectedTextColor = DesignSystem.brandColors.persianPurple
    private let unselectedTextColor = DesignSystem.brandColors.orchid.opacity(0.7)
    private let horizontalPadding: CGFloat = DesignSystem.spacing.medium
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: tab.systemImage)
                    .font(.system(size: 16, weight: isSelected ? .semibold : .regular))
                
                Text(tab.displayName)
                    .font(DesignSystem.typography.tabBarLabel)
                    .fontWeight(isSelected ? .semibold : .regular)
                    .dynamicTypeSize(...DynamicTypeSize.accessibility2)
            }
            .foregroundColor(isSelected ? selectedTextColor : unselectedTextColor)
            .padding(.horizontal, horizontalPadding)
            .frame(maxWidth: .infinity)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .minimumTouchTarget()
        .accessibilityIdentifier("\(AccessibilityHelper.Identifier.historyTabBar)_\(tab.rawValue)")
        .accessibilityLabel(AccessibilityHelper.Label.tabButton(title: tab.displayName, isSelected: isSelected))
        .accessibilityHint(AccessibilityHelper.Hint.tabButton)
        .accessibilityAddTraits(AccessibilityHelper.Trait.button)
        .accessibilityAddTraits(isSelected ? AccessibilityHelper.Trait.selected : [])
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        @State private var selectedTab: HistoryTab = .recents
        
        var body: some View {
            VStack {
                HistoryTabBar(selectedTab: $selectedTab)
                
                Spacer()
                
                Text("Selected: \(selectedTab.displayName)")
                    .font(.title2)
                    .padding()
                
                Spacer()
            }
            .background(Color(.systemGroupedBackground))
        }
    }
    
    return PreviewWrapper()
}
