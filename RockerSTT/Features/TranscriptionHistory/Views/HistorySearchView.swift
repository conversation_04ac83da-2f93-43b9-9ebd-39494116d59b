//
//  HistorySearchView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Full-screen modal search view for transcription history
struct HistorySearchView: View {
    
    // MARK: - Properties
    
    @StateObject private var searchViewModel = HistorySearchViewModel()
    @Environment(\.dismiss) private var dismiss
    @Environment(\.hapticFeedback) private var hapticFeedback
    @FocusState private var isSearchFieldFocused: Bool
    
    // Design constants
    private let cardSpacing: CGFloat = 12
    private let horizontalPadding: CGFloat = 16
    private let searchBarHeight: CGFloat = 44
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search bar
                SearchBar(
                    searchText: $searchViewModel.searchQuery,
                    isSearching: searchViewModel.isSearching,
                    onClear: {
                        searchViewModel.clearSearch()
                    }
                )
                .focused($isSearchFieldFocused)
                .padding(.horizontal, horizontalPadding)
                .padding(.vertical, 8)
                
                // Filter pills
                if searchViewModel.hasActiveSearch {
                    FilterPillsView(
                        selectedFilters: $searchViewModel.activeFilters,
                        onFilterToggle: { filter in
                            searchViewModel.toggleFilter(filter)
                        }
                    )
                    .padding(.horizontal, horizontalPadding)
                    .padding(.bottom, 8)
                    .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                // Content with smooth transitions
                Group {
                    if searchViewModel.hasActiveSearch {
                        // Search results with animations
                        if searchViewModel.isSearching {
                            LoadingSearchView()
                                .transition(.opacity.combined(with: .scale(scale: 0.9)))
                        } else if searchViewModel.searchResults.isEmpty {
                            EmptySearchResultsView(query: searchViewModel.searchQuery)
                                .transition(.opacity.combined(with: .move(edge: .bottom)))
                        } else {
                            SearchResultsList(
                                results: searchViewModel.searchResults,
                                searchQuery: searchViewModel.searchQuery
                            )
                            .transition(.opacity.combined(with: .move(edge: .trailing)))
                        }
                    } else {
                        // Search suggestions and recent searches
                        SearchSuggestionsView(
                            recentSearches: searchViewModel.recentSearches,
                            suggestions: searchViewModel.searchSuggestions,
                            onRecentSearchTap: { query in
                                withAnimation(DesignSystem.animations.standard) {
                                    searchViewModel.useRecentSearch(query)
                                }
                            },
                            onSuggestionTap: { suggestion in
                                withAnimation(DesignSystem.animations.standard) {
                                    searchViewModel.searchQuery = suggestion
                                }
                            },
                            onClearRecentSearches: {
                                withAnimation(DesignSystem.animations.quick) {
                                    searchViewModel.clearRecentSearches()
                                }
                            }
                        )
                        .transition(.opacity.combined(with: .move(edge: .leading)))
                    }
                }
                .animation(DesignSystem.animations.standard, value: searchViewModel.hasActiveSearch)
                .animation(DesignSystem.animations.quick, value: searchViewModel.isSearching)
                
                Spacer()
            }
            .navigationTitle("Search History")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .alert("Search Error", isPresented: .constant(searchViewModel.searchError != nil)) {
                Button("OK") {
                    searchViewModel.clearError()
                }
            } message: {
                Text(searchViewModel.searchError ?? "")
            }
        }
        .onAppear {
            isSearchFieldFocused = true
            searchViewModel.generateSuggestions()
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Search transcription history")
        .accessibilityHint("Search through your saved transcriptions using keywords or phrases")
    }
}

// MARK: - Enhanced Brand Search Bar Component

private struct SearchBar: View {
    @Binding var searchText: String
    let isSearching: Bool
    let onClear: () -> Void
    
    @FocusState private var isFocused: Bool
    @State private var animateSearch = false
    
    var body: some View {
        HStack(spacing: DesignSystem.spacing.small) {
            // Search icon with animation
            Image(systemName: "magnifyingglass")
                .foregroundColor(isFocused ? .brandPersianPurple : .secondary)
                .font(.system(size: 16, weight: .medium))
                .scaleEffect(animateSearch ? 1.1 : 1.0)
                .animation(DesignSystem.animations.quick, value: animateSearch)
            
            // Enhanced text field
            TextField("Search transcriptions...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .font(DesignSystem.typography.searchPlaceholder)
                .foregroundColor(.primary)
                .focused($isFocused)
                .onSubmit {
                    withAnimation(DesignSystem.animations.quick) {
                        animateSearch = true
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        animateSearch = false
                    }
                }
                .accessibilityLabel("Search field")
                .accessibilityHint("Enter text to search your transcription history")
            
            // Clear button or loading indicator with enhanced styling
            if isSearching {
                ProgressView()
                    .scaleEffect(0.8)
                    .tint(.brandOrchid)
            } else if !searchText.isEmpty {
                Button(action: {
                    withAnimation(DesignSystem.animations.quick) {
                        onClear()
                    }
                    HapticPattern.light.trigger()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.brandOrchid)
                        .font(.system(size: 16, weight: .medium))
                }
                .buttonStyle(PlainButtonStyle())
                .accessibilityLabel("Clear search")
                .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, DesignSystem.spacing.textFieldPadding)
        .padding(.vertical, 12)
        .frame(minHeight: DesignSystem.spacing.searchBarHeight)
        .background(backgroundView)
        .cornerRadius(CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: CornerRadius.medium)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .shadow(
            color: shadowColor,
            radius: isFocused ? 6 : 3,
            x: 0,
            y: isFocused ? 3 : 1
        )
        .scaleEffect(isFocused ? 1.02 : 1.0)
        .animation(DesignSystem.animations.standard, value: isFocused)
        .onTapGesture {
            isFocused = true
        }
    }
    
    private var backgroundView: some View {
        Color.brandFrenchLilac.opacity(0.3)
    }
    
    private var borderColor: Color {
        isFocused ? .brandPersianPurple : .brandFrenchLilac
    }
    
    private var borderWidth: CGFloat {
        isFocused ? 2 : 1
    }
    
    private var shadowColor: Color {
        isFocused ? .brandPersianPurple.opacity(0.2) : .brandOrchid.opacity(0.1)
    }
}

// MARK: - Enhanced Loading Search View

private struct LoadingSearchView: View {
    @State private var animateGradient = false
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Custom branded progress indicator
            ZStack {
                Circle()
                    .stroke(Color.brandFrenchLilac.opacity(0.3), lineWidth: 4)
                    .frame(width: 40, height: 40)
                
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(
                        AngularGradient(
                            colors: [.brandPersianPurple, .brandOrchid],
                            center: .center
                        ),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(animateGradient ? 360 : 0))
                    .animation(
                        Animation.linear(duration: 1.0).repeatForever(autoreverses: false),
                        value: animateGradient
                    )
            }
            
            VStack(spacing: DesignSystem.spacing.micro) {
                Text("Searching...")
                    .font(DesignSystem.typography.bodySecondary)
                    .foregroundColor(.brandPersianPurple)
                
                Text("Finding your transcriptions")
                    .font(DesignSystem.typography.captionPrimary)
                    .foregroundColor(.brandOrchid.opacity(0.8))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(DesignSystem.spacing.large)
        .onAppear {
            animateGradient = true
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Searching transcriptions")
        .accessibilityAddTraits(.updatesFrequently)
    }
}

// MARK: - Enhanced Empty Search Results View

private struct EmptySearchResultsView: View {
    let query: String
    
    @State private var animateIcon = false
    
    var body: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Animated search icon with brand styling
            ZStack {
                Circle()
                    .fill(Color.brandFrenchLilac.opacity(0.3))
                    .frame(width: 80, height: 80)
                
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 32, weight: .light))
                    .foregroundColor(.brandOrchid)
                    .scaleEffect(animateIcon ? 1.1 : 1.0)
                    .animation(
                        Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true),
                        value: animateIcon
                    )
            }
            
            VStack(spacing: DesignSystem.spacing.xSmall) {
                Text("No Results Found")
                    .font(DesignSystem.typography.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.brandPersianPurple)
                
                Text("No transcriptions found for \"\(query)\"")
                    .font(DesignSystem.typography.bodySecondary)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, DesignSystem.spacing.large)
                
                Text("Try adjusting your search terms or filters")
                    .font(DesignSystem.typography.captionPrimary)
                    .foregroundColor(.brandOrchid.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, DesignSystem.spacing.large)
                    .padding(.top, DesignSystem.spacing.micro)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(DesignSystem.spacing.large)
        .onAppear {
            animateIcon = true
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("No search results found for \(query)")
        .accessibilityHint("Try adjusting your search terms or filters")
    }
}

// MARK: - Enhanced Search Results List

private struct SearchResultsList: View {
    let results: [HistorySession]
    let searchQuery: String
    
    @State private var animateResults = false
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.spacing.cardSpacing) {
                // Results header with count
                HStack {
                    VStack(alignment: .leading, spacing: DesignSystem.spacing.micro) {
                        Text("Search Results")
                            .font(DesignSystem.typography.headline)
                            .foregroundColor(.brandPersianPurple)
                        
                        Text("\(results.count) transcription\(results.count == 1 ? "" : "s") found")
                            .font(DesignSystem.typography.captionPrimary)
                            .foregroundColor(.brandOrchid.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    // Sort indicator (future enhancement)
                    HStack(spacing: DesignSystem.spacing.micro) {
                        Image(systemName: "arrow.up.arrow.down")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.brandOrchid.opacity(0.6))
                        
                        Text("Recent")
                            .font(DesignSystem.typography.caption2)
                            .foregroundColor(.brandOrchid.opacity(0.6))
                    }
                }
                .padding(.horizontal, DesignSystem.spacing.cardMargin)
                .padding(.top, DesignSystem.spacing.xSmall)
                .opacity(animateResults ? 1 : 0)
                .offset(y: animateResults ? 0 : -10)
                .animation(DesignSystem.animations.standard.delay(0.1), value: animateResults)
                
                // Search results with staggered animations
                ForEach(Array(results.enumerated()), id: \.element.id) { index, session in
                    SearchResultCard(
                        session: session,
                        searchQuery: searchQuery,
                        onTap: {
                            print("Selected session: \(session.title ?? "Untitled Session")")
                            HapticPattern.light.trigger()
                        }
                    )
                    .padding(.horizontal, DesignSystem.spacing.cardMargin)
                    .opacity(animateResults ? 1 : 0)
                    .offset(y: animateResults ? 0 : 20)
                    .animation(
                        DesignSystem.animations.standard.delay(Double(index) * 0.05),
                        value: animateResults
                    )
                }
            }
            .padding(.top, DesignSystem.spacing.xSmall)
            .padding(.bottom, 100)
        }
        .onAppear {
            withAnimation {
                animateResults = true
            }
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Search results list")
        .accessibilityHint("\(results.count) results found")
    }
}

// MARK: - Enhanced Search Result Card

private struct SearchResultCard: View {
    let session: HistorySession
    let searchQuery: String
    let onTap: () -> Void
    
    @State private var isPressed = false
    @State private var animateHighlight = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.small) {
            // Header with enhanced styling
            HStack {
                VStack(alignment: .leading, spacing: DesignSystem.spacing.micro) {
                    // Highlighted title with brand colors
                    Text(highlightedTitle)
                        .font(DesignSystem.typography.headline)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                        .foregroundColor(.brandPersianPurple)
                    
                    // Date with brand styling
                    Text(formattedDate(session.createdAt ?? Date()))
                        .font(DesignSystem.typography.captionPrimary)
                        .foregroundColor(.brandOrchid.opacity(0.8))
                }
                
                Spacer()
                
                // Enhanced status indicators with brand colors
                HStack(spacing: DesignSystem.spacing.xSmall) {
                    if session.isFavourite {
                        Image(systemName: "heart.fill")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.red)
                            .background(
                                Circle()
                                    .fill(Color.red.opacity(0.1))
                                    .frame(width: 24, height: 24)
                            )
                    }
                    
                    if session.isSaved {
                        Image(systemName: "bookmark.fill")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.brandOrchid)
                            .background(
                                Circle()
                                    .fill(Color.brandOrchid.opacity(0.1))
                                    .frame(width: 24, height: 24)
                            )
                    }
                }
            }
            
            // Highlighted content preview with enhanced styling
            if let contentPreview = session.contentPreview, !contentPreview.isEmpty {
                Text(highlightedContent(contentPreview))
                    .font(DesignSystem.typography.bodySecondary)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .padding(.vertical, DesignSystem.spacing.micro)
            }
            
            // Enhanced metadata with brand styling
            HStack(spacing: DesignSystem.spacing.small) {
                MetadataItem(
                    icon: "text.bubble",
                    text: "\(session.entryCount) entries",
                    color: .brandOrchid
                )
                
                if session.duration > 0 {
                    MetadataItem(
                        icon: "clock",
                        text: formatDuration(session.duration),
                        color: .brandOrchid
                    )
                }
                
                Spacer()
                
                // Search relevance indicator
                if !searchQuery.isEmpty {
                    HStack(spacing: DesignSystem.spacing.micro) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.brandAmber)
                        
                        Text("Match")
                            .font(DesignSystem.typography.caption2)
                            .foregroundColor(.brandAmber)
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.brandAmber.opacity(0.1))
                    .cornerRadius(4)
                }
            }
        }
        .padding(DesignSystem.spacing.cardPadding)
        .background(Color.brandAlabaster)
        .cornerRadius(CornerRadius.card)
        .overlay(
            RoundedRectangle(cornerRadius: CornerRadius.card)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .shadow(
            color: shadowColor,
            radius: isPressed ? 6 : 8,
            x: 0,
            y: isPressed ? 3 : 4
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(DesignSystem.animations.standard, value: isPressed)
        .onTapGesture {
            onTap()
            HapticPattern.light.trigger()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {
            HapticPattern.medium.trigger()
        })
        .onAppear {
            withAnimation(DesignSystem.animations.slow.delay(0.1)) {
                animateHighlight = true
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Search result: \(session.title ?? "Untitled Session")")
        .accessibilityHint("Double tap to view details")
        .accessibilityAddTraits(.isButton)
    }
    
    // MARK: - Helper Properties
    
    private var highlightedTitle: AttributedString {
        highlightedText(session.title ?? "Untitled Session", query: searchQuery)
    }
    
    private func highlightedContent(_ content: String) -> AttributedString {
        highlightedText(content, query: searchQuery)
    }
    
    private var borderColor: Color {
        isPressed ? .brandOrchid : .brandFrenchLilac
    }
    
    private var borderWidth: CGFloat {
        isPressed ? 2 : 1
    }
    
    private var shadowColor: Color {
        isPressed ? .brandOrchid.opacity(0.2) : .brandPersianPurple.opacity(0.1)
    }
}

// MARK: - Metadata Item Component

private struct MetadataItem: View {
    let icon: String
    let text: String
    let color: Color
    
    var body: some View {
        HStack(spacing: DesignSystem.spacing.micro) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(color.opacity(0.8))
            
            Text(text)
                .font(DesignSystem.typography.captionPrimary)
                .foregroundColor(color.opacity(0.8))
        }
    }
    

}

// MARK: - Search Suggestions View

private struct SearchSuggestionsView: View {
    let recentSearches: [String]
    let suggestions: [String]
    let onRecentSearchTap: (String) -> Void
    let onSuggestionTap: (String) -> Void
    let onClearRecentSearches: () -> Void

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Recent searches
                if !recentSearches.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Recent Searches")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Spacer()

                            Button("Clear") {
                                onClearRecentSearches()
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }

                        LazyVStack(spacing: 8) {
                            ForEach(recentSearches, id: \.self) { search in
                                SearchSuggestionRow(
                                    text: search,
                                    icon: "clock",
                                    onTap: {
                                        onRecentSearchTap(search)
                                    }
                                )
                            }
                        }
                    }
                }

                // Search suggestions
                if !suggestions.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Suggestions")
                            .font(.headline)
                            .fontWeight(.semibold)

                        LazyVStack(spacing: 8) {
                            ForEach(suggestions, id: \.self) { suggestion in
                                SearchSuggestionRow(
                                    text: suggestion,
                                    icon: "magnifyingglass",
                                    onTap: {
                                        onSuggestionTap(suggestion)
                                    }
                                )
                            }
                        }
                    }
                }

                // Empty state
                if recentSearches.isEmpty && suggestions.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 48, weight: .light))
                            .foregroundColor(.secondary)

                        VStack(spacing: 8) {
                            Text("Search Your History")
                                .font(.title2)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)

                            Text("Find transcriptions by searching for words or phrases")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.top, 60)
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
        }
    }
}

// MARK: - Enhanced Search Suggestion Row

private struct SearchSuggestionRow: View {
    let text: String
    let icon: String
    let onTap: () -> Void
    
    @State private var isPressed = false

    var body: some View {
        Button(action: {
            onTap()
            HapticPattern.selection.trigger()
        }) {
            HStack(spacing: DesignSystem.spacing.small) {
                // Icon with brand styling
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.brandOrchid)
                    .frame(width: 24, height: 24)
                    .background(Color.brandFrenchLilac.opacity(0.3))
                    .cornerRadius(6)

                // Suggestion text
                Text(text)
                    .font(DesignSystem.typography.bodySecondary)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)

                Spacer()

                // Action indicator
                Image(systemName: "arrow.up.left")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.brandOrchid.opacity(0.7))
            }
            .padding(.horizontal, DesignSystem.spacing.small)
            .padding(.vertical, DesignSystem.spacing.small)
            .background(backgroundColor)
            .cornerRadius(CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.medium)
                    .stroke(borderColor, lineWidth: 1)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(DesignSystem.animations.buttonPress, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityLabel("Search suggestion: \(text)")
        .accessibilityHint("Double tap to search for this term")
        .accessibilityAddTraits(.isButton)
    }
    
    private var backgroundColor: Color {
        isPressed ? .brandFrenchLilac.opacity(0.5) : .brandFrenchLilac.opacity(0.2)
    }
    
    private var borderColor: Color {
        isPressed ? .brandOrchid.opacity(0.5) : .brandFrenchLilac.opacity(0.8)
    }
}

// MARK: - Filter Pills View

private struct FilterPillsView: View {
    @Binding var selectedFilters: Set<SearchFilter>
    let onFilterToggle: (SearchFilter) -> Void
    
    private let availableFilters: [SearchFilter] = [
        .recent,
        .favorites,
        .saved,
        .translated,
        .longSessions
    ]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: DesignSystem.spacing.filterPillSpacing) {
                ForEach(availableFilters, id: \.self) { filter in
                    FilterPill(
                        filter: filter,
                        isSelected: selectedFilters.contains(filter),
                        onTap: {
                            withAnimation(DesignSystem.animations.quick) {
                                onFilterToggle(filter)
                            }
                            HapticPattern.selection.trigger()
                        }
                    )
                }
            }
            .padding(.horizontal, DesignSystem.spacing.small)
        }
    }
}

// MARK: - Filter Pill Component

private struct FilterPill: View {
    let filter: SearchFilter
    let isSelected: Bool
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.spacing.micro) {
                // Filter icon
                Image(systemName: filter.iconName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(textColor)
                
                // Filter label
                Text(filter.displayName)
                    .font(DesignSystem.typography.captionPrimary)
                    .fontWeight(.medium)
                    .foregroundColor(textColor)
            }
            .padding(.horizontal, DesignSystem.spacing.filterPillPadding)
            .padding(.vertical, DesignSystem.spacing.xSmall)
            .background(backgroundColor)
            .cornerRadius(CornerRadius.large)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.large)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .shadow(
                color: shadowColor,
                radius: isSelected ? 4 : 2,
                x: 0,
                y: isSelected ? 2 : 1
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(DesignSystem.animations.buttonPress, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityLabel(filter.displayName)
        .accessibilityHint(isSelected ? "Selected filter" : "Tap to apply filter")
        .accessibilityAddTraits(.isButton)
    }
    
    private var backgroundColor: Color {
        isSelected ? .brandOrchid : .brandFrenchLilac.opacity(0.3)
    }
    
    private var textColor: Color {
        isSelected ? .white : .brandPersianPurple
    }
    
    private var borderColor: Color {
        isSelected ? .brandOrchid : .brandFrenchLilac
    }
    
    private var borderWidth: CGFloat {
        isSelected ? 0 : 1
    }
    
    private var shadowColor: Color {
        isSelected ? .brandOrchid.opacity(0.3) : .brandFrenchLilac.opacity(0.2)
    }
}

// MARK: - Helper Functions

private func highlightedText(_ text: String, query: String) -> AttributedString {
    guard !query.isEmpty else {
        return AttributedString(text)
    }
    
    var attributedString = AttributedString(text)
    let searchTerms = query.components(separatedBy: .whitespacesAndNewlines)
        .filter { !$0.isEmpty }
    
    for term in searchTerms {
        let ranges = text.ranges(of: term, options: .caseInsensitive)
        
        for range in ranges {
            if let attributedRange = Range(range, in: attributedString) {
                // Use brand Amber color for highlighting
                attributedString[attributedRange].backgroundColor = Color.brandAmber.opacity(0.3)
                attributedString[attributedRange].foregroundColor = Color.brandPersianPurple
                attributedString[attributedRange].font = .system(.body, weight: .semibold)
            }
        }
    }
    
    return attributedString
}

private func formattedDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .short
    return formatter.string(from: date)
}

private func formatDuration(_ duration: TimeInterval) -> String {
    let minutes = Int(duration) / 60
    let seconds = Int(duration) % 60
    
    if minutes > 0 {
        return "\(minutes)m \(seconds)s"
    } else {
        return "\(seconds)s"
    }
}

// MARK: - Preview

#Preview {
    HistorySearchView()
}
