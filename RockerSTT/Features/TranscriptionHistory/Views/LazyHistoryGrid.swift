//
//  LazyHistoryGrid.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Performance-optimized lazy loading grid for history sessions
/// Implements viewport-based loading and memory management
struct LazyHistoryGrid: View {
    
    // MARK: - Properties
    
    let sessions: [HistorySession]
    let selectedTab: HistoryTab
    let onSessionTap: (HistorySession) -> Void
    let onFavoriteToggle: (HistorySession) -> Void
    let onSaveToggle: (HistorySession) -> Void
    let onDelete: (HistorySession) -> Void
    let onVisibleRangeChanged: (Int, Int) -> Void
    
    // MARK: - State
    
    // Performance optimizer removed for simplicity
    @State private var visibleItems: Set<Int> = []
    @State private var loadedSessions: [Int: HistorySession] = [:]
    
    // MARK: - Design Constants
    
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.spacing.medium),
        GridItem(.flexible(), spacing: DesignSystem.spacing.medium)
    ]
    
    private let itemSpacing: CGFloat = DesignSystem.spacing.medium
    private let bottomPadding: CGFloat = 120 // Account for floating tab bar
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVGrid(columns: columns, spacing: itemSpacing) {
                    ForEach(Array(sessions.enumerated()), id: \.element.id) { index, session in
                        HistorySessionCard(
                            session: session,
                            onTap: { onSessionTap(session) },
                            onFavoriteToggle: { onFavoriteToggle(session) },
                            onSaveToggle: { onSaveToggle(session) },
                            onDelete: { onDelete(session) }
                        )
                        .onAppear {
                            handleItemAppear(index: index, session: session)
                        }
                        .onDisappear {
                            handleItemDisappear(index: index)
                        }
                        .id("session_\(index)")
                    }
                }
                .padding(.horizontal, DesignSystem.spacing.medium)
                .padding(.bottom, bottomPadding)
            }
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            updateVisibleRange(geometry: geometry)
                        }
                        .onChange(of: geometry.frame(in: .global)) {
                            updateVisibleRange(geometry: geometry)
                        }
                }
            )
        }
        // Performance optimization monitoring removed
    }
    
    // MARK: - Helper Methods
    
    /// Handles when an item appears in the viewport
    private func handleItemAppear(index: Int, session: HistorySession) {
        visibleItems.insert(index)
        loadedSessions[index] = session
        if session.id != nil {
            // Performance optimization removed
        }
        
        // Update visible range
        updateVisibleRangeFromItems()
    }
    
    /// Handles when an item disappears from the viewport
    private func handleItemDisappear(index: Int) {
        visibleItems.remove(index)
        
        // Keep session loaded for a while to avoid reload flicker
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            if !visibleItems.contains(index) {
                loadedSessions.removeValue(forKey: index)
            }
        }
        
        updateVisibleRangeFromItems()
    }
    

    
    /// Updates visible range based on currently visible items
    private func updateVisibleRangeFromItems() {
        guard !visibleItems.isEmpty else { return }

        let minIndex = visibleItems.min() ?? 0
        let maxIndex = visibleItems.max() ?? 0

        // Ensure valid range (should always be true, but adding safety check)
        guard minIndex <= maxIndex else { return }

        onVisibleRangeChanged(minIndex, maxIndex)
    }
    
    /// Updates visible range based on scroll geometry
    private func updateVisibleRange(geometry: GeometryProxy) {
        let frame = geometry.frame(in: .global)
        let itemHeight: CGFloat = 200 // Approximate session card height
        let itemsPerRow = 2

        let visibleStartRow = max(0, Int(frame.minY / itemHeight))
        let visibleEndRow = Int(frame.maxY / itemHeight) + 1

        let startIndex = visibleStartRow * itemsPerRow
        let endIndex = min(sessions.count, visibleEndRow * itemsPerRow)

        // Ensure valid range: startIndex <= endIndex
        let validStartIndex = min(startIndex, sessions.count)
        let validEndIndex = max(validStartIndex, endIndex)

        onVisibleRangeChanged(validStartIndex, validEndIndex)
    }
    
    // Performance optimization functions removed for simplicity
}



// MARK: - Preview

#Preview {
    LazyHistoryGrid(
        sessions: [],
        selectedTab: .recents,
        onSessionTap: { _ in },
        onFavoriteToggle: { _ in },
        onSaveToggle: { _ in },
        onDelete: { _ in },
        onVisibleRangeChanged: { _, _ in }
    )
}
