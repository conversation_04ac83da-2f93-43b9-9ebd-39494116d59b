//
//  HistoryGridContent.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Grid layout component for displaying favorite sessions in a 2-column layout
struct HistoryGridContent: View {
    
    // MARK: - Properties
    
    let sessions: [HistorySession]
    let onSessionTap: (HistorySession) -> Void
    let onFavoriteToggle: (HistorySession) -> Void
    let onSaveToggle: (HistorySession) -> Void
    let onDelete: (HistorySession) -> Void
    
    // Design constants optimized for iPhone 16 with brand spacing
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.spacing.historyGridSpacing),
        GridItem(.flexible(), spacing: DesignSystem.spacing.historyGridSpacing)
    ]
    private let horizontalPadding: CGFloat = DesignSystem.spacing.screenPadding
    private let verticalSpacing: CGFloat = DesignSystem.spacing.historyGridSpacing
    private let topPadding: CGFloat = DesignSystem.spacing.xSmall
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: verticalSpacing) {
                ForEach(sessions, id: \.id) { session in
                    HistoryGridCard(
                        session: session,
                        onTap: {
                            onSessionTap(session)
                        },
                        onFavoriteToggle: {
                            onFavoriteToggle(session)
                        },
                        onSaveToggle: {
                            onSaveToggle(session)
                        },
                        onDelete: {
                            onDelete(session)
                        }
                    )
                }
            }
            .padding(.horizontal, horizontalPadding)
            .padding(.top, topPadding)
            .padding(.bottom, 100) // Extra bottom padding for safe area
        }
    }
}

/// Grid card component for displaying individual sessions in the favorites grid
struct HistoryGridCard: View {
    
    // MARK: - Properties
    
    let session: HistorySession
    let onTap: () -> Void
    let onFavoriteToggle: () -> Void
    let onSaveToggle: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDeleteAlert = false
    @State private var isPressed = false
    @State private var showingDetailView = false
    @StateObject private var actionService = HistoryActionService()
    
    // Design constants - Updated with brand colors
    private let cornerRadius: CGFloat = CornerRadius.card
    private let shadowRadius: CGFloat = ShadowStyles.card.radius
    private let cardPadding: CGFloat = DesignSystem.spacing.cardPadding
    private let minHeight: CGFloat = DesignSystem.spacing.historyCardMinHeight
    private let favoriteColor = DesignSystem.brandColors.amber
    private let saveColor = DesignSystem.brandColors.orchid
    
    // Adaptive brand-based background colors for grid cards
    private let backgroundColors: [Color] = [
        DesignSystem.brandColors.adaptiveTertiary.opacity(0.3),
        DesignSystem.brandColors.adaptiveSecondary.opacity(0.1),
        DesignSystem.brandColors.adaptivePrimary.opacity(0.05),
        DesignSystem.brandColors.adaptiveAccent.opacity(0.1),
        DesignSystem.brandColors.adaptiveBackground,
        DesignSystem.brandColors.adaptiveTertiary.opacity(0.2)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with actions
            HStack {
                Spacer()
                
                // Action buttons
                HStack(spacing: 8) {
                    // Favorite button
                    Button(action: onFavoriteToggle) {
                        Image(systemName: session.isFavourite ? "heart.fill" : "heart")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(session.isFavourite ? favoriteColor : .secondary)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Save button
                    Button(action: onSaveToggle) {
                        Image(systemName: session.isSaved ? "bookmark.fill" : "bookmark")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(session.isSaved ? saveColor : .secondary)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Speak button
                    Button(action: {
                        if actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking {
                            actionService.stopSpeaking()
                        } else {
                            actionService.speakSession(session)
                        }
                    }) {
                        Image(systemName: actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking ? "speaker.slash" : "speaker.wave.2")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(actionService.currentSpeakingSessionId == session.id && actionService.isSpeaking ? .blue : .secondary)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // More actions menu
                    Menu {
                        Button(action: {
                            actionService.copyToClipboard(session: session)
                        }) {
                            Label("Copy Text", systemImage: "doc.on.clipboard")
                        }

                        Button("Delete", role: .destructive) {
                            showingDeleteAlert = true
                        }
                    } label: {
                        Image(systemName: "ellipsis")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // Main content
            VStack(alignment: .leading, spacing: 8) {
                // Session title
                Text(session.displayTitle)
                    .font(DesignSystem.typography.heading3)
                    .foregroundColor(DesignSystem.brandColors.persianPurple)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                // Content preview
                if let contentPreview = session.contentPreview, !contentPreview.isEmpty {
                    Text(contentPreview)
                        .font(DesignSystem.typography.captionPrimary)
                        .foregroundColor(DesignSystem.colors.textSecondary)
                        .lineLimit(4)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Bottom metadata
                VStack(alignment: .leading, spacing: 4) {
                    // Date
                    Text(session.formattedDate)
                        .font(DesignSystem.typography.captionSecondary)
                        .foregroundColor(DesignSystem.brandColors.orchid)
                    
                    // Entry count and duration
                    HStack(spacing: 8) {
                        HStack(spacing: 2) {
                            Image(systemName: "text.bubble")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            Text("\(session.entryCount)")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        
                        if let duration = session.formattedDuration {
                            HStack(spacing: 2) {
                                Image(systemName: "clock")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Text(duration)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        // Status indicators
                        HStack(spacing: 4) {
                            if session.isFavourite {
                                Image(systemName: "heart.fill")
                                    .font(.caption2)
                                    .foregroundColor(favoriteColor)
                            }
                            
                            if session.isSaved {
                                Image(systemName: "bookmark.fill")
                                    .font(.caption2)
                                    .foregroundColor(saveColor)
                            }
                        }
                    }
                }
            }
        }
        .padding(cardPadding)
        .frame(minHeight: minHeight)
        .background(cardBackgroundColor)
        .overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(DesignSystem.brandColors.frenchLilac, lineWidth: 1)
        )
        .cornerRadius(cornerRadius)
        .shadow(color: DesignSystem.brandColors.persianPurple.opacity(0.1), radius: shadowRadius, x: 0, y: 2)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)

        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {
            // Long press action could be added here if needed
        }
        .alert("Delete Session", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                do {
                    try actionService.deleteSession(session)
                    onDelete()
                } catch {
                    print("Failed to delete session: \(error)")
                }
            }
        } message: {
            Text("Are you sure you want to delete this transcription session? This action cannot be undone.")
        }
        .onTapGesture {
            showingDetailView = true
        }
        .sheet(isPresented: $showingDetailView) {
            HistoryDetailView(session: session)
        }
    }
    
    // MARK: - Helper Properties
    
    private var cardBackgroundColor: Color {
        // Use session ID to consistently pick a color
        let sessionIdString = session.id?.uuidString ?? ""
        let hash = abs(sessionIdString.hashValue)
        let colorIndex = hash % backgroundColors.count
        return backgroundColors[colorIndex]
    }
}



// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        @State private var sampleSessions: [HistorySession] = {
            var sessions: [HistorySession] = []
            
            for i in 0..<6 {
                let session = HistorySession()
                session.id = UUID()
                session.title = "Session \(i + 1)"
                session.contentPreview = "This is a sample transcription content for session \(i + 1). It contains some text to demonstrate the grid layout."
                session.createdAt = Date().addingTimeInterval(-Double(i * 3600))
                session.updatedAt = Date().addingTimeInterval(-Double(i * 3600 - 300))
                session.entryCount = Int32(10 + i * 5)
                session.isFavourite = i % 2 == 0
                session.isSaved = i % 3 == 0
                sessions.append(session)
            }
            
            return sessions
        }()
        
        var body: some View {
            NavigationView {
                HistoryGridContent(
                    sessions: sampleSessions,
                    onSessionTap: { session in
                        print("Session tapped: \(session.displayTitle)")
                    },
                    onFavoriteToggle: { session in
                        session.isFavourite.toggle()
                    },
                    onSaveToggle: { session in
                        session.isSaved.toggle()
                    },
                    onDelete: { session in
                        print("Delete session: \(session.displayTitle)")
                    }
                )
                .navigationTitle("Favorites")
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#E8F6E9"),
                            Color(hex: "#CFEFCC")
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .ignoresSafeArea()
                )
            }
        }
    }
    
    return PreviewWrapper()
}
