//
//  HistoryListView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// Main history list view with card-based layout and gradient background
struct HistoryListView: View {
    
    // MARK: - Properties
    
    @StateObject private var viewModel = HistoryListViewModel()
    @StateObject private var errorHandler = HistoryErrorHandlingService()
    @StateObject private var animationState = AnimationStateManager()
    @State private var showingSearchView = false
    @State private var showingExportOptions = false
    @Environment(\.hapticFeedback) private var hapticFeedback
    
    // Design constants - Updated with brand colors
    private let cardSpacing: CGFloat = DesignSystem.spacing.cardSpacing
    private let horizontalPadding: CGFloat = DesignSystem.spacing.screenPadding
    private let topPadding: CGFloat = DesignSystem.spacing.xSmall
    
    var body: some View {
        NavigationView {
            ZStack {
                backgroundGradient
                mainContentView
            }
            .navigationTitle("History")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbarBackground(DesignSystem.brandColors.persianPurple, for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbar {
                trailingToolbarItems
//                leadingToolbarItems
            }
            .sheet(isPresented: $showingSearchView) {
                HistorySearchView()
                    .smoothModalPresentation()
            }
//            .sheet(isPresented: $showingExportOptions) {
//                ExportOptionsView(sessions: viewModel.filteredSessions)
//                    .smoothModalPresentation()
//            }
            .withErrorHandling(errorHandler)
        }
        .onAppear {
            viewModel.loadSessions()
        }
    }

    // MARK: - View Components

    private var backgroundGradient: some View {
        DesignSystem.brandColors.adaptiveBackgroundGradient
            .ignoresSafeArea()
    }

    private var mainContentView: some View {
        VStack(spacing: 0) {
            historyTabBar
            contentBasedOnState
        }
    }

    private var historyTabBar: some View {
        HistoryTabBar(selectedTab: $viewModel.selectedTab)
            .background(DesignSystem.brandColors.alabaster)
            .accessibilityIdentifier(AccessibilityHelper.Identifier.historyTabBar)
    }

    @ViewBuilder
    private var contentBasedOnState: some View {
        if viewModel.isLoading && viewModel.filteredSessions.isEmpty {
            // Only show loading state if we have no data at all
            loadingStateView
        } else if let error = viewModel.error {
            errorStateView(error: error)
        } else if viewModel.filteredSessions.isEmpty && !viewModel.isLoading {
            // Only show empty state if we're not loading and truly have no data
            emptyStateView
        } else {
            sessionContentView
        }
    }

    private var loadingStateView: some View {
        BrandLoadingStateView(
            message: "Loading your transcription history...",
            showProgress: false
        )
        .accessibilityIdentifier(AccessibilityHelper.Identifier.historyLoadingState)
        .accessibilityLabel(AccessibilityHelper.Label.loadingState)
    }

    private func errorStateView(error: Error) -> some View {
        let historyError = error as? HistoryError ?? .unknown(error.localizedDescription)
        return BrandErrorStateView(
            errorType: mapHistoryErrorToBrandError(historyError),
            message: historyError.description,
            primaryAction: {
                viewModel.retryLoading()
            }
        )
    }

    private var emptyStateView: some View {
        BrandEmptyStateView(
            emptyStateType: mapHistoryEmptyStateToBrandEmptyState(emptyStateType),
            primaryAction: {
                hapticFeedback.buttonPressed()
                handleEmptyStateAction()
            }
        )
        .accessibilityIdentifier(AccessibilityHelper.Identifier.historyEmptyState)
        .accessibilityLabel(AccessibilityHelper.Label.emptyState)
    }

    @ViewBuilder
    private var sessionContentView: some View {
        if viewModel.selectedTab == .favorites {
            favoritesGridView
        } else {
            sessionListView
        }
    }

    private var favoritesGridView: some View {
        HistoryGridContent(
            sessions: viewModel.filteredSessions,
            onSessionTap: { session in
                print("Selected session: \(session.displayTitle)")
            },
            onFavoriteToggle: { session in
                viewModel.toggleFavorite(for: session)
            },
            onSaveToggle: { session in
                viewModel.toggleSaved(for: session)
            },
            onDelete: { session in
                viewModel.deleteSession(session)
            }
        )
    }

    private var sessionListView: some View {
        ScrollView {
            LazyVStack(spacing: cardSpacing) {
                sessionCardsView
                loadMoreButtonView
                performanceMetricsView
            }
        }
        .accessibilityIdentifier(AccessibilityHelper.Identifier.historyList)
        .refreshable {
            hapticFeedback.pullToRefresh()
            viewModel.refreshSessions()
        }
    }

    private var sessionCardsView: some View {
        ForEach(Array(viewModel.filteredSessions.enumerated()), id: \.element.id) { index, session in
            AnimatedSessionCard(
                session: session,
                onTap: {
                    hapticFeedback.sessionSelected()
                },
                onFavoriteToggle: {
                    viewModel.toggleFavorite(for: session)
                },
                onSaveToggle: {
                    viewModel.toggleSaved(for: session)
                },
                onDelete: {
                    viewModel.deleteSession(session)
                }
            )
            .cardEntranceAnimation(index: index)
            .onAppear {
                // Check if we need to preload more data
                viewModel.checkForPreload(at: index)
            }
        }
    }

    @ViewBuilder
    private var loadMoreButtonView: some View {
        if viewModel.hasMoreData && !viewModel.isLoading {
            Button("Load More") {
                Task {
                    await viewModel.loadNextPage()
                }
            }
            .font(DesignSystem.typography.buttonLabel)
            .foregroundColor(DesignSystem.brandColors.persianPurple)
            .padding(.horizontal, DesignSystem.spacing.buttonPaddingHorizontal)
            .padding(.vertical, DesignSystem.spacing.buttonPaddingVertical)
            .background(DesignSystem.brandColors.frenchLilac.opacity(0.3))
            .cornerRadius(CornerRadius.button)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.button)
                    .stroke(DesignSystem.brandColors.orchid.opacity(0.3), lineWidth: 1)
            )
            .accessibilityIdentifier(AccessibilityHelper.Identifier.loadMoreButton)
        }
    }

    @ViewBuilder
    private var performanceMetricsView: some View {
        if ProcessInfo.processInfo.environment["DEBUG_PERFORMANCE"] == "1" {
            VStack(alignment: .leading, spacing: 4) {
                Text("Performance Metrics")
                    .font(.caption.bold())
                Text(viewModel.performanceMetrics)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .padding(.horizontal)
        }
    }

    // MARK: - Toolbar Items

    private var trailingToolbarItems: ToolbarItem<(), some View> {
        ToolbarItem(placement: .navigationBarTrailing) {
            HStack(spacing: 16) {
//                exportButton
                searchButton
            }
        }
    }

    @ViewBuilder
    private var exportButton: some View {
        if !viewModel.filteredSessions.isEmpty {
            Button(action: {
                showingExportOptions = true
            }) {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 18, weight: .medium))
            }
        }
    }

    private var searchButton: some View {
        Button(action: {
            showingSearchView = true
        }) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 18, weight: .medium))
        }
    }

    private var leadingToolbarItems: ToolbarItem<(), some View> {
        ToolbarItem(placement: .navigationBarLeading) {
            Button(action: {
                viewModel.refreshSessions()
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 18, weight: .medium))
            }
        }
    }

    // MARK: - Empty State Content
    
    private var emptyStateType: HistoryEmptyStateType {
        switch viewModel.selectedTab {
        case .recents:
            return .noHistory
        case .saved:
            return .noSaved
        case .favorites:
            return .noFavorites
        }
    }

    private func handleEmptyStateAction() {
        switch viewModel.selectedTab {
        case .recents:
            // Navigate to recording view
            print("Navigate to recording")
        case .saved, .favorites:
            // No action for these states
            break
        }
    }
    
    // MARK: - State Mapping Functions
    
    private func mapHistoryErrorToBrandError(_ historyError: HistoryError) -> BrandErrorType {
        switch historyError {
        case .networkUnavailable:
            return .networkError(historyError.description)
        case .storageFailure:
            return .serverError(historyError.description)
        case .permissionDenied:
            return .permissionError(historyError.description)
        case .dataCorruption, .exportFailure, .unknown:
            return .unknownError(historyError.description)
        }
    }
    
    private func mapHistoryEmptyStateToBrandEmptyState(_ historyEmptyState: HistoryEmptyStateType) -> BrandEmptyStateType {
        switch historyEmptyState {
        case .noHistory, .noSaved, .noFavorites:
            return .noHistory
        case .noSearchResults(let query):
            return .noSearchResults(query)
        case .networkError:
            return .offlineMode
        case .storageError, .permissionDenied:
            return .microphoneDisabled
        }
    }
}





// MARK: - Preview

// MARK: - Preview Support

#Preview {
    HistoryListView()
}

#Preview("Brand Loading State") {
    BrandLoadingStateView(message: "Loading history...")
}

#Preview("Brand Empty State") {
    BrandEmptyStateView(
        emptyStateType: .noHistory,
        primaryAction: {
            print("Start recording tapped")
        }
    )
}

#Preview("Brand Error State") {
    BrandErrorStateView(
        errorType: .networkError("Connection failed"),
        message: "Unable to load transcription history",
        primaryAction: {
            print("Retry tapped")
        }
    )
}
