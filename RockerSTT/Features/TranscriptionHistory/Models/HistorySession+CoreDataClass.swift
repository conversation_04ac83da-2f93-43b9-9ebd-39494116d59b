//
//  HistorySession+CoreDataClass.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 7/19/25.
//

import Foundation
import CoreData

@objc(HistorySession)
public class HistorySession: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /// Returns the entries as a sorted array by timestamp
    var sortedEntries: [HistoryEntry] {
        guard let context = managedObjectContext, let sessionId = id else { return [] }

        let request: NSFetchRequest<HistoryEntry> = HistoryEntry.fetchRequest()
        request.predicate = NSPredicate(format: "sessionId == %@", sessionId as CVarArg)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \HistoryEntry.timestamp, ascending: true)]

        do {
            return try context.fetch(request)
        } catch {
            print("Failed to fetch entries for session: \(error)")
            return []
        }
    }
    
    /// Returns the first entry's timestamp for session ordering
    var firstEntryTimestamp: Date {
        return sortedEntries.first?.timestamp ?? createdAt ?? Date()
    }

    /// Returns the last entry's timestamp for session ordering
    var lastEntryTimestamp: Date {
        return sortedEntries.last?.timestamp ?? updatedAt ?? Date()
    }
    
    /// Generates a content preview from the first few entries
    func generateContentPreview(maxLength: Int = 150) -> String {
        let entries = sortedEntries.prefix(3)
        let combinedText = entries.map { $0.originalText ?? "" }.joined(separator: " ")

        if combinedText.count <= maxLength {
            return combinedText
        } else {
            let truncated = String(combinedText.prefix(maxLength))
            return truncated + "..."
        }
    }
    
    /// Updates the session metadata based on current entries
    func updateMetadata() {
        let entries = sortedEntries
        
        // Update entry count
        entryCount = Int32(entries.count)
        
        // Update content preview
        contentPreview = generateContentPreview()
        
        // Update duration (time between first and last entry)
        if let firstEntry = entries.first, let lastEntry = entries.last,
           let firstTimestamp = firstEntry.timestamp, let lastTimestamp = lastEntry.timestamp {
            duration = lastTimestamp.timeIntervalSince(firstTimestamp)
        }
        
        // Note: detectedLanguages and translationLanguages are computed properties now
        
        // Update has translations flag
        hasTranslations = entries.contains { entry in
            guard let translatedText = entry.translatedText else { return false }
            return !translatedText.isEmpty
        }
        
        // Update timestamp
        updatedAt = Date()
    }
    
    /// Generates a meaningful title based on content or timestamp
    func generateTitle() -> String {
        let entries = sortedEntries
        
        // Try to extract meaningful words from the first entry
        if let firstEntry = entries.first, let originalText = firstEntry.originalText {
            let words = originalText.components(separatedBy: .whitespacesAndNewlines)
                .filter { !$0.isEmpty }
                .prefix(4)

            if !words.isEmpty {
                return words.joined(separator: " ")
            }
        }

        // Fallback to timestamp-based title
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        let sessionDate = createdAt ?? Date()
        return "Note \(formatter.string(from: sessionDate))"
    }
    
    /// Returns the category of this session for tab organization
    var sessionCategory: HistorySessionCategory {
        if isFavourite {
            return .favourites
        } else if isSaved {
            return .saved
        } else {
            return .recents
        }
    }
    
    /// Checks if this session should be considered recent (within last 30 days)
    var isRecent: Bool {
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let sessionDate = createdAt ?? Date()
        return sessionDate > thirtyDaysAgo
    }
}

// MARK: - Session Categories

enum HistorySessionCategory: String, CaseIterable {
    case recents = "Recents"
    case favourites = "Favourites"
    case saved = "Saved"
}

// MARK: - Core Data Extensions

extension HistorySession {
    
    /// Creates a new HistorySession with default values
    static func create(in context: NSManagedObjectContext) -> HistorySession {
        let session = HistorySession(context: context)
        session.id = UUID()
        session.createdAt = Date()
        session.updatedAt = Date()
        session.title = ""
        session.contentPreview = ""
        session.duration = 0
        session.entryCount = 0
        session.hasTranslations = false
        session.isFavourite = false
        session.isSaved = false
        session.cardColorIndex = Int16.random(in: 0...5) // For grid view colors
        return session
    }
    
    /// Adds a new entry to this session
    func addEntry(_ entry: HistoryEntry) {
        entry.sessionId = self.id
        updateMetadata()

        // Auto-generate title if empty
        if title?.isEmpty ?? true {
            title = generateTitle()
        }
    }
    
    /// Toggles the favourite status of this session
    func toggleFavourite() {
        isFavourite.toggle()
        updatedAt = Date()
    }
    
    /// Toggles the saved status of this session
    func toggleSaved() {
        isSaved.toggle()
        updatedAt = Date()
    }
    
    /// Returns the total word count for this session
    var wordCount: Int {
        return sortedEntries.reduce(0) { total, entry in
            guard let originalText = entry.originalText else { return total }
            return total + originalText.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count
        }
    }
    
    /// Returns language statistics for this session
    var languageStatistics: [String: Int] {
        var stats: [String: Int] = [:]
        
        for entry in sortedEntries {
            if let language = entry.detectedLanguage {
                stats[language, default: 0] += 1
            }
        }
        
        return stats
    }
    
    /// Computed property for detected languages
    var detectedLanguages: [String] {
        let languages = Set(sortedEntries.compactMap { $0.detectedLanguage })
        return Array(languages)
    }
    
    /// Computed property for translation languages
    var translationLanguages: [String] {
        let languages = Set(sortedEntries.compactMap { $0.targetLanguage })
        return Array(languages)
    }
}