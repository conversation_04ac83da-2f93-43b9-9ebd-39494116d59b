<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23F79" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
  <entity name="HistoryEntry" representedClassName="HistoryEntry" syncable="YES" codeGenerationType="category">
    <attribute name="audioType" optional="YES" attributeType="String"/>
    <attribute name="detectedLanguage" optional="YES" attributeType="String"/>
    <attribute name="emotion" optional="YES" attributeType="String"/>
    <attribute name="id" optional="NO" attributeType="UUID" usesScalarValueType="NO"/>
    <attribute name="isFinal" optional="NO" attributeType="Boolean" usesScalarValueType="YES"/>
    <attribute name="originalText" optional="NO" attributeType="String"/>
    <attribute name="sessionId" optional="NO" attributeType="UUID" usesScalarValueType="NO"/>
    <attribute name="targetLanguage" optional="YES" attributeType="String"/>
    <attribute name="timestamp" optional="NO" attributeType="Date" usesScalarValueType="NO"/>
    <attribute name="translatedText" optional="YES" attributeType="String"/>
  </entity>
  <entity name="HistorySession" representedClassName="HistorySession" syncable="YES" codeGenerationType="category">
    <attribute name="cardColorIndex" optional="NO" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
    <attribute name="category" optional="YES" attributeType="String"/>
    <attribute name="contentPreview" optional="NO" attributeType="String"/>
    <attribute name="createdAt" optional="NO" attributeType="Date" usesScalarValueType="NO"/>
    <attribute name="duration" optional="NO" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
    <attribute name="entryCount" optional="NO" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
    <attribute name="hasBeenViewed" optional="NO" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="hasTranslations" optional="NO" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="id" optional="NO" attributeType="UUID" usesScalarValueType="NO"/>
    <attribute name="isFavourite" optional="NO" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="isSaved" optional="NO" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="tags" optional="YES" attributeType="String"/>
    <attribute name="title" optional="NO" attributeType="String"/>
    <attribute name="updatedAt" optional="NO" attributeType="Date" usesScalarValueType="NO"/>
  </entity>
</model>