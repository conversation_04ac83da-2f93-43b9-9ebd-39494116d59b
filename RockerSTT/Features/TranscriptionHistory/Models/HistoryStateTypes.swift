//
//  HistoryStateTypes.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//

import SwiftUI

// MARK: - History Empty State Types

enum HistoryEmptyStateType: Equatable {
    case noHistory
    case noSaved
    case noFavorites
    case noSearchResults(String)
    case networkError
    case storageError
    case permissionDenied
    
    var title: String {
        switch self {
        case .noHistory:
            return "No Notes Yet"
        case .noSaved:
            return "No Saved Notes"
        case .noFavorites:
            return "No Favorite Notes"
        case .noSearchResults:
            return "No Results Found"
        case .networkError:
            return "Connection Error"
        case .storageError:
            return "Storage Error"
        case .permissionDenied:
            return "Permission Required"
        }
    }
    
    var description: String {
        switch self {
        case .noHistory:
            return "Start recording to create your first note and build your history"
        case .noSaved:
            return "Save notes to access them quickly later"
        case .noFavorites:
            return "Mark notes as favorites to find them easily"
        case .noSearchResults(let query):
            return "No notes match '\(query)'. Try adjusting your search terms or filters."
        case .networkError:
            return "Unable to load your note history. Please check your connection."
        case .storageError:
            return "Unable to access your saved notes. Please try again."
        case .permissionDenied:
            return "Storage access is required to view your note history."
        }
    }
    
    var iconName: String {
        switch self {
        case .noHistory:
            return "waveform"
        case .noSaved:
            return "bookmark"
        case .noFavorites:
            return "heart"
        case .noSearchResults:
            return "magnifyingglass"
        case .networkError:
            return "wifi.exclamationmark"
        case .storageError:
            return "externaldrive.trianglebadge.exclamationmark"
        case .permissionDenied:
            return "lock.shield"
        }
    }
    
    var actionTitle: String? {
        switch self {
        case .noHistory, .noSaved, .noFavorites:
            return "Start Recording"
        case .noSearchResults:
            return "Clear Search"
        case .networkError, .storageError:
            return "Try Again"
        case .permissionDenied:
            return "Open Settings"
        }
    }
    
    var actionIcon: String? {
        switch self {
        case .noHistory, .noSaved, .noFavorites:
            return "mic.fill"
        case .noSearchResults:
            return "xmark.circle"
        case .networkError, .storageError:
            return "arrow.clockwise"
        case .permissionDenied:
            return "gear"
        }
    }
}

// MARK: - History Error Types

enum HistoryError: Error, Equatable {
    case networkUnavailable
    case storageFailure(String)
    case permissionDenied
    case dataCorruption
    case exportFailure(String)
    case unknown(String)
    
    var title: String {
        switch self {
        case .networkUnavailable:
            return "Network Unavailable"
        case .storageFailure:
            return "Storage Error"
        case .permissionDenied:
            return "Permission Denied"
        case .dataCorruption:
            return "Data Corruption"
        case .exportFailure:
            return "Export Failed"
        case .unknown:
            return "Unknown Error"
        }
    }
    
    var description: String {
        switch self {
        case .networkUnavailable:
            return "Unable to connect to the transcription service. Please check your internet connection and try again."
        case .storageFailure(let details):
            return "Failed to access local storage: \(details)"
        case .permissionDenied:
            return "The app doesn't have permission to access the required resources. Please check your settings."
        case .dataCorruption:
            return "Some of your transcription data appears to be corrupted. Please contact support if this persists."
        case .exportFailure(let details):
            return "Failed to export transcriptions: \(details)"
        case .unknown(let details):
            return "An unexpected error occurred: \(details)"
        }
    }
    
    var iconName: String {
        switch self {
        case .networkUnavailable:
            return "wifi.exclamationmark"
        case .storageFailure:
            return "externaldrive.trianglebadge.exclamationmark"
        case .permissionDenied:
            return "lock.shield"
        case .dataCorruption:
            return "exclamationmark.triangle"
        case .exportFailure:
            return "square.and.arrow.up.trianglebadge.exclamationmark"
        case .unknown:
            return "questionmark.circle"
        }
    }
    
    var recoveryAction: String {
        switch self {
        case .networkUnavailable, .storageFailure, .dataCorruption, .exportFailure, .unknown:
            return "Try Again"
        case .permissionDenied:
            return "Open Settings"
        }
    }
}