# Implementation Plan

- [x] 1. Remove unused test playground file

  - Delete MergingTestPlayground.swift file completely
  - Verify project still compiles without errors
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 2. Create dedicated test utility class
- [x] 2.1 Create TranscriptionTestUtility.swift file

  - Create new Swift file for testing utilities
  - Define TranscriptionTestUtility class with proper structure
  - _Requirements: 2.1, 2.2_

- [x] 2.2 Move test methods from SpeechRecognitionViewModel to test utility

  - Extract testTranscriptionFlow() method and move to TranscriptionTestUtility
  - Extract processTestMessagesWithDelay() method and move to TranscriptionTestUtility
  - Update method signatures to work with external class structure
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 2.3 Update TranscriptionView to use new test utility

  - Modify Test button action to use TranscriptionTestUtility instead of viewModel
  - Ensure test functionality still works correctly
  - _Requirements: 2.1, 2.2, 2.4_

- [ ] 3. Remove unused methods from AudioEngineManager
- [x] 3.1 Remove convertBufferToData method

  - Delete the unused convertBufferToData() private method
  - Verify audio processing still works correctly
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 4. Remove complex unused merging logic from WebSocketManager
- [x] 4.1 Remove transcript merging methods

  - Delete mergeTranscript() method and all its dependencies
  - Remove findCharacterOverlap(), findTokenOverlap(), findFuzzyOverlap() methods
  - Remove calculateStringSimilarity(), calculateTokenSimilarity(), calculateSequenceSimilarity() methods
  - Remove levenshteinDistance(), tokenize(), findBestTokenOverlap() methods
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 4.2 Remove supporting data structures

  - Delete OverlapMatch and FuzzyMatch private structs
  - Clean up any remaining references to merging functionality
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 4.3 Remove legacy audio buffering system

  - Remove audioBuffer private property
  - Remove chunkSize constant (32000 value)
  - Update sendAudioData method to work without buffering
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 5. Clean up SpeechRecognitionViewModel
- [x] 5.1 Remove stub methods

  - Delete handleWithTimestamp() method that just returns input unchanged
  - Remove any calls to this method and replace with direct text usage
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 5.2 Remove unused hotwords functionality

  - Remove parseHotwords() method
  - Remove hotwords AppStorage property
  - Remove hotwords references from FunASRConfiguration creation
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 5.3 Remove unused UI properties

  - Remove backgroundColor property from RecognizedLanguage enum
  - Clean up any unused color-related code
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 6. Clean up TranscriptionView
- [x] 6.1 Remove commented-out cursor animation code

  - Delete all commented-out cursor animation blocks
  - Remove cursorVisible state variable
  - Remove startCursorAnimation() commented method
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 6.2 Remove unused UI properties

  - Delete buttonText computed property that's never used
  - Clean up any other unused UI state variables
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 7. Remove commented-out code blocks
- [x] 7.1 Clean up FunASR configuration comments

  - Remove commented-out hotwords property in FunASRConfiguration struct
  - Remove commented-out hotwords CodingKeys
  - Remove commented-out hotwords parameter in createFunASRConfiguration()
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 7.2 Remove other commented-out code blocks

  - Scan all files for large commented-out code blocks
  - Remove obsolete commented implementations
  - Keep only meaningful documentation comments
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 8. Update SettingsView hotwords section
- [x] 8.1 Remove hotwords UI components

  - Remove hotwords TextEditor and related UI elements from SettingsView
  - Remove hotwords section from FunASR settings
  - Update settings footer text to reflect removed functionality
  - _Requirements: 2.1, 2.2, 2.3_

- [-] 9. Final validation and testing
- [x] 9.1 Compile and fix any remaining issues

  - Ensure project compiles without errors or warnings
  - Fix any broken references or imports
  - _Requirements: 1.2, 2.3_

- [ ] 9.2 Test core functionality

  - Verify audio recording starts and stops correctly
  - Test WebSocket connection establishment
  - Confirm transcription display updates properly
  - Test settings navigation and modification
  - Verify test utility functionality works from UI
  - _Requirements: 1.2, 2.3, 4.2, 5.2_

- [ ] 9.3 Performance and cleanup verification
  - Confirm no performance regressions
  - Verify reduced code complexity and improved readability
  - Check that all compilation warnings are eliminated
  - _Requirements: 2.3, 2.4_
