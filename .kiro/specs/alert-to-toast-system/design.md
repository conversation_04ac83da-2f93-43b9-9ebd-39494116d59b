# Design Document

## Overview

The toast notification system will replace the current SwiftUI alert-based error messaging with a modern, non-intrusive notification system. The design focuses on creating reusable toast components that can display different types of messages with appropriate urgency levels, while maintaining accessibility and consistent user experience across the RockerSTT app.

## Architecture

### Component Structure

```
ToastManager (ObservableObject)
├── ToastMessage (Model)
├── ToastView (SwiftUI View)
├── ToastContainer (SwiftUI View)
└── ToastModifier (ViewModifier)
```

### Data Flow

1. Error occurs in ViewModel or other components
2. Error is classified by urgency level
3. ToastManager receives the error and creates ToastMessage
4. ToastContainer displays the toast with appropriate styling
5. Toast auto-dismisses or waits for user interaction based on urgency

## Components and Interfaces

### ToastMessage Model

```swift
struct ToastMessage: Identifiable, Equatable {
    let id = UUID()
    let text: String
    let type: ToastType
    let duration: TimeInterval?
    let timestamp: Date
}

enum ToastType {
    case info
    case success
    case warning
    case error
    case urgent

    var color: Color { /* implementation */ }
    var icon: String { /* implementation */ }
    var autoDismiss: Bool { /* implementation */ }
}
```

### ToastManager

```swift
@MainActor
class ToastManager: ObservableObject {
    @Published var toasts: [ToastMessage] = []

    func show(_ message: String, type: ToastType)
    func showUrgent(_ message: String)
    func showInfo(_ message: String)
    func dismiss(_ toast: ToastMessage)
    func dismissAll()
}
```

### ToastView

```swift
struct ToastView: View {
    let toast: ToastMessage
    let onDismiss: () -> Void

    // Displays individual toast with:
    // - Icon based on type
    // - Message text
    // - Dismiss button for urgent toasts
    // - Appropriate styling and animations
}
```

### ToastContainer

```swift
struct ToastContainer: View {
    @ObservedObject var toastManager: ToastManager

    // Manages multiple toasts:
    // - Stacking behavior
    // - Animation coordination
    // - Auto-dismiss timers
}
```

### ToastModifier

```swift
struct ToastModifier: ViewModifier {
    @StateObject private var toastManager = ToastManager()

    func body(content: Content) -> some View {
        // Adds toast container overlay to any view
    }
}

extension View {
    func withToasts() -> some View {
        modifier(ToastModifier())
    }
}
```

## Data Models

### Error Classification System

```swift
enum ErrorUrgency {
    case low      // URL validation, non-critical settings
    case medium   // Connection issues, recoverable errors
    case high     // Permission failures, core functionality errors

    var toastType: ToastType {
        switch self {
        case .low: return .info
        case .medium: return .warning
        case .high: return .urgent
        }
    }
}

protocol ClassifiableError {
    var urgency: ErrorUrgency { get }
    var userMessage: String { get }
}
```

### Toast Configuration

```swift
struct ToastConfiguration {
    static let defaultDuration: TimeInterval = 4.0
    static let maxVisibleToasts = 3
    static let animationDuration: TimeInterval = 0.3
    static let stackOffset: CGFloat = 8.0
}
```

## Error Handling

### Error Classification Rules

- **Low Urgency**: URL validation errors, non-critical setting validation
- **Medium Urgency**: WebSocket connection issues, temporary service failures
- **High Urgency**: Microphone permission denied, critical system failures

### Fallback Behavior

- If toast system fails, fall back to console logging
- Maintain error state in ViewModel for debugging
- Provide manual error checking methods for critical paths

### Error Recovery

- Automatic retry mechanisms for connection-related errors
- Clear error states when conditions resolve
- Prevent duplicate error messages for the same issue

## Testing Strategy

### Unit Tests

- ToastManager message queuing and dismissal logic
- Error classification accuracy
- Toast duration and auto-dismiss behavior
- Accessibility announcements

### Integration Tests

- Toast display in TranscriptionView and SettingsView
- Error propagation from ViewModels to toast system
- Multiple toast stacking behavior
- Theme adaptation

### UI Tests

- Toast appearance and dismissal animations
- User interaction with dismissible toasts
- VoiceOver navigation and announcements
- Visual regression testing for different toast types

### Manual Testing Scenarios

- Trigger various error conditions to verify classification
- Test with VoiceOver enabled
- Test with reduced motion accessibility setting
- Verify toast behavior during app backgrounding/foregrounding

## Implementation Notes

### SwiftUI Integration

- Use `.overlay()` modifier to position toasts
- Leverage `@StateObject` and `@ObservedObject` for state management
- Implement custom `ViewModifier` for easy integration

### Animation Strategy

- Use `withAnimation()` for smooth transitions
- Implement spring animations for natural feel
- Respect accessibility motion preferences

### Performance Considerations

- Limit maximum number of visible toasts
- Implement efficient toast queuing system
- Use lazy loading for toast content when possible

### Accessibility Implementation

- Use `accessibilityAnnouncement()` for VoiceOver
- Implement proper focus management
- Ensure sufficient color contrast ratios
- Support Dynamic Type for text scaling
