# Requirements Document

## Introduction

This feature enhances the TranscriptionView header in the RockerSTT app by implementing an intelligent auto-folding navigation header that responds to user scroll behavior. The header will automatically hide when users scroll down to maximize content viewing space, and elegantly reappear when users scroll up or need to access navigation controls. The implementation will use modern iOS design patterns including transparency effects and smooth animations to create a polished, native user experience.

## Requirements

### Requirement 1

**User Story:** As a user viewing transcription content, I want the header to automatically hide when I scroll down, so that I can maximize the screen space available for reading transcriptions.

#### Acceptance Criteria

1. WHEN the user scrolls down in the transcription list THEN the header SHALL gradually fade out and slide up
2. WHEN the user scrolls down with fast velocity THEN the header SHALL hide immediately without delay
3. WHEN the transcription content reaches the bottom of the visible area THEN the header SHALL automatically fold regardless of scroll direction
4. WHEN the header is folding THEN the animation SHALL use smooth easing transitions consistent with the existing DesignSystem.animations

### Requirement 2

**User Story:** As a user who has scrolled down and wants to access navigation controls, I want the header to reappear when I scroll up, so that I can easily access settings and other header functions.

#### Acceptance Criteria

1. WHEN the user scrolls up in any amount THEN the header SHALL immediately begin to unfold and become visible
2. WHEN the header is unfolding THEN it SHALL animate smoothly into view using slide and fade effects
3. WHEN the header becomes visible THEN all interactive elements SHALL be immediately accessible
4. WHEN the user stops scrolling up THEN the header SHALL remain visible until the auto-fold timer triggers

### Requirement 3

**User Story:** As a user who has stopped scrolling, I want the header to automatically hide after a few seconds of inactivity, so that the interface stays clean and maximizes content space without manual intervention.

#### Acceptance Criteria

1. WHEN the user stops scrolling and the header is visible THEN a 3-second countdown timer SHALL begin
2. WHEN the 3-second timer expires AND no user interaction occurs THEN the header SHALL automatically fold
3. WHEN the user interacts with the header during the countdown THEN the timer SHALL reset
4. WHEN the user starts scrolling during the countdown THEN the timer SHALL be cancelled and normal scroll behavior SHALL resume

### Requirement 4

**User Story:** As a user, I want the header to have a modern, transparent appearance that doesn't completely block the content underneath, so that the interface feels light and allows me to see transcription content even when the header is visible.

#### Acceptance Criteria

1. WHEN the header is visible THEN it SHALL display with a blur effect background
2. WHEN the header is visible THEN it SHALL have adjustable transparency that allows content to show through
3. WHEN the header transitions between states THEN the blur intensity SHALL smoothly animate
4. WHEN content scrolls behind the header THEN the blur effect SHALL maintain readability of header text and controls
5. WHEN the header is in different states THEN it SHALL maintain visual hierarchy through shadow depth adjustments

### Requirement 5

**User Story:** As a user with accessibility needs, I want the auto-folding header behavior to work seamlessly with VoiceOver and other accessibility features, so that I can navigate the app effectively regardless of my interaction method.

#### Acceptance Criteria

1. WHEN VoiceOver is enabled THEN the header folding behavior SHALL remain functional
2. WHEN the header is folded and VoiceOver focus moves to header elements THEN the header SHALL automatically unfold
3. WHEN using VoiceOver THEN header state changes SHALL be announced appropriately
4. WHEN accessibility settings indicate reduced motion preference THEN animations SHALL be simplified while maintaining functionality
5. WHEN using assistive technologies THEN all header interactive elements SHALL remain accessible in both folded and unfolded states

### Requirement 6

**User Story:** As a user, I want to manually control the header visibility through gestures, so that I have direct control over the interface when automatic behavior doesn't meet my immediate needs.

#### Acceptance Criteria

1. WHEN the user taps on the collapsed header area THEN the header SHALL expand to full visibility
2. WHEN the user performs a pull-down gesture from the top edge THEN the header SHALL reveal with elastic resistance feedback
3. WHEN the pull-down gesture is released before reaching the threshold THEN the header SHALL snap back to its previous state
4. WHEN the pull-down gesture exceeds the threshold THEN the header SHALL remain expanded and reset the auto-fold timer
5. WHEN manual gestures are performed THEN appropriate haptic feedback SHALL be provided using existing HapticPattern system

### Requirement 7

**User Story:** As a user, I want the header appearance to subtly reflect the current app state (recording, connecting, etc.), so that I can quickly understand the system status even when the header is partially visible.

#### Acceptance Criteria

1. WHEN the app is actively recording THEN the header SHALL display a subtle visual indicator (color tint or effect)
2. WHEN the connection state changes THEN the header SHALL reflect the appropriate visual state
3. WHEN transitioning between app states THEN header visual changes SHALL animate smoothly
4. WHEN the header is collapsed THEN state indicators SHALL remain visible in the collapsed portion
5. WHEN multiple states are active THEN the header SHALL prioritize the most critical state indicator
