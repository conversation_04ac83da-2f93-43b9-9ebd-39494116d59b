# Design Document

## Overview

The auto-folding header feature transforms the existing NavigationView toolbar into an intelligent, responsive header that automatically adapts to user scroll behavior. The design leverages SwiftUI's built-in scroll detection capabilities combined with custom state management to create a seamless, iOS-native experience that maximizes content viewing space while maintaining easy access to navigation controls.

The implementation will replace the current static NavigationView toolbar with a custom header component that overlays the content area, using transparency and blur effects to maintain visual hierarchy without completely obscuring the transcription content below.

## Architecture

### Component Structure

```
TranscriptionView
├── AutoFoldingHeaderView (New)
│   ├── HeaderContentView
│   │   ├── Title Component
│   │   ├── Settings Button
│   │   └── State Indicators
│   └── HeaderBackgroundView
│       ├── Blur Effect Layer
│       ├── Gradient Overlay
│       └── Shadow Layer
├── ScrollableContentView (Modified)
│   ├── ScrollViewReader
│   ├── List/EmptyStateView
│   └── Scroll Detection Logic
└── ControlsView (Unchanged)
```

### State Management Architecture

The header behavior will be managed through a dedicated `HeaderState` observable object that coordinates between scroll events, timers, and user interactions:

```swift
@StateObject private var headerState = HeaderState()

class HeaderState: ObservableObject {
    @Published var isVisible: Bool = true
    @Published var opacity: Double = 1.0
    @Published var blurRadius: Double = 10.0
    @Published var yOffset: Double = 0.0

    private var autoFoldTimer: Timer?
    private var scrollVelocityTracker: ScrollVelocityTracker
}
```

## Components and Interfaces

### AutoFoldingHeaderView

**Purpose:** Main container for the auto-folding header functionality
**Responsibilities:**

- Manage header visibility state transitions
- Handle scroll event processing
- Coordinate animations and timing
- Provide accessibility support

**Interface:**

```swift
struct AutoFoldingHeaderView<Content: View>: View {
    @ObservedObject var headerState: HeaderState
    @ObservedObject var viewModel: SpeechRecognitionViewModel
    @Binding var showingSettings: Bool
    @Binding var showingDeveloperDebug: Bool
    let content: () -> Content

    var body: some View
}
```

### HeaderContentView

**Purpose:** Contains the actual header content (title, buttons, indicators)
**Responsibilities:**

- Render header elements with proper styling
- Handle user interactions (taps, long press)
- Display state-specific visual indicators
- Maintain accessibility labels and hints

**Interface:**

```swift
struct HeaderContentView: View {
    @ObservedObject var viewModel: SpeechRecognitionViewModel
    @Binding var showingSettings: Bool
    @Binding var showingDeveloperDebug: Bool
    let isVisible: Bool

    var body: some View
}
```

### HeaderBackgroundView

**Purpose:** Manages the visual background effects (blur, transparency, shadows)
**Responsibilities:**

- Apply dynamic blur effects based on header state
- Render gradient overlays for text readability
- Manage shadow depth for visual hierarchy
- Animate background transitions smoothly

**Interface:**

```swift
struct HeaderBackgroundView: View {
    let opacity: Double
    let blurRadius: Double
    let isRecording: Bool
    let connectionState: ConnectionState

    var body: some View
}
```

### ScrollDetectionModifier

**Purpose:** Custom ViewModifier to detect and process scroll events
**Responsibilities:**

- Track scroll direction and velocity
- Detect when content reaches viewport boundaries
- Trigger header state changes based on scroll behavior
- Debounce rapid scroll events

**Interface:**

```swift
struct ScrollDetectionModifier: ViewModifier {
    @ObservedObject var headerState: HeaderState
    let onScrollChange: (ScrollInfo) -> Void

    func body(content: Content) -> some View
}
```

### HeaderState (Observable Object)

**Purpose:** Centralized state management for header behavior
**Responsibilities:**

- Track current header visibility and animation states
- Manage auto-fold timer lifecycle
- Process scroll events and determine appropriate responses
- Coordinate with accessibility services

**Key Properties:**

```swift
class HeaderState: ObservableObject {
    // Visibility and Animation State
    @Published var isVisible: Bool = true
    @Published var opacity: Double = 1.0
    @Published var blurRadius: Double = 10.0
    @Published var yOffset: Double = 0.0
    @Published var shadowOpacity: Double = 0.1

    // Interaction State
    @Published var isUserInteracting: Bool = false
    @Published var isPinnedByUser: Bool = false

    // Timing and Behavior
    private var autoFoldTimer: Timer?
    private let autoFoldDelay: TimeInterval = 3.0
    private var lastScrollDirection: ScrollDirection = .none
    private var scrollVelocity: Double = 0.0

    // Methods
    func handleScrollEvent(_ scrollInfo: ScrollInfo)
    func startAutoFoldTimer()
    func cancelAutoFoldTimer()
    func foldHeader(animated: Bool = true)
    func unfoldHeader(animated: Bool = true)
    func handleManualInteraction()
}
```

## Data Models

### ScrollInfo Structure

```swift
struct ScrollInfo {
    let direction: ScrollDirection
    let velocity: Double
    let contentOffset: CGPoint
    let contentSize: CGSize
    let visibleBounds: CGRect
    let isAtTop: Bool
    let isAtBottom: Bool
}

enum ScrollDirection {
    case up
    case down
    case none
}
```

### HeaderVisibilityState Enum

```swift
enum HeaderVisibilityState {
    case visible
    case folding
    case folded
    case unfolding

    var isInteractive: Bool {
        switch self {
        case .visible, .unfolding: return true
        case .folding, .folded: return false
        }
    }
}
```

### HeaderAppearanceConfiguration

```swift
struct HeaderAppearanceConfiguration {
    let baseBlurRadius: Double = 10.0
    let maxBlurRadius: Double = 20.0
    let baseOpacity: Double = 0.95
    let foldedOpacity: Double = 0.0
    let shadowRadius: Double = 8.0
    let cornerRadius: Double = 0.0

    // State-specific configurations
    func recordingConfiguration() -> HeaderAppearanceConfiguration
    func connectingConfiguration() -> HeaderAppearanceConfiguration
    func errorConfiguration() -> HeaderAppearanceConfiguration
}
```

## Error Handling

### Scroll Event Processing Errors

**Error Scenarios:**

- Rapid scroll events causing state conflicts
- Timer invalidation race conditions
- Animation interruption during state transitions

**Handling Strategy:**

- Implement debouncing for scroll events with 50ms delay
- Use atomic state updates with proper synchronization
- Graceful animation cancellation and restart mechanisms
- Fallback to immediate state changes if animations fail

### Accessibility Integration Errors

**Error Scenarios:**

- VoiceOver focus conflicts during header transitions
- Reduced motion preference not respected
- Header controls becoming inaccessible

**Handling Strategy:**

- Accessibility-aware state transitions with proper announcements
- Alternative interaction methods when animations are disabled
- Guaranteed header visibility when assistive technologies are active

### Memory and Performance Errors

**Error Scenarios:**

- Timer leaks from improper cleanup
- Excessive animation calculations during rapid scrolling
- Memory pressure from blur effect rendering

**Handling Strategy:**

- Automatic timer cleanup in deinit and view disappearance
- Animation throttling during high-frequency scroll events
- Blur effect optimization with reduced quality during animations

## Testing Strategy

### Unit Testing

**HeaderState Logic Testing:**

- Test auto-fold timer behavior with various delay scenarios
- Verify scroll event processing logic with edge cases
- Test state transition validity and consistency
- Mock timer and animation dependencies for deterministic testing

**Scroll Detection Testing:**

- Test scroll direction and velocity calculation accuracy
- Verify boundary detection (top/bottom of content)
- Test debouncing behavior with rapid scroll events
- Validate scroll event filtering and processing

### Integration Testing

**Header-Content Interaction Testing:**

- Test header behavior with different content sizes
- Verify proper interaction with existing transcription list
- Test header visibility with empty state vs populated content
- Validate proper z-index and overlay behavior

**Accessibility Integration Testing:**

- Test VoiceOver navigation with folded/unfolded states
- Verify reduced motion preference compliance
- Test keyboard navigation and focus management
- Validate accessibility announcements for state changes

### UI Testing

**Animation and Visual Testing:**

- Test smooth transitions between visibility states
- Verify blur effect rendering and performance
- Test header appearance with different app states (recording, connecting)
- Validate proper visual hierarchy and content readability

**Gesture and Interaction Testing:**

- Test manual tap-to-expand functionality
- Verify pull-down gesture behavior and thresholds
- Test haptic feedback integration
- Validate gesture conflicts with existing scroll behavior

### Performance Testing

**Scroll Performance Testing:**

- Measure frame rate during header animations
- Test memory usage with extended scrolling sessions
- Verify smooth performance with large transcription lists
- Test battery impact of continuous blur effects

**Animation Performance Testing:**

- Measure animation completion times and smoothness
- Test performance with multiple simultaneous animations
- Verify proper animation cleanup and resource management
- Test performance impact on older device models

## Implementation Considerations

### SwiftUI Integration Patterns

The implementation will leverage SwiftUI's declarative nature while managing imperative scroll detection:

- Use `@StateObject` for header state management to ensure proper lifecycle
- Implement custom `ViewModifier` for scroll detection to maintain reusability
- Utilize `GeometryReader` for precise scroll position tracking
- Apply `@Environment(\.accessibilityReduceMotion)` for accessibility compliance

### Animation Strategy

All animations will use the existing `DesignSystem.animations` tokens:

- Header fold/unfold: `DesignSystem.animations.standard` (0.3s ease-in-out)
- Blur effect transitions: `DesignSystem.animations.quick` (0.2s ease-out)
- Opacity changes: `DesignSystem.animations.quick` (0.2s ease-out)
- Emergency state changes: Immediate (no animation) for accessibility

### Performance Optimization

- Implement scroll event debouncing to prevent excessive state updates
- Use `LazyVStack` patterns where appropriate to minimize rendering overhead
- Cache blur effect configurations to avoid repeated calculations
- Implement view recycling for header components to reduce memory allocation

### Backward Compatibility

The implementation will maintain full backward compatibility:

- Existing `SettingsButtonWithDebugAccess` functionality preserved
- All current toolbar behaviors maintained as fallback options
- Existing haptic feedback patterns and timing preserved
- Current accessibility features enhanced, not replaced
