# Implementation Plan

Convert the existing history view into a modern floating tab bar design that matches the provided reference image while maintaining the current brand color palette and all existing functionality. Prioritize incremental development with early testing and validation on iPhone 16.

- [x] 1. Create floating tab bar component with brand colors

  - Implement FloatingTabBar view with Persian Purple active state and Orchid inactive states
  - Add proper corner radius (12pt), padding, and shadow styling using brand color system
  - Create FloatingTabButton component with 44x44pt minimum touch targets for iPhone 16
  - Implement smooth tab switching animations (0.3s ease-in-out) with haptic feedback
  - Add accessibility support with proper labels, hints, and VoiceOver announcements
  - _Requirements: 1.1, 1.2, 1.3, 4.1, 4.2, 7.1, 7.3_

- [x] 2. Implement tab bar visibility management system

  - Create TabBarVisibilityManager class to handle intelligent show/hide logic
  - Implement scroll direction detection with proper thresholds (50pt minimum movement)
  - Add 2-second timeout functionality to auto-show hidden tab bar
  - Create tap-to-show gesture handling for when tab bar is hidden
  - Add proper animation timing and respect for reduced motion accessibility settings
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 7.5_

- [x] 3. Create unified grid view for all tab categories

  - Modify existing HistoryGridContent to work as UnifiedHistoryGrid for all tabs
  - Ensure 2-column layout with 16pt spacing optimized for iPhone 16 screen dimensions
  - Implement smooth filtering transitions with staggered card animations (0.05s delays)
  - Add proper bottom padding (120pt) to account for floating tab bar space
  - Maintain all existing card functionality (favorite, save, speak, copy, delete actions)
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.1, 5.2, 8.2_

- [x] 4. Enhance view model with unified filtering logic

  - Extend HistoryListViewModel to create FloatingHistoryViewModel with enhanced filtering
  - Implement filterSessions() method to handle Recent, Favourite, and Saved tab filtering
  - Update filteredSessions data model immediately when tab selection changes
  - Add smooth animation wrapper around filtering operations (0.3s ease-in-out)
  - Ensure proper data sorting (most recent first) for all filtered categories
  - _Requirements: 2.5, 9.1, 9.2, 9.3, 9.4, 9.5_

- [x] 5. Create main floating history view container

  - Implement FloatingHistoryView to replace existing HistoryListView
  - Integrate floating tab bar with unified grid content using ZStack layout
  - Add scroll offset detection using GeometryReader and ScrollViewReader
  - Position floating tab bar at bottom with proper margins (16pt horizontal, 24pt bottom)
  - Maintain existing navigation bar with "All notes" title and search functionality
  - _Requirements: 1.1, 8.1, 8.3, 10.1, 10.4_

- [x] 6. Implement scroll detection and tab bar positioning

  - Create ScrollOffsetReader component to track scroll position changes
  - Add scroll direction detection logic with proper velocity thresholds
  - Implement smooth show/hide animations with Y-offset and opacity transitions
  - Ensure tab bar always shows at top of content and respects safe areas
  - Add proper iPhone 16 safe area handling and notch positioning considerations
  - _Requirements: 3.1, 3.2, 3.3, 8.1, 8.3_

- [x] 7. Add smooth transition animations for content filtering

  - Implement asymmetric transitions for grid items (scale + opacity on insertion/removal)
  - Add staggered entrance animations with proper timing delays between cards
  - Create smooth tab switching animations that respect accessibility settings
  - Implement card entrance animations using existing spring physics (response: 0.55, damping: 0.75)
  - Add micro-animations for immediate visual feedback on tab selection
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 8. Integrate with existing navigation and modal systems

  - Ensure floating tab bar doesn't conflict with navigation bar or toolbar items
  - Handle tab bar visibility when search modal is presented (hide during search)
  - Maintain proper state restoration when returning from detail views
  - Add keyboard handling to adjust tab bar position if necessary
  - Ensure proper modal presentation behavior with floating tab bar
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5_

- [x] 9. Implement comprehensive accessibility support

  - Add VoiceOver support with proper tab change announcements and selection states
  - Ensure hidden tab bar remains accessible through VoiceOver navigation
  - Implement Dynamic Type support with proper text scaling for tab labels
  - Add reduced motion respect for all animations and transitions
  - Create proper accessibility identifiers and labels for all interactive elements
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 10. Optimize performance for iPhone 16 and large datasets

  - Ensure smooth 60fps animations on iPhone 16 hardware specifications
  - Optimize scroll detection to minimize CPU usage during scrolling
  - Implement efficient filtering that doesn't create unnecessary data copies
  - Add proper memory management for timer-based visibility logic
  - Test performance with large session datasets (1000+ items) on iPhone 16
  - _Requirements: 8.2, 8.4, 8.5_

- [x] 11. Create comprehensive unit tests for new components

  - Write tests for FloatingTabBar component with all interaction states
  - Test TabBarVisibilityManager logic including scroll detection and timeout behavior
  - Create tests for unified filtering logic in FloatingHistoryViewModel
  - Add tests for scroll offset detection and animation timing
  - Test accessibility features including VoiceOver announcements and Dynamic Type
  - _Requirements: All requirements - comprehensive testing coverage_

- [x] 12. Implement integration tests with existing history system

  - Test seamless integration with existing HistorySession data models
  - Verify all existing card actions (favorite, save, delete) work correctly with new filtering
  - Test navigation flow from floating history view to detail views and back
  - Verify search functionality integration with floating tab bar visibility
  - Test modal presentation behavior and state restoration
  - _Requirements: 5.3, 5.4, 5.5, 10.1, 10.2, 10.3, 10.4_

- [x] 13. Add UI tests for complete user interaction flows

  - Create UI tests for tab switching with proper content filtering validation
  - Test floating tab bar show/hide behavior during scroll interactions
  - Verify tap-to-show functionality when tab bar is hidden
  - Test accessibility user flows with VoiceOver and Dynamic Type
  - Add performance testing for smooth animations on iPhone 16 simulator
  - _Requirements: 6.1, 6.2, 6.3, 7.1, 7.4, 8.1, 8.5_

- [x] 14. Polish visual design and micro-interactions

  - Fine-tune animation timing and easing curves for optimal feel
  - Adjust shadow and blur effects for floating tab bar visual hierarchy
  - Optimize color opacity values for perfect brand color representation
  - Add subtle haptic feedback for all user interactions with proper timing
  - Ensure pixel-perfect alignment and spacing on iPhone 16 display
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 6.5, 8.1_

- [ ] 15. Validate design consistency with brand guidelines

  - Verify all colors match current brand palette (Persian Purple, Orchid, French Lilac, Amber, Alabaster)
  - Ensure typography follows existing design system hierarchy
  - Validate spacing and layout consistency with 8pt grid system
  - Test dark mode adaptation with proper brand color variants
  - Confirm accessibility compliance with brand accessibility standards
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 7.1, 7.4_
