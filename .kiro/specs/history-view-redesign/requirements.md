# Requirements Document

## Introduction

The History View Redesign feature will transform the existing transcription history interface to match the modern floating tab design shown in the provided reference image. This redesign will implement a floating tab bar with "Recent", "Favourite", and "Saved" categories, using a unified grid view approach with intelligent filtering and enhanced user interaction patterns. The design will maintain the current codebase's brand color palette while adopting the new visual layout and interaction paradigms.

The feature will enhance user experience through improved visual hierarchy, better content organization, and more intuitive navigation patterns while preserving all existing functionality.

## Requirements

### Requirement 1

**User Story:** As a user, I want to see a floating tab bar design so that I can easily switch between Recent, Favourite, and Saved transcriptions with a modern interface.

#### Acceptance Criteria

1. WHEN the history view loads THEN the system SHALL display a floating tab bar with "Recent", "Favourite", and "Saved" tabs
2. WHEN a tab is selected THEN the system SHALL highlight the active tab with the brand color palette
3. WHEN the floating tab bar is displayed THEN the system SHALL position it over the content with appropriate spacing and shadows
4. WHEN the user scrolls the content THEN the system SHALL show/hide the floating tab bar based on scroll direction and user interaction patterns
5. WHEN the tab bar is hidden THEN the system SHALL provide a way for users to bring it back through scroll gestures or tapping

### Requirement 2

**User Story:** As a user, I want all three tab categories to use a unified grid view so that I have a consistent browsing experience across all content types.

#### Acceptance Criteria

1. WHEN any tab is selected THEN the system SHALL display content using a single grid view layout (similar to current favoritesGridView)
2. WHEN switching between tabs THEN the system SHALL filter the sessions using filterSessions() method and update the filteredSessions data model
3. WHEN displaying grid content THEN the system SHALL use the same card design, spacing, and layout for all tabs
4. WHEN the grid is populated THEN the system SHALL maintain consistent card heights and responsive 2-column layout
5. WHEN content is filtered THEN the system SHALL animate the transition smoothly between different filtered states

### Requirement 3

**User Story:** As a user, I want the floating tab bar to intelligently show and hide so that it doesn't obstruct my content viewing while remaining easily accessible.

#### Acceptance Criteria

1. WHEN the user scrolls down THEN the system SHALL hide the floating tab bar with a smooth animation
2. WHEN the user scrolls up THEN the system SHALL show the floating tab bar with a smooth animation
3. WHEN the user reaches the top of the content THEN the system SHALL always show the floating tab bar
4. WHEN the user stops scrolling for 2 seconds THEN the system SHALL show the floating tab bar regardless of scroll direction
5. WHEN the user taps anywhere on the screen while tab bar is hidden THEN the system SHALL show the floating tab bar

### Requirement 4

**User Story:** As a user, I want the new design to follow the current brand color palette so that it feels consistent with the rest of the application.

#### Acceptance Criteria

1. WHEN displaying the floating tab bar THEN the system SHALL use the current brand colors (Persian Purple, Orchid, French Lilac, Amber, Alabaster)
2. WHEN a tab is active THEN the system SHALL use Persian Purple (#3D2593) for highlighting
3. WHEN displaying cards THEN the system SHALL maintain the current adaptive brand color system for backgrounds
4. WHEN showing inactive tabs THEN the system SHALL use Orchid opacity (0.7) for text color
5. WHEN applying shadows and borders THEN the system SHALL use French Lilac and brand-appropriate opacity values

### Requirement 5

**User Story:** As a user, I want the grid cards to maintain all current functionality so that I don't lose any existing features in the redesign.

#### Acceptance Criteria

1. WHEN displaying grid cards THEN the system SHALL preserve all current action buttons (favorite, save, speak, copy, delete)
2. WHEN interacting with cards THEN the system SHALL maintain all current haptic feedback and animations
3. WHEN performing card actions THEN the system SHALL update the appropriate data models and refresh the filtered view
4. WHEN cards are displayed THEN the system SHALL show all current metadata (date, duration, entry count, status indicators)
5. WHEN tapping cards THEN the system SHALL navigate to the detailed session view as currently implemented

### Requirement 6

**User Story:** As a user, I want smooth animations and transitions so that the interface feels polished and responsive.

#### Acceptance Criteria

1. WHEN switching tabs THEN the system SHALL animate the content change with a smooth transition (0.3s ease-in-out)
2. WHEN the floating tab bar appears/disappears THEN the system SHALL use a smooth slide animation with appropriate easing
3. WHEN filtering content THEN the system SHALL animate grid items appearing and disappearing with staggered timing
4. WHEN cards are loaded THEN the system SHALL use the existing card entrance animations with proper timing
5. WHEN the user interacts with the tab bar THEN the system SHALL provide immediate visual feedback with micro-animations

### Requirement 7

**User Story:** As a user, I want the floating tab bar to be accessible so that I can use it with assistive technologies.

#### Acceptance Criteria

1. WHEN using VoiceOver THEN the system SHALL announce tab changes and current selection state
2. WHEN the floating tab bar is hidden THEN the system SHALL still be accessible through VoiceOver navigation
3. WHEN tabs are focused THEN the system SHALL provide clear accessibility labels and hints
4. WHEN using Dynamic Type THEN the system SHALL scale the floating tab bar text appropriately
5. WHEN using reduced motion settings THEN the system SHALL respect accessibility preferences for animations

### Requirement 8

**User Story:** As a user, I want the layout to be optimized for iPhone 16 so that it takes advantage of the screen size and capabilities.

#### Acceptance Criteria

1. WHEN displaying the floating tab bar THEN the system SHALL position it optimally for iPhone 16 screen dimensions
2. WHEN showing grid content THEN the system SHALL use appropriate spacing and sizing for iPhone 16 display
3. WHEN the tab bar is floating THEN the system SHALL account for iPhone 16 safe areas and notch positioning
4. WHEN cards are displayed THEN the system SHALL optimize touch targets for iPhone 16 interaction patterns
5. WHEN animations play THEN the system SHALL be optimized for iPhone 16 performance characteristics

### Requirement 9

**User Story:** As a user, I want the filtering logic to work seamlessly so that I see the correct content for each tab without delays.

#### Acceptance Criteria

1. WHEN "Recent" tab is selected THEN the system SHALL filter sessions to show recent transcriptions using existing logic
2. WHEN "Favourite" tab is selected THEN the system SHALL filter sessions where isFavourite is true
3. WHEN "Saved" tab is selected THEN the system SHALL filter sessions where isSaved is true
4. WHEN filtering occurs THEN the system SHALL update filteredSessions data model immediately
5. WHEN no content matches the filter THEN the system SHALL display appropriate empty state views

### Requirement 10

**User Story:** As a user, I want the floating tab bar design to integrate seamlessly with existing navigation so that it doesn't conflict with other UI elements.

#### Acceptance Criteria

1. WHEN the navigation bar is present THEN the system SHALL position the floating tab bar to avoid conflicts
2. WHEN modals or sheets are presented THEN the system SHALL handle floating tab bar visibility appropriately
3. WHEN the search view is active THEN the system SHALL hide the floating tab bar to avoid interference
4. WHEN returning from detail views THEN the system SHALL restore the floating tab bar to its previous state
5. WHEN keyboard is shown THEN the system SHALL adjust floating tab bar position if necessary
