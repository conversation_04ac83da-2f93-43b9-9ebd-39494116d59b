# Design Document

## Overview

The History View Redesign will transform the existing transcription history interface into a modern, floating tab bar design that matches the provided reference image while maintaining the current brand color palette. The redesign centers around a floating tab bar component that intelligently shows and hides based on user interaction, with all three tabs (Recent, Favourite, Saved) using a unified grid view approach with smart filtering.

The design maintains all existing functionality while providing a more modern and intuitive user experience through improved visual hierarchy, better content organization, and enhanced interaction patterns.

## Architecture

### Core Components

```
HistoryViewRedesign/
├── Views/
│   ├── FloatingHistoryView.swift         # Main redesigned history view
│   ├── FloatingTabBar.swift              # New floating tab bar component
│   ├── UnifiedHistoryGrid.swift          # Single grid view for all tabs
│   └── FloatingTabBarContainer.swift     # Container managing tab bar visibility
├── ViewModels/
│   ├── FloatingHistoryViewModel.swift    # Enhanced view model with filtering
│   └── TabBarVisibilityManager.swift     # Manages show/hide logic
├── Components/
│   ├── FloatingTabButton.swift           # Individual tab button component
│   └── GridFilterTransition.swift        # Animated filtering transitions
└── Extensions/
    ├── ScrollViewExtensions.swift        # Scroll detection utilities
    └── AnimationExtensions.swift         # Custom animation helpers
```

### Integration Points

- **Existing HistoryListView**: Will be replaced by FloatingHistoryView
- **HistoryGridContent**: Will be enhanced and unified for all tabs
- **HistoryListViewModel**: Will be extended with FloatingHistoryViewModel
- **Current Brand Colors**: Will be maintained throughout the redesign
- **Existing Card Components**: Will be preserved with minor adaptations

## Components and Interfaces

### 1. Floating Tab Bar (FloatingTabBar)

**Visual Design:**

- Floating design positioned over content with subtle shadow
- Rounded corners with brand-appropriate radius (12pt)
- Background: Adaptive brand background with blur effect
- Active tab: Persian Purple (#3D2593) background
- Inactive tabs: Orchid (0.7 opacity) text color
- Smooth show/hide animations based on scroll behavior

**Layout Structure:**

```swift
struct FloatingTabBar: View {
    @Binding var selectedTab: HistoryTab
    @Binding var isVisible: Bool
    let onTabSelected: (HistoryTab) -> Void

    private let tabs: [HistoryTab] = [.recents, .favorites, .saved]
    private let cornerRadius: CGFloat = 12
    private let horizontalPadding: CGFloat = 16
    private let verticalPadding: CGFloat = 8

    var body: some View {
        HStack(spacing: 0) {
            ForEach(tabs, id: \.self) { tab in
                FloatingTabButton(
                    tab: tab,
                    isSelected: selectedTab == tab,
                    onTap: {
                        withAnimation(DesignSystem.animations.tabSwitch) {
                            selectedTab = tab
                            onTabSelected(tab)
                        }
                    }
                )
            }
        }
        .padding(.horizontal, horizontalPadding)
        .padding(.vertical, verticalPadding)
        .background(floatingBackground)
        .cornerRadius(cornerRadius)
        .shadow(
            color: DesignSystem.brandColors.persianPurple.opacity(0.15),
            radius: 8,
            x: 0,
            y: 4
        )
        .offset(y: isVisible ? 0 : 100)
        .opacity(isVisible ? 1 : 0)
        .animation(DesignSystem.animations.standard, value: isVisible)
    }

    private var floatingBackground: some View {
        DesignSystem.brandColors.adaptiveBackground
            .opacity(0.95)
            .background(.ultraThinMaterial)
    }
}
```

### 2. Floating Tab Button (FloatingTabButton)

**Design Specifications:**

- Minimum touch target: 44x44pt (iPhone 16 optimized)
- Active state: Persian Purple background with white text
- Inactive state: Transparent background with Orchid text
- Smooth transition animations (0.3s ease-in-out)
- Haptic feedback on selection

**Implementation:**

```swift
struct FloatingTabButton: View {
    let tab: HistoryTab
    let isSelected: Bool
    let onTap: () -> Void

    private let activeBackgroundColor = DesignSystem.brandColors.persianPurple
    private let activeTextColor = DesignSystem.brandColors.alabaster
    private let inactiveTextColor = DesignSystem.brandColors.orchid.opacity(0.7)
    private let cornerRadius: CGFloat = 8
    private let horizontalPadding: CGFloat = 16
    private let verticalPadding: CGFloat = 8

    var body: some View {
        Button(action: {
            HapticFeedbackManager.shared.selectionChanged()
            onTap()
        }) {
            Text(tab.displayName)
                .font(DesignSystem.typography.buttonLabel)
                .fontWeight(isSelected ? .semibold : .medium)
                .foregroundColor(isSelected ? activeTextColor : inactiveTextColor)
                .padding(.horizontal, horizontalPadding)
                .padding(.vertical, verticalPadding)
                .background(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(isSelected ? activeBackgroundColor : Color.clear)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .frame(minWidth: 44, minHeight: 44)
        .animation(DesignSystem.animations.tabSwitch, value: isSelected)
        .accessibilityLabel(tab.displayName)
        .accessibilityHint("Double tap to switch to \(tab.displayName) tab")
        .accessibilityAddTraits(isSelected ? .isSelected : [])
    }
}
```

### 3. Unified History Grid (UnifiedHistoryGrid)

**Design Specifications:**

- Single grid view used for all three tabs (Recent, Favourite, Saved)
- 2-column layout with 16pt spacing (optimized for iPhone 16)
- Animated transitions when filtering between tabs
- Maintains all existing card functionality and design
- Staggered entrance animations for smooth content changes

**Implementation:**

```swift
struct UnifiedHistoryGrid: View {
    let sessions: [HistorySession]
    let selectedTab: HistoryTab
    let onSessionTap: (HistorySession) -> Void
    let onFavoriteToggle: (HistorySession) -> Void
    let onSaveToggle: (HistorySession) -> Void
    let onDelete: (HistorySession) -> Void

    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.spacing.historyGridSpacing),
        GridItem(.flexible(), spacing: DesignSystem.spacing.historyGridSpacing)
    ]

    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: DesignSystem.spacing.historyGridSpacing) {
                ForEach(Array(sessions.enumerated()), id: \.element.id) { index, session in
                    HistoryGridCard(
                        session: session,
                        onTap: { onSessionTap(session) },
                        onFavoriteToggle: { onFavoriteToggle(session) },
                        onSaveToggle: { onSaveToggle(session) },
                        onDelete: { onDelete(session) }
                    )
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                    .animation(
                        DesignSystem.animations.cardEntrance.delay(Double(index) * 0.05),
                        value: selectedTab
                    )
                }
            }
            .padding(.horizontal, DesignSystem.spacing.screenPadding)
            .padding(.top, DesignSystem.spacing.xSmall)
            .padding(.bottom, 120) // Extra space for floating tab bar
        }
    }
}
```

### 4. Tab Bar Visibility Manager (TabBarVisibilityManager)

**Functionality:**

- Tracks scroll direction and velocity
- Implements intelligent show/hide logic
- Handles timeout-based visibility (2-second rule)
- Manages tap-to-show functionality
- Respects accessibility settings

**Implementation:**

```swift
class TabBarVisibilityManager: ObservableObject {
    @Published var isVisible: Bool = true

    private var lastScrollOffset: CGFloat = 0
    private var hideTimer: Timer?
    private let hideThreshold: CGFloat = 50
    private let showTimeout: TimeInterval = 2.0

    func handleScrollChange(_ scrollOffset: CGFloat) {
        let scrollDelta = scrollOffset - lastScrollOffset
        lastScrollOffset = scrollOffset

        // Always show at top
        if scrollOffset <= 0 {
            showTabBar()
            return
        }

        // Hide on scroll down, show on scroll up
        if abs(scrollDelta) > 5 {
            if scrollDelta > 0 && isVisible {
                hideTabBar()
            } else if scrollDelta < 0 && !isVisible {
                showTabBar()
            }
        }

        // Reset timer on scroll
        resetHideTimer()
    }

    func handleTapGesture() {
        if !isVisible {
            showTabBar()
        }
    }

    private func showTabBar() {
        withAnimation(DesignSystem.animations.standard) {
            isVisible = true
        }
        resetHideTimer()
    }

    private func hideTabBar() {
        guard !UIAccessibility.isReduceMotionEnabled else { return }

        withAnimation(DesignSystem.animations.standard) {
            isVisible = false
        }
    }

    private func resetHideTimer() {
        hideTimer?.invalidate()
        hideTimer = Timer.scheduledTimer(withTimeInterval: showTimeout, repeats: false) { _ in
            DispatchQueue.main.async {
                self.showTabBar()
            }
        }
    }
}
```

### 5. Floating History View (FloatingHistoryView)

**Main Container:**

- Replaces existing HistoryListView
- Integrates floating tab bar with content
- Manages scroll detection and tab bar visibility
- Maintains all existing navigation and functionality

**Layout Structure:**

```swift
struct FloatingHistoryView: View {
    @StateObject private var viewModel = FloatingHistoryViewModel()
    @StateObject private var visibilityManager = TabBarVisibilityManager()
    @State private var showingSearchView = false

    var body: some View {
        NavigationView {
            ZStack(alignment: .bottom) {
                // Background
                DesignSystem.brandColors.adaptiveBackgroundGradient
                    .ignoresSafeArea()

                // Main content
                GeometryReader { geometry in
                    ScrollViewReader { proxy in
                        UnifiedHistoryGrid(
                            sessions: viewModel.filteredSessions,
                            selectedTab: viewModel.selectedTab,
                            onSessionTap: { session in
                                // Navigate to detail view
                            },
                            onFavoriteToggle: { session in
                                viewModel.toggleFavorite(for: session)
                            },
                            onSaveToggle: { session in
                                viewModel.toggleSaved(for: session)
                            },
                            onDelete: { session in
                                viewModel.deleteSession(session)
                            }
                        )
                        .background(
                            ScrollOffsetReader { offset in
                                visibilityManager.handleScrollChange(offset)
                            }
                        )
                    }
                }
                .onTapGesture {
                    visibilityManager.handleTapGesture()
                }

                // Floating tab bar
                FloatingTabBar(
                    selectedTab: $viewModel.selectedTab,
                    isVisible: $visibilityManager.isVisible,
                    onTabSelected: { tab in
                        viewModel.filterSessions(for: tab)
                    }
                )
                .padding(.horizontal, DesignSystem.spacing.screenPadding)
                .padding(.bottom, DesignSystem.spacing.medium)
            }
            .navigationTitle("All notes")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbarBackground(DesignSystem.brandColors.persianPurple, for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingSearchView = true
                    }) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 18, weight: .medium))
                    }
                }
            }
            .sheet(isPresented: $showingSearchView) {
                HistorySearchView()
            }
        }
        .onAppear {
            viewModel.loadSessions()
        }
    }
}
```

## Data Models and Filtering

### Enhanced View Model

The `FloatingHistoryViewModel` extends the existing `HistoryListViewModel` with enhanced filtering capabilities:

```swift
class FloatingHistoryViewModel: HistoryListViewModel {

    override func filterSessions(for tab: HistoryTab? = nil) {
        let targetTab = tab ?? selectedTab

        withAnimation(DesignSystem.animations.standard) {
            switch targetTab {
            case .recents:
                filteredSessions = allSessions
                    .sorted { $0.createdAt ?? Date() > $1.createdAt ?? Date() }
            case .favorites:
                filteredSessions = allSessions
                    .filter { $0.isFavourite }
                    .sorted { $0.createdAt ?? Date() > $1.createdAt ?? Date() }
            case .saved:
                filteredSessions = allSessions
                    .filter { $0.isSaved }
                    .sorted { $0.createdAt ?? Date() > $1.createdAt ?? Date() }
            }
        }

        selectedTab = targetTab
    }
}
```

## Visual Design Specifications

### Color Palette (Maintaining Current Brand)

- **Floating Tab Bar Background**: Adaptive brand background with 95% opacity + ultra thin material
- **Active Tab**: Persian Purple (#3D2593) background with Alabaster (#FFFFFF) text
- **Inactive Tabs**: Orchid (#8C43D0) at 70% opacity for text
- **Tab Bar Shadow**: Persian Purple at 15% opacity, 8pt radius, 4pt Y offset
- **Grid Cards**: Maintain existing adaptive brand color system

### Typography

- **Tab Labels**: Button label font (17pt semibold for active, medium for inactive)
- **Navigation Title**: "All notes" using navigation title font (20pt semibold)
- **Card Content**: Maintains existing typography hierarchy

### Layout Specifications

- **Floating Tab Bar**:

  - Corner radius: 12pt
  - Horizontal padding: 16pt
  - Vertical padding: 8pt
  - Bottom margin: 24pt from safe area
  - Horizontal margin: 16pt from screen edges

- **Tab Buttons**:

  - Minimum touch target: 44x44pt
  - Internal padding: 16pt horizontal, 8pt vertical
  - Corner radius: 8pt
  - Spacing between buttons: 0pt (seamless design)

- **Grid Layout**:
  - 2 columns with 16pt spacing
  - Screen padding: 16pt horizontal
  - Top padding: 8pt
  - Bottom padding: 120pt (space for floating tab bar)

### Animation Specifications

- **Tab Switching**: 0.3s ease-in-out (matches existing standard animation)
- **Tab Bar Show/Hide**: 0.3s ease-in-out with Y-offset and opacity changes
- **Content Filtering**: Staggered card animations with 0.05s delay between items
- **Card Entrance**: Spring animation (response: 0.55, damping: 0.75)
- **Button Press**: 0.12s ease-out for immediate feedback

## Accessibility Considerations

### VoiceOver Support

- Tab buttons announce current selection state
- Hidden tab bar remains accessible through VoiceOver navigation
- Clear accessibility labels and hints for all interactive elements
- Proper accessibility traits for buttons and selected states

### Dynamic Type Support

- All text scales appropriately with user's preferred text size
- Tab bar maintains minimum touch targets even with larger text
- Grid layout adapts to accommodate larger text in cards

### Reduced Motion

- Respects system reduce motion settings
- Tab bar remains visible when reduce motion is enabled
- Simplified animations that don't cause motion sickness

### High Contrast Support

- Colors maintain sufficient contrast ratios in high contrast mode
- Border and shadow adjustments for better visibility
- Text remains readable across all accessibility settings

## Performance Considerations

### Scroll Performance

- Efficient scroll offset tracking with minimal CPU usage
- Debounced visibility changes to prevent excessive animations
- Optimized grid rendering with LazyVGrid for large datasets

### Memory Management

- Reuses existing session data models without duplication
- Efficient filtering that doesn't create unnecessary copies
- Proper cleanup of timers and observers

### Animation Performance

- Hardware-accelerated animations using SwiftUI's built-in system
- Staggered animations that don't overwhelm the GPU
- Respects device capabilities and battery optimization

## Integration Strategy

### Phase 1: Core Implementation

1. Create floating tab bar component
2. Implement visibility management system
3. Adapt existing grid view for unified use

### Phase 2: Enhanced Functionality

1. Add scroll detection and smart hiding
2. Implement smooth filtering transitions
3. Integrate with existing navigation

### Phase 3: Polish and Optimization

1. Fine-tune animations and timing
2. Optimize performance for large datasets
3. Complete accessibility implementation

This design maintains all existing functionality while providing a modern, intuitive interface that matches the reference image and enhances the user experience through thoughtful interaction design and visual hierarchy.
