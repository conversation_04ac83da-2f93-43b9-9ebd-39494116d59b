# Design Document

## Overview

The Smart Word Merging feature addresses the issue of word splitting in online ASR partial results by implementing intelligent word boundary detection and merging. The system analyzes consecutive partial transcription results and merges split words when appropriate, creating a more natural reading experience while maintaining transcription accuracy.

The feature integrates seamlessly with the existing WebSocket-based transcription pipeline, operating between the raw ASR response processing and the UI display logic. It uses iOS's built-in UITextChecker for English word validation and implements language-aware processing to handle multilingual scenarios appropriately.

## Architecture

### High-Level Flow

```
WebSocket Response → Parse Response → Smart Word Merging → Update UI
                                           ↓
                                    Dictionary Lookup
                                           ↓
                                    Cache Management
```

### Integration Points

The Smart Word Merging feature integrates with existing components:

1. **SpeechRecognitionViewModel**: Extends the `addTranscriptionEntry` method to include word merging logic
2. **WebSocketManager**: No changes required - continues to deliver raw transcription responses
3. **TranscriptionEntry**: No structural changes - the merging happens before entry creation/update

### Component Responsibilities

- **SmartWordMerger**: Core merging logic and dictionary validation
- **WordMergingCache**: Performance optimization through result caching
- **LanguageDetector**: Language-aware processing decisions
- **SpeechRecognitionViewModel**: Integration and state management

## Components and Interfaces

### SmartWordMerger

The core component responsible for word merging decisions:

```swift
class SmartWordMerger {
    func shouldMergeWords(previousToken: String, currentToken: String, language: RecognizedLanguage) -> Bool
    func mergePartialResults(previousPartial: String?, currentPartial: String, language: RecognizedLanguage) -> String
    private func isValidEnglishWord(_ word: String) -> Bool
    private func extractFirstToken(from text: String) -> String
    private func extractLastToken(from text: String) -> String
}
```

**Key Methods:**

- `shouldMergeWords`: Determines if two tokens should be merged based on dictionary lookup
- `mergePartialResults`: Main entry point that handles the complete merging logic
- `isValidEnglishWord`: Uses UITextChecker for English word validation
- Token extraction methods: Handle text parsing and cleanup

### WordMergingCache

Performance optimization component to avoid redundant dictionary lookups:

```swift
class WordMergingCache {
    private var cache: [String: Bool] = [:]
    private let maxCacheSize: Int = 1000

    func getCachedResult(for combinedWord: String) -> Bool?
    func setCachedResult(for combinedWord: String, isValid: Bool)
    func clearCache()
    private func evictOldestEntries()
}
```

**Features:**

- LRU-style cache management
- Configurable size limits
- Thread-safe operations
- Memory pressure handling

### LanguageDetector

Language-aware processing to determine when merging should be applied:

```swift
class LanguageDetector {
    func shouldApplyMerging(for language: RecognizedLanguage) -> Bool
    func detectLanguageFromText(_ text: String) -> RecognizedLanguage
    private func isSpaceBasedLanguage(_ language: RecognizedLanguage) -> Bool
}
```

**Logic:**

- English: Apply merging (space-based language with splitting issues)
- Chinese/Cantonese: Skip merging (no spaces, no splitting issues)
- Unknown/Mixed: Default to applying merging for safety
- Japanese: Apply merging (space-based in romanized form)

### Enhanced SpeechRecognitionViewModel

Extensions to existing ViewModel for integration:

```swift
extension SpeechRecognitionViewModel {
    private let smartWordMerger = SmartWordMerger()
    private var previousPartialResult: String?

    private func processPartialResultWithMerging(_ response: TranscriptionResponse) -> String
    private func updatePreviousPartialResult(_ result: String, isFinal: Bool)
}
```

## Data Models

### MergeResult

Encapsulates the result of a word merging operation:

```swift
struct MergeResult {
    let mergedText: String
    let wasMerged: Bool
    let confidence: Float
    let processingTime: TimeInterval
}
```

### MergingConfiguration

Configuration options for the merging behavior:

```swift
struct MergingConfiguration {
    let enableCaching: Bool = true
    let maxCacheSize: Int = 1000
    let dictionaryTimeout: TimeInterval = 0.01 // 10ms
    let enableLanguageDetection: Bool = true
    let fallbackToSpacing: Bool = true
}
```

## Error Handling

### Graceful Degradation

The system implements multiple fallback layers:

1. **Dictionary Lookup Failure**: Default to adding spaces between tokens
2. **Performance Issues**: Skip merging for tokens exceeding length limits
3. **Language Detection Failure**: Default to applying merging logic
4. **Cache Overflow**: Clear cache and continue operation

### Error Recovery

```swift
enum WordMergingError: Error {
    case dictionaryUnavailable
    case processingTimeout
    case invalidInput
    case cacheError
}

extension SmartWordMerger {
    private func handleMergingError(_ error: WordMergingError, fallbackBehavior: FallbackBehavior) -> String
}
```

### Performance Safeguards

- **Timeout Protection**: Dictionary lookups limited to 10ms
- **Input Validation**: Token length limits to prevent performance issues
- **Memory Management**: Cache size limits and cleanup
- **Thread Safety**: Non-blocking operations on main thread

## Testing Strategy

### Unit Testing

**SmartWordMerger Tests:**

- Valid word combinations (e.g., "restr" + "icted" → "restricted")
- Invalid combinations (e.g., "hello" + "world" → "hello world")
- Edge cases: empty strings, punctuation, numbers
- Performance benchmarks for dictionary lookups

**WordMergingCache Tests:**

- Cache hit/miss scenarios
- Cache eviction behavior
- Memory usage validation
- Thread safety verification

**LanguageDetector Tests:**

- Language-specific merging decisions
- Mixed language content handling
- Unknown language fallback behavior

### Integration Testing

**SpeechRecognitionViewModel Integration:**

- End-to-end partial result processing
- State management during merging
- Error handling and fallback behavior
- Performance impact on transcription flow

**Real-world Scenario Testing:**

- Common word splitting patterns
- Multi-language code-switching
- Network interruption handling
- Long transcription sessions

### Performance Testing

**Benchmarks:**

- Dictionary lookup response times
- Cache performance under load
- Memory usage patterns
- UI responsiveness during processing

**Load Testing:**

- High-frequency partial results
- Large vocabulary processing
- Extended session duration
- Memory pressure scenarios

## Implementation Considerations

### Thread Safety

- Dictionary lookups performed on background queue
- Cache operations use concurrent queues with barriers
- UI updates remain on main thread
- State synchronization through proper queuing

### Memory Management

- Weak references to prevent retain cycles
- Cache size monitoring and cleanup
- Automatic cache clearing under memory pressure
- Efficient string handling to minimize allocations

### Performance Optimization

- Lazy initialization of components
- Batch processing for multiple partial results
- Intelligent cache warming for common words
- Background processing to avoid UI blocking

### Localization Support

- UITextChecker respects system language settings
- Configurable language-specific behavior
- Support for regional dictionary variations
- Extensible architecture for additional languages

## Future Enhancements

### Advanced Language Detection

- Real-time language switching detection
- Confidence scoring for language identification
- Context-aware language hints
- Machine learning-based language classification

### Enhanced Dictionary Support

- Custom domain-specific dictionaries
- User vocabulary learning
- Contextual word validation
- Phonetic similarity matching

### Performance Improvements

- Predictive caching based on usage patterns
- Parallel processing for multiple token combinations
- Hardware-accelerated text processing
- Adaptive timeout based on device performance
