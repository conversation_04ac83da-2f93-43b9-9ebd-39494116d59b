# Requirements Document

## Introduction

This feature implements intelligent word merging for online ASR (Automatic Speech Recognition) partial results to prevent word splitting artifacts that occur during real-time transcription. The system will analyze consecutive partial results and merge split words when appropriate, creating a more natural and readable transcription experience for users while maintaining accuracy.

## Requirements

### Requirement 1

**User Story:** As a user of the speech-to-text app, I want partial transcription results to display complete words without artificial splits, so that I can read the transcription naturally as it appears in real-time.

#### Acceptance Criteria

1. WHEN a new partial ASR result is received THEN the system SHALL analyze the last token of the previous partial and the first token of the current partial
2. WH<PERSON> the combination of last token + first token forms a valid English word THEN the system SHALL merge them without adding a space
3. WHEN the combination does not form a valid English word THEN the system SHALL insert a space between the tokens
4. WHEN no previous partial exists THEN the system SHALL display the current partial as-is

### Requirement 2

**User Story:** As a user, I want the word merging to work efficiently without causing delays in the transcription display, so that the real-time experience remains smooth and responsive.

#### Acceptance Criteria

1. WH<PERSON> performing dictionary lookups THEN the system SHALL complete the validation within 10ms per lookup
2. <PERSON><PERSON><PERSON> the same word combination is checked multiple times THEN the system SHALL use cached results to avoid redundant lookups
3. WHEN processing partial results THEN the system SHALL not block the UI thread
4. WHEN memory usage exceeds reasonable limits THEN the system SHALL clear the cache and continue operation
5. WHEN running unit tests THEN the system SHALL use iPhone 16 simulator for testing

### Requirement 3

**User Story:** As a user who speaks multiple languages, I want the word merging to be language-aware, so that it only applies to languages where word splitting is problematic (like English) and doesn't interfere with languages that don't use spaces (like Chinese).

#### Acceptance Criteria

1. WHEN the detected language is English THEN the system SHALL apply word merging logic
2. WHEN the detected language is Cantonese or Mandarin THEN the system SHALL skip word merging and display results as-is
3. WHEN language detection is uncertain THEN the system SHALL default to applying word merging for safety
4. WHEN code-switching occurs within a sentence THEN the system SHALL apply merging based on the current segment's detected language

### Requirement 4

**User Story:** As a user, I want the final offline transcription result to replace the merged online result, so that I get the most accurate transcription possible while still having a good real-time experience.

#### Acceptance Criteria

1. WHEN an offline/final transcription result is received THEN the system SHALL replace the corresponding online-merged result
2. WHEN replacing results THEN the system SHALL maintain proper UI state and user context
3. WHEN offline results are delayed THEN the system SHALL continue showing the merged online results until replacement occurs
4. WHEN offline results fail to arrive THEN the system SHALL retain the merged online results as the final transcription

### Requirement 5

**User Story:** As a developer maintaining the app, I want the word merging feature to integrate cleanly with existing components, so that it doesn't disrupt current functionality or create maintenance overhead.

#### Acceptance Criteria

1. WHEN integrating with WebSocketManager THEN the system SHALL not modify existing WebSocket handling logic
2. WHEN integrating with SpeechRecognitionViewModel THEN the system SHALL extend functionality without breaking existing transcription flows
3. WHEN errors occur in word merging THEN the system SHALL gracefully fall back to standard space-separated display
4. WHEN the feature is disabled THEN the system SHALL revert to original behavior without side effects
5. WHEN running unit tests THEN the system SHALL use iPhone 16 simulator for testing

### Requirement 6

**User Story:** As a user, I want the word merging to handle edge cases gracefully, so that unusual input doesn't cause the app to malfunction or display incorrect results.

#### Acceptance Criteria

1. WHEN partial results contain only punctuation or numbers THEN the system SHALL handle them appropriately without attempting word merging
2. WHEN partial results are empty or null THEN the system SHALL continue normal operation without errors
3. WHEN dictionary lookup fails or is unavailable THEN the system SHALL default to adding spaces between tokens
4. WHEN extremely long tokens are encountered THEN the system SHALL limit processing to prevent performance issues
5. WHEN running unit tests THEN the system SHALL use iPhone 16 simulator for testing
