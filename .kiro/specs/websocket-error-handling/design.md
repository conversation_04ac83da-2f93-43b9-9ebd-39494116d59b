# Design Document

## Overview

This design addresses the WebSocket error handling issues by introducing a disconnection reason tracking system and reorganizing error handling logic. The solution distinguishes between user-initiated disconnections (like stopping recording) and system-initiated disconnections (like network failures), ensuring appropriate error messages and reconnection behavior for each scenario.

## Architecture

### Current Issues Analysis

1. **Indiscriminate Reconnection**: The current `attemptReconnection()` method is called for all disconnections, including user-initiated ones
2. **Generic Error Handling**: All WebSocket errors are treated the same way regardless of their cause
3. **Confusing UI States**: Users see "reconnecting" messages when they intentionally stop recording
4. **Missing Context**: The system doesn't track why a disconnection occurred

### Proposed Solution Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    WebSocket Manager                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │ Disconnection   │    │     Error Classification        │ │
│  │ Reason Tracker  │    │                                 │ │
│  │                 │    │  • User Initiated               │ │
│  │ • User Intent   │    │  • Network Error                │ │
│  │ • System Error  │    │  • Server Error                 │ │
│  │ • Network Issue │    │  • Protocol Error               │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            Smart Reconnection Logic                     │ │
│  │                                                         │ │
│  │  • Only reconnect for system/network errors            │ │
│  │  • Skip reconnection for user-initiated disconnections │ │
│  │  • Exponential backoff for genuine failures            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Components and Interfaces

### 1. Disconnection Reason Enum

```swift
enum DisconnectionReason {
    case userInitiated          // User stopped recording
    case networkError          // Network connectivity issues
    case serverError           // Server-side problems
    case connectionTimeout     // Connection establishment timeout
    case protocolError         // WebSocket protocol violations
    case unknown              // Fallback for unclassified errors
}
```

### 2. Enhanced WebSocket Error Types

```swift
enum WebSocketError: LocalizedError {
    case invalidURL
    case connectionFailed(reason: String)
    case connectionTimeout
    case networkUnavailable
    case serverUnreachable
    case protocolError(String)
    case maxReconnectAttemptsReached
    case userCancelled

    var shouldAttemptReconnection: Bool {
        switch self {
        case .userCancelled:
            return false
        case .invalidURL:
            return false
        case .maxReconnectAttemptsReached:
            return false
        default:
            return true
        }
    }
}
```

### 3. Enhanced WebSocket Manager Interface

```swift
class WebSocketManager {
    // New properties
    private var disconnectionReason: DisconnectionReason = .unknown
    private var isUserInitiatedDisconnection = false

    // Enhanced methods
    func disconnect(reason: DisconnectionReason = .userInitiated)
    func forceDisconnect() // For system errors
    private func handleDisconnection(closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?)
    private func shouldAttemptReconnection(for reason: DisconnectionReason) -> Bool
}
```

### 4. Error Message Strategy

Different error types will have specific user-facing messages:

- **User Initiated**: No error message, clean disconnection
- **Network Error**: "Connection lost. Attempting to reconnect..."
- **Server Error**: "Server unavailable. Retrying connection..."
- **Timeout**: "Connection timeout. Check your network and try again."
- **Protocol Error**: "Communication error. Please restart the connection."

## Data Models

### Enhanced Connection State

```swift
enum WebSocketConnectionState {
    case disconnected(reason: DisconnectionReason?)
    case connecting
    case connected
    case reconnecting(attempt: Int, maxAttempts: Int)

    var shouldShowError: Bool {
        switch self {
        case .disconnected(let reason):
            return reason != .userInitiated && reason != nil
        case .reconnecting:
            return false // Show status, not error
        default:
            return false
        }
    }
}
```

### Error Context Model

```swift
struct WebSocketErrorContext {
    let error: WebSocketError
    let timestamp: Date
    let connectionAttempt: Int
    let shouldRetry: Bool
    let userMessage: String
}
```

## Error Handling

### 1. Disconnection Flow

```mermaid
flowchart TD
    A[Disconnection Event] --> B{Check Disconnection Reason}
    B -->|User Initiated| C[Clean Disconnect]
    B -->|System Error| D[Classify Error Type]
    B -->|Network Issue| D

    C --> E[Update State to Disconnected]
    E --> F[No Error Message]

    D --> G{Should Reconnect?}
    G -->|Yes| H[Start Reconnection Logic]
    G -->|No| I[Show Error Message]

    H --> J[Update State to Reconnecting]
    J --> K[Attempt Reconnection]

    I --> L[Update State to Disconnected with Error]
```

### 2. Error Classification Logic

The system will classify errors based on:

- **Close Codes**: URLSessionWebSocketTask.CloseCode values
- **Error Types**: NSError domain and codes
- **Context**: Whether disconnect was called by user or system

### 3. Reconnection Strategy

- **User Initiated**: No reconnection
- **Network Errors**: Exponential backoff (2s, 4s, 8s, 16s, 32s)
- **Server Errors**: Linear backoff (5s intervals)
- **Protocol Errors**: Single retry after 3s delay

## Testing Strategy

### Unit Tests

1. **Disconnection Reason Tracking**

   - Test user-initiated disconnection doesn't trigger reconnection
   - Test system errors trigger appropriate reconnection
   - Test error classification accuracy

2. **Error Message Generation**

   - Test correct messages for each error type
   - Test no messages for user-initiated disconnections
   - Test error context creation

3. **Reconnection Logic**
   - Test reconnection attempts for appropriate errors
   - Test no reconnection for user-initiated disconnections
   - Test exponential backoff timing
   - Test maximum attempt limits

### Integration Tests

1. **End-to-End Scenarios**

   - Test normal recording start/stop cycle
   - Test network interruption during recording
   - Test server unavailability scenarios
   - Test connection timeout scenarios

2. **UI State Verification**
   - Test connection state updates
   - Test error message display
   - Test reconnection status display

### Manual Testing Scenarios

1. **User Experience Testing**
   - Start and stop recording multiple times
   - Disconnect network during recording
   - Stop server during recording
   - Test with slow/unstable network

## Implementation Notes

### Backward Compatibility

The changes maintain the existing WebSocketManagerDelegate interface while enhancing internal error handling. Existing delegate methods will receive more contextual information.

### Performance Considerations

- Error classification adds minimal overhead
- Reconnection logic includes circuit breaker pattern to prevent excessive attempts
- Connection state updates are batched to avoid UI thrashing

### Security Considerations

- Error messages don't expose sensitive server information
- Connection attempts include rate limiting
- Invalid URLs are rejected early in the process

## Migration Strategy

1. **Phase 1**: Add disconnection reason tracking
2. **Phase 2**: Implement enhanced error classification
3. **Phase 3**: Update reconnection logic
4. **Phase 4**: Enhance UI error display
5. **Phase 5**: Add comprehensive testing

This phased approach ensures the system remains functional throughout the implementation process.
